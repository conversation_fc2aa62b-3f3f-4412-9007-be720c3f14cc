BUILDER=${PWD}/builder
OUTDIR=${PWD}/builder/_output
tag=$(shell git describe --abbrev=0 --tags)

all: vet gen starship example

clean: ## make clean
	rm -rf ${OUTDIR}

vet:
	@echo "run go vet ..."
	@go vet ./server...

starship: ## starship server binary
	@echo "build starship server"
	@GOOS=linux GOARCH=amd64 go build -o ${OUTDIR}/starship ./server

starship-client: ## starship client binary
	@echo "build starship client"
	@GOOS=linux GOARCH=amd64 go build -o ${OUTDIR}/client ./client

gen: ## generate proto
	@echo "generate proto"
	@protoc --go_out=. --go_opt=paths=source_relative \
         --go-grpc_out=. --go-grpc_opt=paths=source_relative \
         pb/starship.proto --experimental_allow_proto3_optional
	@protoc --go_out=. --go_opt=paths=source_relative \
         --go-grpc_out=. --go-grpc_opt=paths=source_relative \
         pb/tp_rpc.proto --experimental_allow_proto3_optional

image: starship starship-client
	docker build -t ccr.ccs.tencentyun.com/kmetis/starship:${tag} -f ${BUILDER}/Dockerfile ${BUILDER}

push: image
	docker push ccr.ccs.tencentyun.com/kmetis/starship:${tag}


.PHONY: help
help:
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "\033[36m%-30s\033[0m %s\n", $$1, $$2}'