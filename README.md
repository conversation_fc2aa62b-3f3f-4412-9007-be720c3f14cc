# starship云原生发布系统（星舰）

## 背景

随着线上TKE集群规模越来越大，集群中组件的发布也面临越来越大的挑战，当前问题主要集中在如下几个方面:

### 发布效率瓶颈

#### 大规模挑战

TKE集群数量庞大（上万集群），eklet等核心组件全量发布耗时长达2个月，无法满足业务快速迭代需求

#### 组件部署在用户环境不可控
coredns/kube-proxy等组件部署在用户集群侧，环境不可控，资源不足和用户自己修改过节点等参数异常情况多，需要对用户进行周知，并且需要用户配合调整，非标环境变更可能会引发集群内业务数据面中断，同时缺少标准化变更能力

### 变更灰度、预检和后检能力不足

#### 安全性不足

变更过程无强制按地域灰度、预检/后检，人工介入多，无法保障大规模变更的稳定性

### 产品化能力不足

#### 白屏化覆盖低

关键组件依赖命令行工具后台运维和研发升级，无法通过控制台统一管理，用户体验差，无法自闭环组件升级操作

## 目标

### 效率目标
对于 eklet、apiserver、kcm、scheduler、ccm 等通用核心组件，将全量发布周期从 2 个月缩短至 3 周。针对用户集群coredns、kube-proxy 这类对集群稳定性影响大的网络组件，构建完善的通知体系，降低通知和用户确认成本。

### 质量目标
通过强制灰度、预检 / 后检机制，保障超大规模组件变更稳定性，减少人工操作失误，平台零变更故障。支持业务快速收敛致命隐患，完成组件版本的收敛，提升组件的稳定性，降低相关工单数。

### 变更产品化能力追赶并超越友商
将核心组件发布全面白屏化，用户可在产品控制台完成安全的升级操作（预检、变更、后检、回滚等)，组件可基于用户自定义维护时间窗口，自动化升级。



## 技术方案

https://iwiki.woa.com/p/**********

## 整体架构

参考最新技术方案文档

![整体架构](./doc/images/architecture.png)



## 核心接口

```
  // IsComponentHealthy Check if component is healthy
  rpc IsComponentHealthy (ComponentHealthyRequest) returns (ComponentHealthyReply) {}

  // IsNodeHealthy Check if node is healthy
  rpc IsNodeHealthy (NodeHealthyRequest) returns (NodeHealthyReply) {}

  // CreateDryRunTask Creates dry run task for specified component
  rpc CreateDryRunTask(CreateDryRunTaskRequest) returns (CreateDryRunTaskReply) {}

  // GetDryRunTaskResult get the result of dry run task
  rpc GetDryRunTaskResult(GetDryRunTaskRequest) returns (GetDryRunTaskReply) {}
```
详情参考[proto文件](./pb/starship.proto)

## 构建

* protobuf文件生成
```
make gen
```

* 构建二进制、镜像、推送镜像

````
make
make image
make push

````