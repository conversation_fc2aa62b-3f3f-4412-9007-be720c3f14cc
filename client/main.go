/*
 *
 * Copyright 2015 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

// Package main implements a client for Greeter service.
package main

import (
	"context"
	"crypto/rand"
	"flag"
	"fmt"
	"math/big"
	"strings"
	"time"

	pb "git.woa.com/kmetis/starship/pb"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"k8s.io/klog/v2"
)

var (
	addr      = flag.String("addr", "localhost:50058", "the address to connect to")
	testCase  = flag.String("testCase", "", "Name to test")
	taskid    = flag.String("taskid", "", "task id")
	clusterId = flag.String("clusterId", "", "cluster will be checked")
	nodes     = flag.String("nodes", "np-9ceob7jj-cvbq3,np-9ceob7jj-cvbq4", "nodes will be checked")
)

func main() {
	flag.Parse()
	// Set up a connection to the server.
	conn, err := grpc.NewClient(*addr, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		klog.Fatalf("did not connect: %v", err)
	}
	defer conn.Close()

	// Contact the server and print out its response.
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute)
	defer cancel()

	switch *testCase {
	case "tpcreate":
		// timeout := int32(2)
		c := pb.NewTPTaskEngineClient(conn)

		cls := *clusterId
		if cls == "" {
			cls = "cls-" + GenerateRandomString()
		}
		req := &pb.TPCreateTaskRequest{
			Region:    "gz",
			ClusterId: cls,
			Workload:  "coredns",
			ImageTag:  "v1.6.7",
			// TaskName:      "coredns-precheck",
			// Action:        "precheck",
			// Timeout:       &timeout,
		}
		res, err := c.CreateTask(ctx, req)
		if err != nil {
			klog.Errorf("could not create: %v", err)
			return
		}
		klog.Infof("request is %v,response is %v", req, res)
	case "tpdescribe":
		// timeout := int32(2)
		c := pb.NewTPTaskEngineClient(conn)
		req := &pb.TPDescribeTaskRequest{
			Region: "gz",
			TaskId: *taskid,
		}
		res, err := c.DescribeTask(ctx, req)
		if err != nil {
			klog.Errorf("could not describe: %v", err)
			return
		}
		klog.Infof("request is %v,response is %s %s", req, res.GetStatus(), res.GetReason())

	case "tpcancel":
		// timeout := int32(2)
		c := pb.NewTPTaskEngineClient(conn)
		req := &pb.TPCancelTaskRequest{
			Region: "gz",
			TaskId: *taskid,
		}
		res, err := c.CancelTask(ctx, req)
		if err != nil {
			klog.Errorf("could not cancel: %v", err)
			return
		}
		klog.Infof("request is %v,response is %v", req, res)
	case "create":
		// timeout := int32(2)
		c := pb.NewHealthCheckerClient(conn)
		req := &pb.CreateTaskRequest{
			Region:    "gz",
			ClusterId: "cls-xxxxxxxx",
			// ComponentName: "coredns",
			// TaskName:      "coredns-precheck",
			// Action:        "precheck",
			// Timeout:       &timeout,
		}
		res, err := c.CreateTask(ctx, req)
		if err != nil {
			klog.Errorf("could not create: %v", err)
			return
		}
		klog.Infof("request is %v,response is %v", req, res)
	case "report":
		c := pb.NewHealthCheckerClient(conn)
		req := &pb.ReportTaskResultRequest{
			// Region: "gz",
			// TaskId: *taskid,
			// Result: "hello, check has done",
		}
		res, err := c.ReportTaskResult(ctx, req)
		if err != nil {
			klog.Errorf("could not report: %v", err)
			return
		}
		klog.Infof("request is %v,response is %v", req, res)
	case "cancel":
		c := pb.NewHealthCheckerClient(conn)
		req := &pb.CancelTaskRequest{
			Region: "gz",
			TaskId: *taskid,
		}
		res, err := c.CancelTask(ctx, req)
		if err != nil {
			klog.Errorf("could not cancel: %v", err)
			return
		}
		klog.Infof("request is %v,response is %v", req, res)
	case "describe":
		c := pb.NewHealthCheckerClient(conn)
		req := &pb.DescribeTaskRequest{
			// Region: "gz",
			// TaskId: *taskid,
		}
		res, err := c.DescribeTask(ctx, req)
		if err != nil {
			klog.Errorf("could not describe: %v", err)
			return
		}
		klog.Infof("request is %v,response is %v", req, res)
	default:
		c := pb.NewHealthCheckerClient(conn)
		req := &pb.ComponentHealthyRequest{Product: "tke", ClusterId: *clusterId, ComponentName: "apiserver"}
		res, err := c.IsComponentHealthy(ctx, req)
		if err != nil {
			klog.Errorf("could not greet: %v", err)
		}
		klog.Infof("request is %v,response is %v", req, res)
		req.ClusterId = *clusterId
		res, err = c.IsComponentHealthy(ctx, req)
		if err != nil {
			klog.Errorf("could not greet: %v", err)
		}
		klog.Infof("request is %v,response is %v", req, res)
		req.ClusterId = *clusterId
		req.ComponentName = "etcd"
		res, err = c.IsComponentHealthy(ctx, req)
		if err != nil {
			klog.Errorf("could not greet: %v", err)
		}
		klog.Infof("request is %v,response is %v", req, res)
		req.ClusterId = *clusterId
		res, err = c.IsComponentHealthy(ctx, req)
		if err != nil {
			klog.Errorf("could not greet: %v", err)
		}
		klog.Infof("request is %v,response is %v", req, res)
		klog.Infof("Greeting: %s", res.GetErrMessage())

		ts := time.Now().Add(-30 * time.Second).UnixMilli()
		ns := strings.Split(*nodes, ",")
		req2 := &pb.NodeHealthyRequest{Product: "tke", ClusterId: *clusterId, Nodes: ns, Timestamp: &ts}
		res2, err := c.IsNodeHealthy(ctx, req2)
		if err != nil {
			klog.Errorf("could not greet: %v", err)
		}
		klog.Infof("request is %v,response is %v", req2, res2)
		klog.Infof("Greeting: %s", res2.GetErrMessage())
	}
}

func GenerateRandomString() string {
	const letters = "abcdefghijklmnopqrstuvwxyz0123456789"
	result := make([]byte, 8)
	for i := range result {
		index, err := rand.Int(rand.Reader, big.NewInt(int64(len(letters))))
		if err != nil {
			// 处理错误，这里简单地打印错误并使用默认字符
			fmt.Printf("Error generating random index: %v\n", err)
			result[i] = 'a'
		} else {
			result[i] = letters[index.Int64()]
		}
	}
	return string(result)
}
