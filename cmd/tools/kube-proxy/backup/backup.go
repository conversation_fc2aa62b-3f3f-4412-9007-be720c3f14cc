package backup

import (
	"context"
	encodingjson "encoding/json"
	"fmt"
	"os"

	"git.woa.com/kmetis/starship/cmd/tools/kube-proxy/config"
	"git.woa.com/kmetis/starship/cmd/tools/kube-proxy/ianvs"
	"git.woa.com/kmetis/starship/cmd/tools/kube-proxy/util"

	"github.com/spf13/cobra"
	appv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/serializer/json"
	"k8s.io/apimachinery/pkg/runtime/serializer/yaml"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/scheme"
)

var BackupCmd = &cobra.Command{
	Use:   "backup",
	Short: "Backup kube-proxy configuration",
	Run: func(cmd *cobra.Command, args []string) {
		runBackup(cmd)
	},
}

func runBackup(cmd *cobra.Command) {
	// init ianvs config
	ianvs.InitIanvsClient(config.GetConfig())

	clusterId := cmd.Flags().Lookup("cluster").Value.String()
	clusterFile := cmd.Flags().Lookup("file").Value.String()
	if clusterId == "" && clusterFile == "" {
		fmt.Println("Error: must specify either --cluster or --file")
		return
	}

	if err := util.HandleCluster(clusterId, clusterFile, backupClusterConfig); err != nil {
		fmt.Println(err)
		return
	}
}

func backupClusterConfig(client kubernetes.Interface, clusterId string) error {
	daemonSet, err := client.AppsV1().DaemonSets(v1.NamespaceSystem).Get(context.Background(), util.KUBEPROXY_DAEMONSET_NAME, v1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return fmt.Errorf("Kube Proxy Not Found. %s", clusterId)
		}
		return err
	}

	pods, err := client.CoreV1().Pods(v1.NamespaceSystem).List(context.Background(), v1.ListOptions{
		LabelSelector:   fmt.Sprintf("k8s-app=%s", util.KUBEPROXY_DAEMONSET_NAME),
		ResourceVersion: "0",
	})
	if err != nil {
		return err
	}

	revisionList, err := client.AppsV1().ControllerRevisions(v1.NamespaceSystem).List(context.Background(), v1.ListOptions{
		LabelSelector:   fmt.Sprintf("k8s-app=%s", util.KUBEPROXY_DAEMONSET_NAME),
		ResourceVersion: "0",
	})
	if err != nil {
		return err
	}

	if err := writeUpgradeRecord(clusterId, daemonSet, pods, revisionList); err != nil {
		return err
	}

	if err := writeBackup(clusterId, daemonSet, pods, revisionList); err != nil {
		return err
	}

	container := util.GetKubeProxyContainerFromDaemonSet(daemonSet)
	if container == nil {
		return fmt.Errorf("Kube Proxy Container Not Found. %s", clusterId)
	}
	if err := WriteOriginalDaemonSetImage(container.Image, clusterId); err != nil {
		return err
	}

	return nil
}

func writeUpgradeRecord(clusterId string, daemonSet *appv1.DaemonSet, podList *corev1.PodList, revisionList *appv1.ControllerRevisionList) error {
	_, revisionHashMap := util.GetHistoryMap(revisionList)

	currentControllerRevision, err := util.GetDaemonSetGeneration(daemonSet, revisionList.Items)
	if err != nil {
		return err
	}
	daemonSetRevisionHash := util.GetRevisionHashByControllerRevision(currentControllerRevision)

	// nodeName -> revisionHash
	podRevisionMap := make(map[string]string)
	classification := make(map[string][]string)
	for _, pod := range podList.Items {
		revisionHash := util.GetRevisionHashByPod(&pod)
		if _, exist := revisionHashMap[revisionHash]; !exist {
			return fmt.Errorf("pod %s node %s. 'controller-revision-hash' Revision History Not Exist", pod.Name, pod.Spec.NodeName)
		}
		podRevisionMap[pod.Spec.NodeName] = revisionHash

		if len(pod.Spec.Containers) >= 1 {
			ImageVersion := pod.Spec.Containers[0].Image
			if _, ok := classification[ImageVersion]; !ok {
				classification[ImageVersion] = make([]string, 0)
			}
			classification[ImageVersion] = append(classification[ImageVersion], pod.Spec.NodeName)
		}
	}

	if _, err := os.Stat("kubeproxy_backup" + "-" + clusterId); err != nil {
		if os.IsNotExist(err) {
			if err := os.Mkdir("kubeproxy_backup"+"-"+clusterId, os.ModePerm); err != nil {
				return err
			}
		}
	}

	f, err := os.Create(fmt.Sprintf("kubeproxy_backup-%s/%s.revertRevision", clusterId, clusterId))
	if err != nil {
		return err
	}
	defer f.Close()

	line := ""
	line = line + fmt.Sprintf("%s\t%s\n", "_status", "normal")
	line = line + fmt.Sprintf("%s\t%s\n", "_daemonset", daemonSetRevisionHash)
	for _, pod := range podList.Items {
		line = line + fmt.Sprintf("%s\t%s\n", pod.Spec.NodeName, util.GetRevisionHashByPod(&pod))
	}
	if _, err := f.WriteString(line); err != nil {
		return err
	}

	classificationData, err := encodingjson.Marshal(classification)
	if err != nil {
		fmt.Println("Marshal err: ", err)
		return nil
	}

	classificationBaseFile, err := os.Create(fmt.Sprintf("kubeproxy_backup-%s/%s.classificationBaseFile", clusterId, clusterId))
	if err != nil {
		return err
	}
	defer classificationBaseFile.Close()

	if _, err := classificationBaseFile.WriteString(string(classificationData)); err != nil {
		return err
	}

	return nil
}

func writeBackup(clusterId string, daemonSet *appv1.DaemonSet, pods *corev1.PodList, controllerRevisions *appv1.ControllerRevisionList) error {
	if _, err := os.Stat("kubeproxy_backup" + "-" + clusterId); err != nil {
		if os.IsNotExist(err) {
			if err := os.Mkdir("kubeproxy_backup"+"-"+clusterId, os.ModePerm); err != nil {
				return err
			}
		}
	}

	revisionHash := make(map[string]bool)
	for _, pod := range pods.Items {
		revisionHash[util.GetRevisionHashByPod(&pod)] = true
	}
	for _, controllerRevision := range controllerRevisions.Items {
		revisionHashByControllerRevision := util.GetRevisionHashByControllerRevision(&controllerRevision)
		if revisionHash[revisionHashByControllerRevision] == true {
			if err := backupObject(fmt.Sprintf("kubeproxy_backup-%s/%s.controllerrevision.%s.yaml", clusterId, clusterId, revisionHashByControllerRevision), &controllerRevision); err != nil {
				return err
			}
		}
	}

	// 备份DaemonSet相关的ControllerRevision
	currentRevision, err := util.GetDaemonSetGeneration(daemonSet, controllerRevisions.Items)
	if err != nil {
		return err
	}
	daemonSetRevisionHash := util.GetRevisionHashByControllerRevision(currentRevision)
	if err := backupObject(fmt.Sprintf("kubeproxy_backup-%s/%s.controllerrevision.%s.yaml", clusterId, clusterId, daemonSetRevisionHash), currentRevision); err != nil {
		return err
	}

	// 备份DaemonSet
	if err := backupObject(fmt.Sprintf("kubeproxy_backup-%s/%s.daemonset.yaml", clusterId, clusterId), daemonSet); err != nil {
		return err
	}
	return nil
}

func backupObject(backupName string, object runtime.Object) error {
	serializer := json.NewSerializerWithOptions(yaml.DefaultMetaFactory, scheme.Scheme, scheme.Scheme, json.SerializerOptions{
		Yaml:   true,
		Pretty: true,
		Strict: true,
	})

	if fileExists, err := PathExists(backupName); err != nil {
		return err
	} else if !fileExists {
		file, err := os.Create(backupName)
		if err != nil {
			return err
		}
		defer file.Close()

		copyObject := object.DeepCopyObject()
		gvks, isUnversioned, err := scheme.Scheme.ObjectKinds(copyObject)
		if err != nil {
			panic(err)
		}
		if !isUnversioned && len(gvks) == 1 {
			copyObject.GetObjectKind().SetGroupVersionKind(gvks[0])
		}
		if err := serializer.Encode(copyObject, file); err != nil {
			return err
		}
	}
	return nil
}

func PathExists(path string) (bool, error) {
	_, err := os.Stat(path)
	if err == nil {
		return true, nil
	}
	if os.IsNotExist(err) {
		return false, nil
	}
	return false, err
}

func WriteOriginalDaemonSetImage(originalVersion string, clusterId string) error {
	if _, err := os.Stat("kubeproxy_backup" + "-" + clusterId); err != nil {
		if os.IsNotExist(err) {
			if err := os.Mkdir("kubeproxy_backup"+"-"+clusterId, os.ModePerm); err != nil {
				return err
			}
		}
	}

	originalDaemonSetImageFile, err := os.Create(fmt.Sprintf("kubeproxy_backup-%s/%s.originalDaemonSetImageFile", clusterId, clusterId))
	if err != nil {
		return err
	}
	defer originalDaemonSetImageFile.Close()

	if _, err := originalDaemonSetImageFile.WriteString(originalVersion); err != nil {
		return err
	}
	return nil
}
