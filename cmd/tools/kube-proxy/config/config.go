package config

type Ianvs struct {
	Addr       string
	SecretUser string
	SecretID   string
	<PERSON><PERSON><PERSON>  string
}

func GetConfig() Ianvs {
	return Ianvs{
		Addr:       "https://your-ianvs-server.com", // 替换为实际的ianvs服务地址
		SecretUser: "your-username",                 // 替换为实际的用户名
		SecretID:   "your-secret-id",                // 替换为实际的SecretID
		SecretKey:  "your-secret-key",               // 替换为实际的SecretKey
	}
}
