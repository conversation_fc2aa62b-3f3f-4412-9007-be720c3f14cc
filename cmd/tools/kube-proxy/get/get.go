package get

import (
	"context"
	"fmt"

	"git.woa.com/kmetis/starship/cmd/tools/kube-proxy/config"
	"git.woa.com/kmetis/starship/cmd/tools/kube-proxy/ianvs"
	"git.woa.com/kmetis/starship/cmd/tools/kube-proxy/util"

	"github.com/spf13/cobra"
	appsv1 "k8s.io/api/apps/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
)

var GetCmd = &cobra.Command{
	Use:   "get",
	Short: "Get kube-proxy status",
	Run: func(cmd *cobra.Command, args []string) {
		runQuery(cmd)
	},
}

func runQuery(cmd *cobra.Command) {
	// init ianvs config
	ianvs.InitIanvsClient(config.GetConfig())

	clusterId := cmd.Flags().Lookup("cluster").Value.String()
	clusterFile := cmd.Flags().Lookup("file").Value.String()
	if clusterId == "" && clusterFile == "" {
		fmt.Println("Error: must specify either --cluster or --file")
		return
	}

	if err := util.HandleCluster(clusterId, clusterFile, queryClusterStatus); err != nil {
		fmt.Println(err)
		return
	}
}

func queryClusterStatus(client kubernetes.Interface, clusterName string) error {
	// 1. 获取kube-proxy DaemonSet详情
	ds, err := client.AppsV1().DaemonSets("kube-system").Get(context.TODO(), "kube-proxy", metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("[%s] Error getting kube-proxy DaemonSet: %v", clusterName, err)
	}

	// 2. 输出DaemonSet基本信息
	fmt.Printf("[%s] kube-proxy DaemonSet Status:\n", clusterName)
	fmt.Printf("更新策略: %s\n", ds.Spec.UpdateStrategy.Type)
	fmt.Printf("期望Pod数: %d\n", ds.Status.DesiredNumberScheduled)
	fmt.Printf("当前Pod数: %d\n", ds.Status.CurrentNumberScheduled)
	fmt.Printf("就绪Pod数: %d\n", ds.Status.NumberReady)
	fmt.Printf("可用Pod数: %d\n", ds.Status.NumberAvailable)

	revisionList, err := client.AppsV1().ControllerRevisions(v1.NamespaceSystem).List(context.Background(), v1.ListOptions{
		LabelSelector:   fmt.Sprintf("k8s-app=%s", util.KUBEPROXY_DAEMONSET_NAME),
		ResourceVersion: "0",
	})
	if err != nil {
		return fmt.Errorf("[%s] Get ControllerRevision list failed: %v", clusterName, err)
	}
	controllerRevision, err := util.GetDaemonSetGeneration(ds, revisionList.Items)
	revision := util.GetRevisionHashByControllerRevision(controllerRevision)
	if err != nil {
		revision = ""
		return fmt.Errorf("[%s] Get ControllerRevision hash failed: %v", clusterName, err)
	}
	fmt.Printf("current controllerRevision : %s\n", revision)

	// 3. 查询节点状态
	nodes, err := client.CoreV1().Nodes().List(context.TODO(), metav1.ListOptions{
		ResourceVersion: "0",
	})
	if err != nil {
		return fmt.Errorf("[%s] Error listing nodes: %v", clusterName, err)
	}

	notReadyNodes := make([]string, 0)
	for _, node := range nodes.Items {
		for _, cond := range node.Status.Conditions {
			if cond.Type == "Ready" && cond.Status != "True" {
				notReadyNodes = append(notReadyNodes, node.Name)
				break
			}
		}
	}

	// 4. 查询kube-proxy Pods
	pods, err := client.CoreV1().Pods("kube-system").List(context.TODO(), metav1.ListOptions{
		LabelSelector:   "k8s-app=kube-proxy",
		ResourceVersion: "0",
	})
	if err != nil {
		return fmt.Errorf("[%s] Error listing kube-proxy pods: %v", clusterName, err)
	}

	// 5. 分析Pod变更状态
	var (
		currentPods  int
		oldPods      int
		pendingPods  int
		runningPods  int
		notReadyPods int
	)

	for _, pod := range pods.Items {
		if pod.ObjectMeta.Labels["controller-revision-hash"] == revision {
			currentPods++
		} else {
			oldPods++
		}

		switch pod.Status.Phase {
		case "Pending":
			pendingPods++
		case "Running":
			runningPods++
		default:
			notReadyPods++
		}
	}

	// 6. 输出变更详情
	fmt.Printf("\nPod变更进度:\n")
	fmt.Printf("总Pod数: %d\n", len(pods.Items))
	fmt.Printf("已更新Pod: %d (%.1f%%)\n", currentPods, float64(currentPods)/float64(len(pods.Items))*100)
	fmt.Printf("待更新Pod: %d\n", oldPods)
	fmt.Printf("Pending状态Pod: %d\n", pendingPods)
	fmt.Printf("Running状态Pod: %d\n", runningPods)
	fmt.Printf("异常状态Pod: %d\n", notReadyPods)

	// 7. 计算并输出受NotReady节点影响的Pod数
	var skippedPods int
	for _, pod := range pods.Items {
		for _, node := range notReadyNodes {
			if pod.Spec.NodeName == node {
				skippedPods++
				break
			}
		}
	}
	fmt.Printf("NotReady节点(%d/%d): %v\n", len(notReadyNodes), len(nodes.Items), notReadyNodes)
	fmt.Printf("受NotReady节点影响的Pod: %d (将跳过更新)\n\n", skippedPods)
	return nil
}

func getCRHash(clientset kubernetes.Interface, ds *appsv1.DaemonSet) (string, error) {
	// 使用更精确的标签选择器
	selector, err := metav1.LabelSelectorAsSelector(ds.Spec.Selector)
	if err != nil {
		return "", err
	}

	controllerRevisions, err := clientset.AppsV1().ControllerRevisions(ds.Namespace).List(context.TODO(), metav1.ListOptions{
		LabelSelector:   selector.String(),
		ResourceVersion: "0",
	})
	if err != nil {
		return "", fmt.Errorf("获取ControllerRevisions失败: %v", err)
	}

	var max int
	for index, cr := range controllerRevisions.Items {
		if cr.Revision > controllerRevisions.Items[max].Revision {
			max = index
		}
	}

	return controllerRevisions.Items[max].Labels[appsv1.DefaultDaemonSetUniqueLabelKey], nil
}
