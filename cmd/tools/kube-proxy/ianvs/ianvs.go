package ianvs

import (
	"git.woa.com/ianvs/ianvs-sdk/pkg/client"
	"git.woa.com/kmetis/starship/cmd/tools/kube-proxy/config"

	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
)

var ianvsClient struct {
	client *client.Client
	user   string
}

func InitIanvsClient(ianvs config.Ianvs) {
	ianvsClient.client = client.NewClient(
		ianvs.Addr,
		ianvs.SecretID,
		ianvs.SecretKey,
	)
	ianvsClient.user = ianvs.SecretUser
}

func GetClusterKubeConfig(cluster string) (*rest.Config, error) {
	return ianvsClient.client.GetClientConfig(ianvsClient.user, cluster)
}

func getK8sClientFromRest(config *rest.Config) (*kubernetes.Clientset, error) {
	config.QPS = 80
	config.Burst = 100
	config.UserAgent = "starship-kube-proxy"
	return kubernetes.NewForConfig(config)
}

func BuildK8sClientByIanvs(cls string) (*kubernetes.Clientset, error) {
	kubeConfig, err := GetClusterKubeConfig(cls)
	if err != nil {
		return nil, err
	}

	return getK8sClientFromRest(kubeConfig)
}

func GetClusterClient(clusterName string) (kubernetes.Interface, error) {
	restConfig, err := GetClusterKubeConfig(clusterName)
	if err != nil {
		return nil, err
	}

	client, err := kubernetes.NewForConfig(restConfig)
	if err != nil {
		return nil, err
	}

	return client, nil
}
