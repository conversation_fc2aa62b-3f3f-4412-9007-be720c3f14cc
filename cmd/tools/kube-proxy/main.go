package main

import (
	"fmt"
	"os"

	"github.com/spf13/cobra"
	"github.com/spf13/pflag"

	"git.woa.com/kmetis/starship/cmd/tools/kube-proxy/backup"
	"git.woa.com/kmetis/starship/cmd/tools/kube-proxy/get"
	"git.woa.com/kmetis/starship/cmd/tools/kube-proxy/rollback"
)

var rootCmd = &cobra.Command{
	Use:   "kube-proxy",
	Short: "kube-proxy management tool",
}

func init() {
	rootCmd.PersistentFlags().StringP("cluster", "c", "", "single cluster ID")
	rootCmd.PersistentFlags().StringP("file", "f", "", "file path containing cluster IDs, one per line")
	rootCmd.PersistentFlags().AddFlagSet(pflag.CommandLine)
	rootCmd.AddCommand(get.GetCmd)
	rootCmd.AddCommand(backup.BackupCmd)
	rootCmd.AddCommand(rollback.RollbackCmd)
	pflag.CommandLine.SortFlags = false
}

func main() {
	if err := rootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}
