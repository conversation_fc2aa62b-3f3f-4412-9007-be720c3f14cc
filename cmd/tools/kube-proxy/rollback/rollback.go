package rollback

import (
	"bytes"
	"context"
	"encoding/json"
	encodingjson "encoding/json"
	"fmt"
	"io"
	"math"
	"os"
	"strings"
	"time"

	ianvs "git.woa.com/ianvs/ianvs-sdk/pkg/client"
	"github.com/spf13/cobra"
	v13 "k8s.io/api/apps/v1"
	v14 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/client-go/informers"
	v12 "k8s.io/client-go/informers/core/v1"
	"k8s.io/client-go/kubernetes"
)

func init() {
	RollbackCmd.Flags().Int("count", 0, "The cluster upgrade count")
	//RollbackCmd.Flags().StringP("clusterID", "c", "", "clusterID")
	RollbackCmd.Flags().StringP("image", "i", "", "upgrade special image")
	RollbackCmd.Flags().StringP("user", "u", "", "user")
	RollbackCmd.Flags().StringP("token", "t", "", "token")
}

var RollbackCmd = &cobra.Command{
	Use:   "rollback",
	Short: "rollback kube-proxy",
	Run: func(cmd *cobra.Command, args []string) {
		rollback(cmd)
	},
}

type RevertRecord struct {
	NodeName     string
	RevisionHash string

	RevertComplete      bool
	CurrentPod          *v14.Pod
	CurrentRevisionHash string
	CurrentRevision     int64
}

func rollback(cmd *cobra.Command) error {

	count, _ := cmd.Flags().GetInt("count")

	clusterId, _ := cmd.Flags().GetString("cluster")
	user, _ := cmd.Flags().GetString("user")
	token, _ := cmd.Flags().GetString("token")
	image, _ := cmd.Flags().GetString("image")

	fmt.Printf("clusterId: %s\n", clusterId)
	fmt.Printf("count: %d\n", count)

	fmt.Printf("user: %s\n", user)
	fmt.Printf("token: %s\n", token)
	fmt.Printf("image: %s\n", image)

	taskImage := ""
	if image == "all" {
		taskImage = "allimage"
	} else {
		taskImage = image
	}
	fmt.Printf("taskImage: %s\n", taskImage)

	//clientSet, err := cluster2.GetTKEClusterClientSet(cluster.Region, cluster)
	client, err := getTKEClusterClientSetIanvs(clusterId, user, token)
	if err != nil {
		fmt.Printf("GetTKEClusterClientSet err: %s\n", err)
		return fmt.Errorf("GetTKEClusterClientSet err: %s\n", err)
	}
	// 1. 获取kube-proxy DaemonSet详情
	daemonSet, err := client.AppsV1().DaemonSets("kube-system").Get(context.TODO(), "kube-proxy", metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("[%s] Error getting kube-proxy DaemonSet: %v", clusterId, err)
	}
	fmt.Printf("更新策略2: %s\n", daemonSet.Spec.UpdateStrategy.Type)
	fmt.Printf("image: %s\n", daemonSet.Spec.Template.Spec.Containers[0].Image)

	if daemonSet.Spec.UpdateStrategy.Type != v13.OnDeleteDaemonSetStrategyType {
		// 必须确保DaemonSet的更新策略为OnDelete
		if daemonSet, err = updateStrategyType(client, daemonSet, v13.OnDeleteDaemonSetStrategyType); err != nil {
			return err
		}
	}

	// 寻找历史版本
	revisionList, err := client.AppsV1().ControllerRevisions(v1.NamespaceSystem).List(context.Background(), v1.ListOptions{
		LabelSelector:   fmt.Sprintf("k8s-app=%s", KUBEPROXY_DAEMONSET_NAME),
		ResourceVersion: "0",
	})
	if err != nil {
		return err
	}

	/*
		currentRevision  &v13.ControllerRevision{...} which is current revision
	*/

	currentRevision, err := getDaemonSetGeneration(daemonSet, revisionList.Items)
	//_, err = getDaemonSetGeneration(daemonSet, revisionList.Items)
	if err != nil {
		return err
	}

	/*
		[root@VM-192-56-tencentos ~]# kubectl get controllerrevision -A
		NAMESPACE     NAME                          CONTROLLER                         REVISION   AGE
		kube-system   ip-masq-agent-657d857996      daemonset.apps/ip-masq-agent       1          194d
		kube-system   kube-proxy-765799597f         daemonset.apps/kube-proxy          1          194d
		revisionHashMap 格式：
		revisionHashMap["765799597f"] = &v13.ControllerRevision{...}
	*/
	_, revisionHashMap := getHistoryMap(revisionList)

	//_, _ = getHistoryMap(revisionList)

	/*
		➜  kubeproxy_backup-cls-ldrh696r cat cls-ldrh696r.revertRevision
		_status	normal
		_daemonset	6f84c68b57
		*********	6f84c68b57
		**********	6f84c68b57
		*********	6f84c68b57

		以上第一列作为revertRecord的key，第二列作为revertRecord的value
	*/
	revertRecord, err := readUpgradeRecord(clusterId)
	if err != nil {
		return fmt.Errorf("readUpgradeRecord:", err)
	}

	if revertRecord["_status"] == "rollbacked" {
		fmt.Printf("Already Rollback")
		return fmt.Errorf("Already Rollback")
	}
	if revertRecord["_status"] == "normal" || revertRecord["_status"] == "complete" {
		if err := updateUpgradeRecordStatus(clusterId, "rollbacking"); err != nil {
			return err
		}
		// 更新内存
		revertRecord["_status"] = "rollbacking"
	}

	/*
		➜  kubeproxy_backup-cls-ldrh696r cat cls-ldrh696r.classificationBaseFile
		{"ccr.ccs.tencentyun.com/tkeimages/hyperkube:v1.26.1-tke.3":["*********","**********","*********"]}%
		➜  kubeproxy_backup-cls-ldrh696r

		classificationBaseMap is make(map[string][]string)
		以上第一列作为classificationBaseMap的key，第二列作为classificationBaseMap的value
	*/
	classificationBaseMap, err := readClassificationBaseRecord(clusterId)
	if err != nil {
		return err
	}

	/*
		baseNodeNameToImageVersionMap key是节点名称，value是imageVersion, like: "ccr.ccs.tencentyun.com/tkeimages/hyperkube:v1.26.1-tke.3"
	*/
	baseNodeNameToImageVersionMap := make(map[string]string)
	for key, val := range classificationBaseMap {
		for _, nodeN := range val {
			baseNodeNameToImageVersionMap[nodeN] = key
		}
	}

	fmt.Printf("baseNodeNameToImageVersionMap : %v\n", baseNodeNameToImageVersionMap)

	needUpdateCount := math.MaxInt32
	if count != 0 {
		needUpdateCount = count
	}

	/*
		rollbackNumForImageVersionMap key是imageVersion，like: "ccr.ccs.tencentyun.com/tkeimages/hyperkube:v1.26.1-tke.3"
		value是需要回滚的节点个数
	*/
	rollbackNumForImageVersionMap := make(map[string]int)
	for _, val := range baseNodeNameToImageVersionMap {
		if image != "all" {
			if image == val {
				rollbackNumForImageVersionMap[val] = needUpdateCount
			}
		} else {
			rollbackNumForImageVersionMap[val] = needUpdateCount
		}
	}

	fmt.Printf("rollbackNumForImageVersionMap: %v\n", rollbackNumForImageVersionMap)
	/*
		nodeMap key是节点名称，value是节点对象
	*/
	nodeMap := make(map[string]*v14.Node)
	if nodes, err := client.CoreV1().Nodes().List(context.Background(), v1.ListOptions{
		LabelSelector:   "node.kubernetes.io/instance-type!=eklet",
		ResourceVersion: "0",
	}); err != nil {
		return err
	} else {
		for index, node := range nodes.Items {
			nodeMap[node.Name] = &nodes.Items[index]
		}
	}

	revertRecordMap := make(map[string]*RevertRecord)
	/*
		revertRecordMap like:
		key is nodeName. It looks like:
		map[string]*RevertRecord {
		"10.0.0.152": &RevertRecord{
			NodeName     string
			RevisionHash string

			RevertComplete      bool
			CurrentPod          *v14.Pod
			CurrentRevisionHash string}
		}
	*/

	// 以下这块为分组逻辑

	/*
		RevertRecord{
			NodeName     string
			RevisionHash string

			RevertComplete      bool
			CurrentPod          *v14.Pod
			CurrentRevisionHash string}
		}
		HashToRevertRecordSliceMap key is revisionHash
	*/
	HashToRevertRecordSliceMap := make(map[string][]*RevertRecord)

	/*
		➜  kubeproxy_backup-cls-ldrh696r cat cls-ldrh696r.revertRevision
		_status	normal
		_daemonset	6f84c68b57
		*********	6f84c68b57
		**********	6f84c68b57
		*********	6f84c68b57

		以上第一列作为revertRecord的key，第二列作为revertRecord的value
	*/
	for nodeName, revisionHash := range revertRecord {
		if strings.HasPrefix(nodeName, "_") { // 特殊记录
			continue
		}

		if nodeMap[nodeName] == nil {
			continue
		}

		// 判断节点是否处于Ready状态
		for _, condition := range nodeMap[nodeName].Status.Conditions {
			if condition.Type == "Ready" && condition.Status == "True" {
				revertRecordMap[nodeName] = &RevertRecord{
					NodeName:     nodeName,
					RevisionHash: revisionHash,
				}

				//保存hash到RevertRecordSlice的映射，以每次尽量将一个hash批次的一起回滚，减少daemonset的变更次数
				if _, exist := HashToRevertRecordSliceMap[revisionHash]; !exist {
					HashToRevertRecordSliceMap[revisionHash] = make([]*RevertRecord, 0)
				}
				HashToRevertRecordSliceMap[revisionHash] = append(HashToRevertRecordSliceMap[revisionHash], revertRecordMap[nodeName])
			}
		}
	}

	podInformer := startKubeProxyPodWatcher(client)
	if pods, err := podInformer.Lister().Pods(v1.NamespaceSystem).List(labels.SelectorFromSet(map[string]string{
		"k8s-app": KUBEPROXY_DAEMONSET_NAME,
	})); err != nil {
		return err
	} else {
		for index, pod := range pods {
			if pod.DeletionTimestamp != nil {
				continue
			}
			fmt.Printf("pod.Spec.NodeName: %s\n", pod.Spec.NodeName)
			record, ok := revertRecordMap[pod.Spec.NodeName]
			if !ok {
				fmt.Printf("pod.Spec.NodeName not exist in revertRecordMap. pod.Spec.NodeName: %s\n", pod.Spec.NodeName)
				continue
			}
			record.CurrentPod = pods[index] //这里需要确保pods是running状态，如果为pending状态，这里会crash
			if revisionHash, exist := pod.Labels["controller-revision-hash"]; exist {
				record.CurrentRevisionHash = revisionHash
				if record.CurrentRevisionHash == record.RevisionHash {
					record.RevertComplete = true
				}
				if revision, exist := revisionHashMap[record.CurrentRevisionHash]; exist {
					record.CurrentRevision = revision.Revision
				}
			}
		}
	}

	rollbackList := make([]string, 0)
	runningMap := make(map[string]bool)
	podsMap := make(map[string]*v14.Pod)

	for specialHash, specalHashRevertRecordSlice := range HashToRevertRecordSliceMap {
		rollbackGroupList := make([]string, 0)
		for _, record := range specalHashRevertRecordSlice {
			if rollbackNumForImageVersionMap[baseNodeNameToImageVersionMap[record.NodeName]] == 0 {
				continue
			}

			if record.RevertComplete {
				continue
			}

			if record.CurrentPod == nil {
				fmt.Printf("Pod Not Exist on Node %s.\n", record.NodeName)
				continue
			}

			// 确认当前DaemonSet版本已回滚
			if record.RevisionHash != currentRevision.Labels["controller-revision-hash"] {
				controllerRevision := revisionHashMap[specialHash]

				if daemonSet, err = daemonsetRollback(client, daemonSet, controllerRevision); err != nil {
					return err
				}

				if currentRevision, err = getDaemonSetGeneration(daemonSet, revisionList.Items); err != nil {
					return err
				}
			}

			// 删除Pod，触发更新
			fmt.Printf("Delete Pod %s Node %s\n", record.CurrentPod.Name, record.NodeName)
			if err := client.CoreV1().Pods(v1.NamespaceSystem).Delete(context.Background(), record.CurrentPod.Name, v1.DeleteOptions{}); err != nil {
				return err
			}

			rollbackGroupList = append(rollbackGroupList, record.NodeName)
			rollbackNumForImageVersionMap[baseNodeNameToImageVersionMap[record.NodeName]] =
				rollbackNumForImageVersionMap[baseNodeNameToImageVersionMap[record.NodeName]] - 1

			//time.Sleep(60*time.Second)
		}

		if len(rollbackGroupList) != 0 {
			//在下一次修改ds之前，需要确保系统中所有的kube-proxy已经处于running状
			err := waitPodStart(podInformer, rollbackGroupList, revertRecordMap, runningMap, podsMap)
			if err != nil {
				return err
			}
			rollbackList = append(rollbackList, rollbackGroupList...)
		}
		time.Sleep(10 * time.Second)
	}

	// 后检
	for _, nodeName := range rollbackList {
		pod, exist := podsMap[nodeName]
		if !exist {
			fmt.Printf("Pod in node %s Not Exist.\n", nodeName)
			continue
		}

		// 确认DaemonSet Pod启动
		if err := waitPodRunning(podInformer, client, clusterId, pod.Name); err != nil {
			return err
		}

		// 验证kubeproxy数据面
		//if err := CheckKubeProxy(&clientSet, pod.Name); err != nil {
		//	return revertRecordMap, err
		//}

		fmt.Printf("Pod %s(%s) Upgrade Success.\n", pod.Name, nodeName)
	}

	allRollback := true
	for _, record := range revertRecordMap {
		if record.RevertComplete == false {
			allRollback = false
			break
		}
	}

	daemonSetRevisionHash := getRevisionHashByControllerRevision(currentRevision)
	//fmt.Printf(" allRollback %v\n", allRollback)
	//fmt.Printf(" revertRecord %v\n", revertRecord["_daemonset"])
	//fmt.Printf(" daemonSetRevisionHash %v\n", daemonSetRevisionHash)
	//fmt.Printf(" revisionHashMap %v\n", common.JsonWrapper(revisionHashMap))

	if allRollback {
		versionHash := revertRecord["_daemonset"]
		if daemonSetRevisionHash != versionHash {
			if revertRevision, exist := revisionHashMap[versionHash]; !exist {
				return fmt.Errorf("DaemonSet Revert Revision NotFoud")
			} else {
				if daemonSet, err = daemonsetRollback(client, daemonSet, revertRevision); err != nil {
					return err
				}
			}
		}

		if revertRecord["_status"] == "rollbacking" {
			if err := updateUpgradeRecordStatus(clusterId, "rollbacked"); err != nil {
				return err
			}
		}
	}

	return nil
}

const (
	KUBEPROXY_DAEMONSET_NAME = "kube-proxy"
	KUBEPROXY_CONTAINER_NAME = "kube-proxy"
)

func waitPodRunning(podInformer v12.PodInformer, client kubernetes.Interface, clusterInstanceId, podName string) error {
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Minute)
	defer cancel()
	ticker := time.NewTicker(time.Second)
	defer ticker.Stop()
	for {
		select {
		case <-ctx.Done():
			return fmt.Errorf("KubeProxy Pod Not Delete In 1 Minute. %s:%s", clusterInstanceId, podName)
		case <-ticker.C:
			pod, err := podInformer.Lister().Pods(v1.NamespaceSystem).Get(podName)
			if err != nil {
				fmt.Errorf("getpod error. %s", err)
				continue
			}

			if pod.Status.Phase != v14.PodRunning {
				fmt.Printf("Wait pod running. %s: %s\n", pod.Spec.NodeName, pod.Name)
				break
			}

			req := client.CoreV1().Pods(v1.NamespaceSystem).GetLogs(pod.Name, &v14.PodLogOptions{
				Container: KUBEPROXY_CONTAINER_NAME,
			})
			podLogs, err := req.Stream(context.Background())
			if err != nil {
				fmt.Printf("getpod logs error. will retry. %s %s, %s\n", pod.Spec.NodeName, pod.Name, err)
				continue
			}

			buf := new(bytes.Buffer)
			if _, err = io.Copy(buf, podLogs); err != nil {
				podLogs.Close()
				continue
			}
			podLogs.Close()

			log1 := false
			for _, line := range strings.Split(buf.String(), "\n") {
				if strings.HasPrefix(line, "E") {
					fmt.Printf("%s(%s) error: %s\n", pod.Name, pod.Spec.NodeName, line)
					return fmt.Errorf("KubeProxy Log Error. %s", line)
				}
				//fmt.Printf("aaaline: %s\n", line )
				if strings.Contains(line, "Caches are synced") {
					log1 = true
				}
			}
			if log1 {
				return nil
			}
			continue
		}
	}
}

func getRevisionHashByPod(pod *v14.Pod) string {
	return pod.Labels["controller-revision-hash"]
}

func waitPodStart(podInformer v12.PodInformer, nodes []string, revertRecord map[string]*RevertRecord, runningMap map[string]bool, podsMap map[string]*v14.Pod) error {
	if len(nodes) == 0 {
		return nil
	}
	ctx, cancel := context.WithTimeout(context.Background(), time.Minute)
	defer cancel()
	ticker := time.NewTicker(2 * time.Second)
	defer ticker.Stop()
	waitRunninglist := []string{}
OUTER:
	for {
		select {
		case <-ctx.Done():
			return nil
		case <-ticker.C:
			if pods, err := podInformer.Lister().Pods(v1.NamespaceSystem).List(labels.SelectorFromSet(map[string]string{
				"k8s-app": KUBEPROXY_DAEMONSET_NAME,
			})); err != nil {
				return err
			} else {
				for index, pod := range pods {
					if pod.DeletionTimestamp != nil {
						continue
					}
					podsMap[pod.Spec.NodeName] = pods[index]
				}
			}

			needRecheck := false
			waitRunninglist = []string{}
			for _, nodeName := range nodes {
				pod, exist := podsMap[nodeName]
				record, _ := revertRecord[nodeName]
				if !exist || getRevisionHashByPod(pod) != record.RevisionHash {
					continue OUTER
				}
				if pod.Status.Phase != v14.PodRunning {
					needRecheck = true
					waitRunninglist = append(waitRunninglist, pod.Name+"/"+nodeName)
				} else {
					if _, ok := runningMap[nodeName]; !ok {
						runningMap[nodeName] = true
						fmt.Printf("Pod %s(%s) become running.\n", pod.Name, nodeName)
					}
				}

				if needRecheck == true {
					continue OUTER
				}
			}

			if len(waitRunninglist) > 0 {
				fmt.Printf("waitRunninglist is: \n")
				for _, val := range waitRunninglist {
					fmt.Printf("%s, \n", val)
				}
			}
			return nil
		}
	}
}

func daemonsetRollback(client kubernetes.Interface, daemonSet *v13.DaemonSet, history *v13.ControllerRevision) (*v13.DaemonSet, error) {
	fmt.Printf("Rollback DaemonSet To Hash %s\n", getRevisionHashByControllerRevision(history))
	for i := 0; i < 3; i++ {
		update, err := client.AppsV1().DaemonSets("kube-system").Patch(context.TODO(), KUBEPROXY_DAEMONSET_NAME, types.StrategicMergePatchType, history.Data.Raw, v1.PatchOptions{})
		if err != nil {
			fmt.Printf("DaemonSets Update Error. %s\n", err)
			time.Sleep(500 * time.Millisecond)
			daemonSet, _ = client.AppsV1().DaemonSets("kube-system").Get(context.Background(), KUBEPROXY_DAEMONSET_NAME, v1.GetOptions{})
			continue
		}
		return update, nil
	}
	return nil, fmt.Errorf("daemonSet rollback error.")
}

func startKubeProxyPodWatcher(client kubernetes.Interface) v12.PodInformer {
	sharedInformerFactory := informers.NewSharedInformerFactoryWithOptions(
		client,
		15*time.Minute,
		informers.WithNamespace(v1.NamespaceSystem),
		informers.WithTweakListOptions(func(options *v1.ListOptions) {
			options.LabelSelector = fmt.Sprintf("k8s-app=%s", KUBEPROXY_DAEMONSET_NAME)
		}),
	)
	podInformer := sharedInformerFactory.Core().V1().Pods()
	go podInformer.Informer().Run(wait.NeverStop)

	for !podInformer.Informer().HasSynced() {
		time.Sleep(time.Second)
		fmt.Printf("Waiting for stores to sync.\n")
	}
	return podInformer
}

func readClassificationBaseRecord(clusterInstanceId string) (map[string][]string, error) {
	if _, err := os.Stat("kubeproxy_backup" + "-" + clusterInstanceId); err != nil {
		if os.IsNotExist(err) {
			if err := os.Mkdir("kubeproxy_backup"+"-"+clusterInstanceId, os.ModePerm); err != nil {
				return nil, err
			}
		}
	}

	filepath := fmt.Sprintf("kubeproxy_backup-%s/%s.classificationBaseFile", clusterInstanceId, clusterInstanceId)
	content, err := os.ReadFile(filepath)
	if err != nil {
		if os.IsNotExist(err) {
			return nil, nil
		}
		return nil, err
	}

	classificationBaseMap := make(map[string][]string)
	err = encodingjson.Unmarshal([]byte(content), &classificationBaseMap)
	if err != nil {
		fmt.Printf("Unmarshal classificationData failed: ", err)
		return nil, err
	}
	return classificationBaseMap, nil
}

func updateUpgradeRecordStatus(clusterInstanceId, status string) error {
	fmt.Printf("Update Task Status %s\n", status)

	if _, err := os.Stat("kubeproxy_backup" + "-" + clusterInstanceId); err != nil {
		if os.IsNotExist(err) {
			if err := os.Mkdir("kubeproxy_backup"+"-"+clusterInstanceId, os.ModePerm); err != nil {
				return err
			}
		}
	}

	filepath := fmt.Sprintf("kubeproxy_backup-%s/%s.revertRevision", clusterInstanceId, clusterInstanceId)
	content, err := os.ReadFile(filepath)
	if err != nil {
		return err
	}

	// 找到状态进行修改
	lines := strings.Split(string(content), "\n")
	for index, line := range lines {
		columns := strings.Split(line, "\t")
		if len(columns) != 2 {
			continue
		}
		if columns[0] == "_status" {
			lines[index] = fmt.Sprintf(fmt.Sprintf("%s\t%s", "_status", status))
		}
	}

	// 写回文件
	f, err := os.Create(fmt.Sprintf("kubeproxy_backup-%s/%s.revertRevision", clusterInstanceId, clusterInstanceId))
	if err != nil {
		return err
	}
	defer f.Close()

	line := strings.Join(lines, "\n")
	if _, err := f.WriteString(line); err != nil {
		return err
	}
	return nil
}

func readUpgradeRecord(clusterInstanceId string) (map[string]string, error) {
	revisionMap := make(map[string]string)
	if _, err := os.Stat("kubeproxy_backup" + "-" + clusterInstanceId); err != nil {
		if os.IsNotExist(err) {
			if err := os.Mkdir("kubeproxy_backup"+"-"+clusterInstanceId, os.ModePerm); err != nil {
				return revisionMap, err
			}
		}
	}

	filepath := fmt.Sprintf("kubeproxy_backup-%s/%s.revertRevision", clusterInstanceId, clusterInstanceId)
	content, err := os.ReadFile(filepath)
	if err != nil {
		if os.IsNotExist(err) {
			return revisionMap, nil
		}
		return revisionMap, err
	}

	lines := strings.Split(string(content), "\n")

	for _, line := range lines {
		columns := strings.Split(line, "\t")
		if len(columns) != 2 {
			continue
		}
		revisionMap[columns[0]] = columns[1]
	}
	return revisionMap, nil
}

func getHistoryMap(revisionList *v13.ControllerRevisionList) (map[int64]*v13.ControllerRevision, map[string]*v13.ControllerRevision) {
	revisionMap := make(map[int64]*v13.ControllerRevision)
	revisionHashMap := make(map[string]*v13.ControllerRevision)
	for index, revision := range revisionList.Items {
		history := &revisionList.Items[index]
		revisionMap[revision.Revision] = history
		revisionHashMap[getRevisionHashByControllerRevision(history)] = history
	}
	return revisionMap, revisionHashMap
}

func getRevisionHashByControllerRevision(revision *v13.ControllerRevision) string {
	return revision.Labels["controller-revision-hash"]
}

// getPatch returns a strategic merge patch that can be applied to restore a Daemonset to a
// previous version. If the returned error is nil the patch is valid. The current state that we save is just the
// PodSpecTemplate. We can modify this later to encompass more state (or less) and remain compatible with previously
// recorded patches.
func GetDaemonSetPatch(ds *v13.DaemonSet) ([]byte, error) {
	dsBytes, err := json.Marshal(ds)
	if err != nil {
		return nil, err
	}
	var raw map[string]interface{}
	err = json.Unmarshal(dsBytes, &raw)
	if err != nil {
		return nil, err
	}
	objCopy := make(map[string]interface{})
	specCopy := make(map[string]interface{})

	// Create a patch of the DaemonSet that replaces spec.template
	spec := raw["spec"].(map[string]interface{})
	template := spec["template"].(map[string]interface{})
	specCopy["template"] = template
	template["$patch"] = "replace"
	objCopy["spec"] = specCopy
	patch, err := json.Marshal(objCopy)
	return patch, err
}

func daemonSetMatch(ds *v13.DaemonSet, history *v13.ControllerRevision) (bool, error) {
	patch, err := GetDaemonSetPatch(ds)
	if err != nil {
		return false, err
	}
	return bytes.Equal(patch, history.Data.Raw), nil
}

func getDaemonSetGeneration(daemonSet *v13.DaemonSet, revisionList []v13.ControllerRevision) (*v13.ControllerRevision, error) {
	for index, _ := range revisionList {
		history := &revisionList[index]
		if match, err := daemonSetMatch(daemonSet, history); err != nil {
			return nil, err
		} else if match {
			return history, nil
		}
	}
	return nil, fmt.Errorf("Revision Not Found")
}

func updateStrategyType(clientSet kubernetes.Interface, daemonSet *v13.DaemonSet, strategyType v13.DaemonSetUpdateStrategyType) (*v13.DaemonSet, error) {
	for i := 0; i < 3; i++ {
		if daemonSet.Spec.UpdateStrategy.Type == strategyType {
			return daemonSet, nil
		}
		daemonSet.Spec.UpdateStrategy.Type = strategyType
		update, err := clientSet.AppsV1().DaemonSets("kube-system").Update(context.Background(), daemonSet, v1.UpdateOptions{})
		if err != nil {
			fmt.Printf("DaemonSets Update Error. %s\n", err)
			time.Sleep(500 * time.Millisecond)
			daemonSet, _ = clientSet.AppsV1().DaemonSets("kube-system").Get(context.Background(), KUBEPROXY_DAEMONSET_NAME, v1.GetOptions{})
			continue
		}
		return update, nil
	}
	return nil, fmt.Errorf("Update StrategyType Error")
}

func getTKEClusterClientSetIanvs(clusterInstanceId, user, token string) (kubernetes.Interface, error) {

	// 需要指定变更实施人和申请到的临时token
	// ianvs.NewTmpTokenClient("bgbiaoxu", "b6bfeceb78725e0cb2e73434f6194f5d") 这里边的
	// 用户名是使用自己的OA账号;token都是使用前文申请的临时token
	//tmpclient := ianvs.NewTmpTokenClient("zhikuodu", "7c78666ff31aabdbf31b5a78cc6fdd92")
	tmpclient := ianvs.NewTmpTokenClient(user, token)

	// 获取指定集群的restConfig，临时token必须包含该集群内
	//restConfig, restConfigErr := tmpclient.GetRestConfig("cls-8hnj9c97")
	restConfig, restConfigErr := tmpclient.GetRestConfig(clusterInstanceId)

	if restConfigErr != nil {
		fmt.Printf("GetTKEClusterClientSet token or user is invalid!\n\n")
		return nil, restConfigErr
	}

	clientSet, clientSetErr := kubernetes.NewForConfig(restConfig)

	if clientSetErr != nil {
		fmt.Printf("GetTKEClusterClientSet NewForConfig token or user is invalid!\n\n")
		return nil, clientSetErr
	}

	return clientSet, nil
}
