package util

import (
	"bytes"
	"encoding/json"
	"fmt"
	"os"
	"strings"

	"git.woa.com/kmetis/starship/cmd/tools/kube-proxy/ianvs"

	appv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/client-go/kubernetes"
)

const (
	KUBEPROXY_DAEMONSET_NAME = "kube-proxy"
	KUBEPROXY_CONTAINER_NAME = "kube-proxy"
)

func HandleCluster(clusterId, clusterFile string, processFunc func(kubernetes.Interface, string) error) error {
	// 检查参数
	if clusterId == "" && clusterFile == "" {
		return fmt.Errorf("must specify either --cluster or --file")
	}

	// 处理单个集群情况
	if clusterId != "" {
		client, err := ianvs.GetClusterClient(clusterId)
		if err != nil {
			return fmt.Errorf("failed to get client for cluster %s: %v", clusterId, err)
		}
		fmt.Printf("【集群Id：%s】\n", clusterId)
		return processFunc(client, clusterId)
	}

	// 处理集群文件情况
	content, err := os.ReadFile(clusterFile)
	if err != nil {
		return fmt.Errorf("failed to read cluster file: %v", err)
	}

	clusters := strings.Split(string(content), "\n")
	for _, cluster := range clusters {
		cluster = strings.TrimSpace(cluster)
		if cluster == "" {
			continue
		}

		client, err := ianvs.GetClusterClient(cluster)
		if err != nil {
			fmt.Printf("failed to get client for cluster %s: %v\n", cluster, err)
			continue
		}
		fmt.Printf("【集群Id：%s】\n", cluster)
		if err := processFunc(client, cluster); err != nil {
			fmt.Printf("failed to process cluster %s: %v\n", cluster, err)
		}
	}
	return nil
}

func GetHistoryMap(revisionList *appv1.ControllerRevisionList) (map[int64]*appv1.ControllerRevision, map[string]*appv1.ControllerRevision) {
	revisionMap := make(map[int64]*appv1.ControllerRevision)
	revisionHashMap := make(map[string]*appv1.ControllerRevision)
	for index, revision := range revisionList.Items {
		history := &revisionList.Items[index]
		revisionMap[revision.Revision] = history
		revisionHashMap[GetRevisionHashByControllerRevision(history)] = history
	}
	return revisionMap, revisionHashMap
}

func GetRevisionHashByControllerRevision(revision *appv1.ControllerRevision) string {
	return revision.Labels["controller-revision-hash"]
}

func GetDaemonSetGeneration(daemonSet *appv1.DaemonSet, revisionList []appv1.ControllerRevision) (*appv1.ControllerRevision, error) {
	for index, _ := range revisionList {
		history := &revisionList[index]
		if match, err := DaemonSetMatch(daemonSet, history); err != nil {
			return nil, err
		} else if match {
			return history, nil
		}
	}
	return nil, fmt.Errorf("Revision Not Found")
}

func DaemonSetMatch(ds *appv1.DaemonSet, history *appv1.ControllerRevision) (bool, error) {
	patch, err := GetDaemonSetPatch(ds)
	if err != nil {
		return false, err
	}
	return bytes.Equal(patch, history.Data.Raw), nil
}

func GetDaemonSetPatch(ds *appv1.DaemonSet) ([]byte, error) {
	dsBytes, err := json.Marshal(ds)
	if err != nil {
		return nil, err
	}
	var raw map[string]interface{}
	err = json.Unmarshal(dsBytes, &raw)
	if err != nil {
		return nil, err
	}
	objCopy := make(map[string]interface{})
	specCopy := make(map[string]interface{})

	// Create a patch of the DaemonSet that replaces spec.template
	spec := raw["spec"].(map[string]interface{})
	template := spec["template"].(map[string]interface{})
	specCopy["template"] = template
	template["$patch"] = "replace"
	objCopy["spec"] = specCopy
	patch, err := json.Marshal(objCopy)
	return patch, err
}

func GetRevisionHashByPod(pod *corev1.Pod) string {
	return pod.Labels["controller-revision-hash"]
}

func GetKubeProxyContainerFromDaemonSet(daemonSet *appv1.DaemonSet) *corev1.Container {
	for index, container := range daemonSet.Spec.Template.Spec.Containers {
		if container.Name == KUBEPROXY_CONTAINER_NAME {
			return &daemonSet.Spec.Template.Spec.Containers[index]
		}
	}
	return nil
}
