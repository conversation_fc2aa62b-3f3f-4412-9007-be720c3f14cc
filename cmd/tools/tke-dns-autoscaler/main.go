package main

import (
	"context"
	"flag"
	"fmt"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"

	ianvs "git.woa.com/ianvs/ianvs-sdk/pkg/client"
)

var token *string
var user *string
var cluster *string
var useragent *string

func init() {
	token = flag.String("token", "", "ianvs token")
	user = flag.String("user", "", "ianvs user")
	cluster = flag.String("cluster", "", "cluster id")
	useragent = flag.String("useragent", "tke-application-controller", "useragent")

	flag.Parse()
}

func k8sClient(cls, token, user, ua string) (kubernetes.Interface, error) {
	client := ianvs.NewTmpTokenClient(user, token)
	restConfig, restConfigErr := client.GetRestConfig(cls)
	restConfig.UserAgent = ua
	if restConfigErr != nil {
		return nil, restConfigErr
	}

	return kubernetes.NewForConfig(restConfig)
}

type check struct {
	Name   string `json:"name"`
	Result bool   `json:"result"`
	Reason string `json:"reason"`
}

/*
 annotations:
   meta.helm.sh/release-name: dnsautoscaler
   meta.helm.sh/release-namespace: kube-system
 labels:
   app.kubernetes.io/managed-by: Helm



*/

func main() {
	if *token == "" || *cluster == "" || *user == "" {
		panic("token, cluster, user is empty")
	}

	client, err := k8sClient(*cluster, *token, *user, *useragent)
	if err != nil {
		fmt.Println("Get k8sClient err:", err)
		return
	}

	rst := []check{}
	dp, err := client.AppsV1().Deployments("kube-system").Get(context.TODO(), "tke-dns-autoscaler", metav1.GetOptions{})
	if err != nil {
		rst = append(rst, check{"Deployments", false, err.Error()})
	} else {
		if dp.Labels["app.kubernetes.io/managed-by"] != "Helm" ||
			dp.Annotations["meta.helm.sh/release-name"] != "dnsautoscaler" ||
			dp.Annotations["meta.helm.sh/release-namespace"] != "kube-system" {
			rst = append(rst, check{"Deployments", false, "metadata not correct"})
		} else {
			rst = append(rst, check{"Deployments", true, ""})
		}
	}

	sa, err := client.CoreV1().ServiceAccounts("kube-system").Get(context.TODO(), "tke-dns-autoscaler", metav1.GetOptions{})
	if err != nil {
		rst = append(rst, check{"ServiceAccounts", false, err.Error()})
	} else {
		if sa.Labels["app.kubernetes.io/managed-by"] != "Helm" ||
			sa.Annotations["meta.helm.sh/release-name"] != "dnsautoscaler" ||
			sa.Annotations["meta.helm.sh/release-namespace"] != "kube-system" {
			rst = append(rst, check{"ServiceAccounts", false, "metadata not correct"})
		} else {
			rst = append(rst, check{"ServiceAccounts", true, ""})
		}
	}

	role, err := client.RbacV1().ClusterRoles().Get(context.TODO(), "tke-dns-autoscaler", metav1.GetOptions{})
	if err != nil {
		rst = append(rst, check{"ClusterRoles", false, err.Error()})
	} else {
		if role.Labels["app.kubernetes.io/managed-by"] != "Helm" ||
			role.Annotations["meta.helm.sh/release-name"] != "dnsautoscaler" ||
			role.Annotations["meta.helm.sh/release-namespace"] != "kube-system" {
			rst = append(rst, check{"ClusterRoles", false, "metadata not correct"})
		} else {
			rst = append(rst, check{"ClusterRoles", true, ""})
		}
	}

	roleBinding, err := client.RbacV1().ClusterRoleBindings().Get(context.TODO(), "tke-dns-autoscaler", metav1.GetOptions{})
	if err != nil {
		rst = append(rst, check{"ClusterRoleBindings", false, err.Error()})
	} else {
		if roleBinding.Labels["app.kubernetes.io/managed-by"] != "Helm" ||
			roleBinding.Annotations["meta.helm.sh/release-name"] != "dnsautoscaler" ||
			roleBinding.Annotations["meta.helm.sh/release-namespace"] != "kube-system" {
			rst = append(rst, check{"ClusterRoleBindings", false, "metadata not correct"})
		} else {
			rst = append(rst, check{"ClusterRoleBindings", true, ""})
		}
	}

	if pass, reason := getCheckRst(rst); !pass {
		fmt.Printf("%s check failed: %s\n", *cluster, reason)
	} else {
		fmt.Printf("%s check pass\n", *cluster)
	}
}

func getCheckRst(rst []check) (bool, string) {
	for _, v := range rst {
		if !v.Result {
			return false, v.Name + " " + v.Reason
		}
	}
	return true, ""
}
