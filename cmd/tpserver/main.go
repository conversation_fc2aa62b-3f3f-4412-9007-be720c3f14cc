// Package main implements a server for Greeter service.
package main

import (
	"flag"
	"fmt"
	"net"
	"net/http"

	grpcprom "github.com/grpc-ecosystem/go-grpc-prometheus"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"google.golang.org/grpc"
	"k8s.io/klog/v2"

	tpserver "git.woa.com/kmetis/starship/cmd/tpserver/server"
	pb "git.woa.com/kmetis/starship/pb"
)

var (
	port        = flag.Int("port", 50058, "The server port")
	metricsPort = flag.Int("metrics-port", 50059, "The metrics port")
)

func main() {
	klog.InitFlags(nil)
	defer klog.Flush()

	flag.Parse()

	// create grpc server
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", *port))
	if err != nil {
		klog.Fatalf("failed to listen: %v", err)
	}

	// Setup metrics
	customBuckets := []float64{0.001, 0.01, 0.1, 0.3, 0.6, 1, 3, 6, 9, 20, 30, 60, 90, 120}
	grpcprom.EnableHandlingTimeHistogram(
		grpcprom.WithHistogramBuckets(customBuckets),
	)
	s := grpc.NewServer(
		grpc.UnaryInterceptor(grpcprom.UnaryServerInterceptor),
		grpc.StreamInterceptor(grpcprom.StreamServerInterceptor),
	)
	grpcprom.Register(s)
	pb.RegisterTPTaskEngineServer(s, tpserver.NewTPServer())

	go func() error {
		m := http.NewServeMux()
		m.Handle("/metrics", promhttp.HandlerFor(prometheus.DefaultGatherer, promhttp.HandlerOpts{}))
		httpSrv := &http.Server{Handler: m, Addr: fmt.Sprintf(":%d", *metricsPort)}
		klog.Infof("metrics listening at %s", httpSrv.Addr)
		return httpSrv.ListenAndServe()
	}()

	klog.Infof("server listening at %v", lis.Addr())
	if err := s.Serve(lis); err != nil {
		klog.Fatalf("failed to serve: %v", err)
	}
}
