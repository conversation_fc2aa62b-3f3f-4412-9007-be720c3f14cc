package server

import (
	"context"
	"fmt"
	"sync"
	"time"

	"k8s.io/klog/v2"

	"git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/util"
)

type risk struct {
	app_name string
	name     string
	resrouce string
	code     string
	detail   string
	level    string
	solution string
}

type task struct {
	taskId    string
	state     string
	risks     []*risk
	action    string
	extender  string
	imageTag  string
	workload  string
	clusterId string
	region    string
	token     string

	status     string
	code       int32
	reason     string
	createTime time.Time
}

// server is used to implement helloworld.GreeterServer.
type TPServer struct {
	task map[string]*task
	lock sync.RWMutex

	taskUpdater *Timer

	pb.UnimplementedTPTaskEngineServer
}

// IsComponentHealthy checks if component is healthy
func (s *TPServer) CreateTask(ctx context.Context, in *pb.TPCreateTaskRequest) (*pb.TPCreateTaskReply, error) {
	klog.Infof("thirdParty CreateTask: %s", in.String())

	s.lock.Lock()
	defer s.lock.Unlock()

	key := in.ClusterId + "/" + in.Workload + "/" + in.ImageTag
	if _, ok := s.task[key]; ok {
		fmt.Printf("task %s has been created.\n", key)
		return &pb.TPCreateTaskReply{
			TaskId: key,
			Code:   0,
		}, nil
	}

	item := &task{
		// taskId:    in.TaskId,
		// state:     in.State,
		// risks:     in.Risks,
		action:    in.Action,
		extender:  in.Extender,
		imageTag:  in.ImageTag,
		workload:  in.Workload,
		clusterId: in.ClusterId,
		region:    in.Region,
		// token:     in.Token,
		// code:      in.Code,
		// reason:    in.Reason,

		createTime: time.Now(),
	}

	s.task[key] = item
	klog.Infof("task %s create successfully.", key)
	return &pb.TPCreateTaskReply{
		TaskId: key,
		Code:   0,
	}, nil
}

func (s *TPServer) DescribeTask(ctx context.Context, in *pb.TPDescribeTaskRequest) (*pb.TPDescribeTaskReply, error) {
	klog.Infof("thirdParty DescribeTask: %s", in.String())

	s.lock.RLock()
	defer s.lock.RUnlock()

	if item, ok := s.task[in.TaskId]; !ok {
		return &pb.TPDescribeTaskReply{
			TaskId: in.TaskId,
			Code:   1,
			Reason: "Not Found",
		}, nil
	} else {
		return &pb.TPDescribeTaskReply{
			TaskId: in.TaskId,
			Code:   0,
			Status: item.status,
		}, nil
	}
}

func (s *TPServer) CancelTask(ctx context.Context, in *pb.TPCancelTaskRequest) (*pb.TPCancelTaskReply, error) {
	klog.Infof("thirdParty CancelTask: %s", in.String())

	s.lock.Lock()
	defer s.lock.Unlock()

	if _, ok := s.task[in.TaskId]; ok {
		return &pb.TPCancelTaskReply{
			TaskId: in.TaskId,
			Code:   1,
			Reason: "Not Found",
		}, nil
	} else {
		delete(s.task, in.TaskId)
		return &pb.TPCancelTaskReply{
			TaskId: in.TaskId,
			Code:   0,
		}, nil
	}
}

func (s *TPServer) updateTaskStatus() {
	s.lock.Lock()
	defer s.lock.Unlock()

	for key, item := range s.task {
		if item.status == "" && time.Now().Sub(item.createTime) > time.Minute {
			item.status = util.TaskStatusDone
			fmt.Printf("Update task %s status to %s\n", key, item.status)
		} else {
			fmt.Printf("Skip update task %s\n", key)
		}
	}
}

func NewTPServer() pb.TPTaskEngineServer {
	tp := TPServer{
		task: make(map[string]*task),
		lock: sync.RWMutex{},
	}
	tp.taskUpdater = NewTimer("tasks-updater", 30, tp.updateTaskStatus)
	go tp.taskUpdater.Run()

	return &tp
}
