[db]
dbUser = starship_rw
dbPasswd = cipher:starship_rw:v1:Rz/ER5vDXAdpf3b25V7eIr2K6AQ3CKqv2Wsid6lb+XEPpUY1g6nHsVa7CLcqHPfD
dbHost = ***********
dbPort = 3306
dbDatabase = starship

[rainbow]
url = http://api.rainbow.woa.com:8080
appId = 4d127961-5bc8-4015-b0b7-55c194936ef2
userId = 4a01b76e41b243e2b334f1fb7f5ef985
userKey = 4e0daa93eed146c149534032fe6486209e84
group = app
env = Test
region = qy

[clsEventConfig]
region = ap-guangzhou
topic = c5526e08-7c20-4ba1-b9cb-d3adb8b7728a
secretId = AKIDqdCXgbHiy2rS1WlyznAIBpJgojPq20jT
secretKey = cipher:starship_rw:v1:ep+H4CSKinU+rHOJG1NoKNJ1oPflwP7iWBcCZJSPdM5x8186jtVNm7MGj5S3OnR7uoiCEbLrQ1gVFzjtsYFVtw==

[client]
tkePlatformClientCAFile = /root/certs/tke-platform-api/platform-client.pem
tkePlatformClientKeyFile = /root/certs/tke-platform-api/platform-client-key.pem
tkeBackendServer = https://tke-native-platform-api.tke-qy:9443
eksClientCAFile = /root/certs/eks-platform-api/eks-platform-client.pem
eksClientKeyFile = /root/certs/eks-platform-api/eks-platform-client-key.pem
eksBackendServer = https://tke-platform-api.eks-qy:30201
dashboardServer = http://dashboard.tke-qy:80
tkeCloudGWServer = http://tke-cloud-gw.tke-qy:8889

[debug]
debug = true