---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: starship
  namespace: tke-qy

---
apiVersion: apps/v1
kind: Deployment
metadata:
  labels:
    app: starship
  name: starship
  namespace: tke-qy
spec:
  progressDeadlineSeconds: 600
  replicas: 1
  revisionHistoryLimit: 10
  selector:
    matchLabels:
      app: starship
  strategy:
    rollingUpdate:
      maxSurge: 25%
      maxUnavailable: 25%
    type: RollingUpdate
  template:
    metadata:
      creationTimestamp: null
      labels:
        app: starship
    spec:
      containers:
      - args:
        - --port=50051
        command:
        - starship
        image: ccr.ccs.tencentyun.com/kmetis/starship:v0.0.1
        imagePullPolicy: Always
        name: starship
        resources:
          limits:
            cpu: 200m
            memory: 500Mi
          requests:
            cpu: 50m
            memory: 200Mi
        env:
          - name: TKE_MONITOR_GRPC_SERVER_ADDR
            value: tke-monitor.tke-qy.svc.cluster.local:5267
          - name: TKE_NODE_HEALTH_GRPC_SERVER_ADDR
            value: node-service.tke-qy.svc.cluster.local:8090
          - name: POD_NAMESPACE
            valueFrom:
              fieldRef:
                apiVersion: v1
                fieldPath: metadata.namespace
          - name: LONG_REGION
            value: ap-qingyuan
          image: mirrors.tencent.com/tkeimages/starship:v0.0.1-rc84
        terminationMessagePath: /dev/termination-log
        terminationMessagePolicy: File
        volumeMounts:
        - mountPath: /etc/localtime
          name: tz-config
      dnsPolicy: ClusterFirst
      restartPolicy: Always
      schedulerName: default-scheduler
      securityContext: {}
      serviceAccount: starship
      serviceAccountName: starship
      terminationGracePeriodSeconds: 30
      volumes:
      - hostPath:
          path: /etc/localtime
          type: ""
        name: tz-config
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    meta.helm.sh/release-name: starship
    meta.helm.sh/release-namespace: tke-qy
  labels:
    app.kubernetes.io/instance: starship
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: starship
  name: starship
  namespace: tke-qy
spec:
  ports:
    - name: grpc
      port: 50051
      protocol: TCP
      targetPort: 50051
  selector:
    app: starship
  sessionAffinity: None
  type: ClusterIP
---
apiVersion: v1
kind: Service
metadata:
  annotations:
    meta.helm.sh/release-name: starship
    meta.helm.sh/release-namespace: tke-qy
  labels:
    app.kubernetes.io/instance: starship
    app.kubernetes.io/managed-by: Helm
    app.kubernetes.io/name: starship
  name: starship-lb
  namespace: tke-qy
spec:
  ports:
    - name: grpc
      port: 50051
      protocol: TCP
      targetPort: 50051
  selector:
    app: starship
  sessionAffinity: None
  type: LoadBalancer
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: starship
rules:
- apiGroups:
  - batch
  resources:
  - jobs
  verbs:
  - '*'
- apiGroups:
  - coordination.k8s.io
  resources:
  - leases
  verbs:
  - create
  - delete
  - get
  - patch
  - update
- apiGroups:
  - ""
  resources:
  - configmaps
  - configmaps/finalizers
  verbs:
  - '*'
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: starship
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: starship
subjects:
- kind: ServiceAccount
  name: starship