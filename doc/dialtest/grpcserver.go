package server

import (
	"context"
	"net"
	"time"

	"google.golang.org/grpc"

	"git.woa.com/tke-library/log"
	"git.woa.com/tke/tke-monitor/pkg/monitor"
	"git.woa.com/tke/tke-monitor/pkg/pb"
)

type monitorServer struct {
	pb.UnimplementedMonitorServer
}

func (s *monitorServer) DialTest(_ context.Context, req *pb.DialTestRequest) (*pb.DialTestResponse, error) {
	if avgDelay, maxDelay, results, successCount, failedCount, err := monitor.ClusterHealthCheck("grpc", req.Product.String(), req.Object.String(), time.Duration(req.SingleTimeout)*time.Millisecond, time.Duration(req.Interval)*time.Millisecond, req.TotalCount, req.ClusterId); err != nil {
		return nil, err
	} else {
		resp := &pb.DialTestResponse{
			MaxDelay:     uint32(maxDelay / time.Millisecond),
			AvgDelay:     uint32(avgDelay / time.Millisecond),
			SuccessCount: successCount,
			FailedCount:  failedCount,
			Results:      results,
		}
		return resp, nil
	}
}

func StartGrpcServer(addr string) {
	lis, err := net.Listen("tcp", addr)
	if err != nil {
		log.WithError(err).Fatal("failed to listen")
	}
	var opts []grpc.ServerOption
	grpcServer := grpc.NewServer(opts...)
	pb.RegisterMonitorServer(grpcServer, &monitorServer{})
	log.Info("start grpc server")
	grpcServer.Serve(lis)
}
