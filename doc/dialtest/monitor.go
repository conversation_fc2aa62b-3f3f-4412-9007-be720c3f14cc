/*
 * TKE Copyright (C) by 2014 THL A29 Limited, a Tencent company
 *
 * Tencent Kubernetes Engine(TKE) is licensed under a
 * Creative Commons Attribution-NonCommercial-NoDerivatives 4.0 International License.
 *
 * You should have received a copy of the license along with this
 * work. If not, see <http://creativecommons.org/licenses/by-nc-nd/4.0/>.
 */

package k8s

import (
	"context"
	"time"

	"git.woa.com/tke-library/log"
	v1 "k8s.io/api/core/v1"
	metaV1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
)

const (
	ApiserverObject = "apiserver"
	EtcdObject      = "etcd"
)

// IsClusterAlive checks whether the cluster api server and ETCD
// server are alive based on the given cluster instance ID and AppID
func IsClusterAlive(object string, clientSet *kubernetes.Clientset) (time.Duration, error) {
	start := time.Now()
	switch object {
	case ApiserverObject:
		if _, err := clientSet.ServerVersion(); err != nil {
			return 0, err
		}
	case EtcdObject:
		if _, err := clientSet.CoreV1().Namespaces().Get(context.TODO(), v1.NamespaceDefault, metaV1.GetOptions{}); err != nil {
			return 0, err
		}
	}
	return time.Since(start), nil
}

func GetClusterNodeList(clientSet *kubernetes.Clientset) ([]v1.Node, error) {
	timeout := int64(120)
	nodesList, err := clientSet.CoreV1().Nodes().List(context.TODO(), metaV1.ListOptions{
		TimeoutSeconds: &timeout,
	})
	if err != nil {
		log.WithError(err).Error("Failed to get node list in cluster")
		return nil, err
	}
	return nodesList.Items, nil
}
