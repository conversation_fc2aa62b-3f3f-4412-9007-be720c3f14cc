# MasterCRD 组件参数与环境变量动态变更功能说明

## 功能简介

本特性支持通过 mastercrd 策略为 Master CRD（apiserver、controller-manager、scheduler）三大核心组件动态增删改启动参数（extraArgs）和环境变量（env），参数变更格式与 deployment.go 完全一致。

## 适用场景
- 需要在不重建集群的情况下，动态调整 master 组件的启动参数或环境变量。
- 支持参数的增、删、改操作。
- 支持参数/env 的回滚。

## 使用方式

### 1. 参数传递入口
- 通过 `TaskStrategyRequest.Extend.Args` 和 `TaskStrategyRequest.Extend.Envs` 传递参数。
- 结构与 deployment.go 一致。

#### Args 示例
```json
"Args": [
  {"type": "update", "value": "--max-requests-inflight=1000"},
  {"type": "remove", "value": "--feature-gates"},
  {"type": "update", "value": "--new-flag=true"}
]
```
- `type` 支持 `update`（有则改，无则加）、`remove`（删除指定参数）。
- `value` 为完整参数字符串。

#### Envs 示例
```json
"Envs": [
  {"type": "add", "key": "ENV1", "value": "value1"},
  {"type": "remove", "key": "ENV2"},
  {"type": "add", "key": "ENV3", "value": "value3"}
]
```
- `type` 支持 `add`（有则改，无则加）、`remove`（删除指定环境变量）。
- `key` 为环境变量名，`value` 为环境变量值。

### 2. 组件选择
- 通过 `TaskStrategyRequest.Component` 指定目标组件：
  - `apiserver`
  - `controller-manager`
  - `scheduler`

### 3. 回滚支持
- 每次参数/env 变更会自动记录 revision，回滚时会恢复到变更前的完整参数/env 状态。

### 4. 兼容性
- 变更格式与 deployment.go 完全一致。
- 旧 revision 只保存镜像 tag 时，回滚仅恢复镜像。

## 示例

#### 请求体片段
```json
{
  "Component": "apiserver",
  "Extend": {
    "Args": [
      {"type": "update", "value": "--max-requests-inflight=2000"},
      {"type": "remove", "value": "--feature-gates"}
    ],
    "Envs": [
      {"type": "add", "key": "MY_ENV", "value": "my_value"},
      {"type": "remove", "key": "OLD_ENV"}
    ]
  }
}
```

## 注意事项
- 仅支持 apiserver、controller-manager、scheduler 三个组件。
- 参数/env 变更立即生效，建议在维护窗口操作。
- 回滚时会恢复所有参数/env 到变更前状态。

## 参考
- deployment.go 的参数/env 变更格式
- master.yaml CRD 结构 