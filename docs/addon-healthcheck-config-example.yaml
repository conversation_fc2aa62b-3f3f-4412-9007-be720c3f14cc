# Addon健康检查配置示例
# 此配置应放置在七彩石配置管理系统中的健康检查规则中
# 配置路径: components/addon/healthchecks/rules/addon

# 最大重试次数
# 默认值: 3
# 说明: 当addon检查失败时，最多重试的次数
maxRetries: 5

# 重试间隔时间（秒）
# 默认值: 10
# 说明: 每次重试之间的等待时间
retryIntervalSeconds: 15

# 配置说明:
# 1. 这些配置参数会从 rule.Value 中解析
# 2. 如果没有配置或配置无效，将使用默认值
# 3. NotFound错误固定返回WARNING级别，无需配置
# 4. 对于生产环境，建议设置较高的重试次数
# 5. 对于测试环境，可以设置较低的值以加快检查速度

# 使用示例:
# 在七彩石中，该配置会被解析为 HealthCheckRule.Value:
# {
#   "maxRetries": 5,
#   "retryIntervalSeconds": 15
# }
