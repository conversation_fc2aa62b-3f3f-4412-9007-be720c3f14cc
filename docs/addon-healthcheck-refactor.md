# Addon健康检查配置驱动改造文档

## 改造概述

本次改造将 `pkg/healthcheck/addon/ready.go` 中的硬编码健康检查逻辑改为配置驱动的动态行为，参照 `crossVersionDowngrade` 的实现方式，从 `rule.Value` 中解析配置参数。

## 改造内容

### 1. 简化配置结构体

```go
type AddonHealthCheckConfig struct {
    MaxRetries    int           // 最大重试次数
    RetryInterval time.Duration // 重试间隔时间
}
```

### 2. 配置参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `maxRetries` | int | 3 | 最大重试次数 |
| `retryIntervalSeconds` | int | 10 | 每次重试之间的等待时间（秒） |

### 3. 配置获取方式

- **配置来源**: 从 `rule.Value` 中解析，而非单独的配置文件
- **配置路径**: `components/addon/healthchecks/rules/addon`
- **解析方式**: 使用 `rule.GetValue()` 方法获取配置值

### 4. 主要改动

#### 4.1 移除硬编码逻辑

**改造前**：
```go
if errors.IsNotFound(err) {
    klog.V(5).Infof("addon check not found: %s", baseLog)
    rsp.Level = healthcheck.RISK_LEVEL_WARNING
    rsp.Code = healthcheck.HEALTHCHECK_CODE_FAILED
    return util.WrapSingleResult(rsp, nil)
}
```

**改造后**：
```go
// 从rule.Value中解析配置参数
config := c.parseHealthCheckConfig(request, baseLog)

// 使用配置驱动的重试逻辑
return c.checkAddonWithRetry(ctx, appclient, request, rsp, config, baseLog)
```

#### 4.2 新增配置解析方法

```go
func (c *AddonChecker) parseHealthCheckConfig(request *strategyprovider.TaskStrategyRequest, baseLog string) *AddonHealthCheckConfig {
    // 默认配置
    config := &AddonHealthCheckConfig{
        MaxRetries:    defaultMaxRetries,
        RetryInterval: defaultRetryInterval,
    }

    // 获取健康检查规则
    rule := request.GetHealthCheckRule(ProviderName)
    if rule == nil {
        return config
    }

    // 解析检测次数
    if maxRetries, err := rule.GetValue("maxRetries"); err == nil {
        if retries, ok := maxRetries.(float64); ok && retries > 0 {
            config.MaxRetries = int(retries)
        } else if retries, ok := maxRetries.(int); ok && retries > 0 {
            config.MaxRetries = retries
        }
    }

    // 解析检测间隔时长（秒）
    if retryIntervalSeconds, err := rule.GetValue("retryIntervalSeconds"); err == nil {
        if interval, ok := retryIntervalSeconds.(float64); ok && interval > 0 {
            config.RetryInterval = time.Duration(interval) * time.Second
        } else if interval, ok := retryIntervalSeconds.(int); ok && interval > 0 {
            config.RetryInterval = time.Duration(interval) * time.Second
        }
    }

    return config
}
```

#### 4.3 简化重试逻辑

- 移除复杂的超时控制和上下文管理
- 保持原有的NotFound错误处理方式（固定返回WARNING）
- 使用简单的 `time.Sleep()` 进行重试间隔控制

### 5. NotFound错误处理

保持原有的处理方式，固定返回WARNING级别：
```go
if errors.IsNotFound(err) {
    rsp.Level = healthcheck.RISK_LEVEL_WARNING
    rsp.Code = healthcheck.HEALTHCHECK_CODE_FAILED
    rsp.Detail = fmt.Sprintf("addon not found (attempt %d/%d)", attempt, config.MaxRetries)
    rsp.Solution = "请检查addon是否已正确安装，或确认是否为非addon组件变更"
    return util.WrapSingleResult(rsp, nil)
}
```

## 配置示例

```yaml
# 七彩石配置路径: components/addon/healthchecks/rules/addon
maxRetries: 5
retryIntervalSeconds: 15
```

## 兼容性保证

1. **向后兼容**: 如果没有配置规则，将使用默认值
2. **类型兼容**: 支持int和float64类型的配置值
3. **接口兼容**: 保持原有的 `IsReady` 接口不变

## 使用场景

### 生产环境配置建议
```yaml
maxRetries: 5
retryIntervalSeconds: 30
```

### 测试环境配置建议
```yaml
maxRetries: 2
retryIntervalSeconds: 5
```

## 日志输出示例

### 配置解析日志
```
addon health check config: maxRetries=5, retryInterval=15s, traceId:xxx
```

### 重试日志
```
addon check attempt 1/5, traceId:xxx
addon check failed on attempt 1/5, will retry after 15s: connection refused, traceId:xxx
addon check attempt 2/5, traceId:xxx
addon check pass on attempt 2, traceId:xxx
```

### NotFound处理日志
```
addon check not found: traceId:xxx
```

## 测试覆盖

1. **配置解析测试**: 验证从rule.Value中正确解析配置参数
2. **默认值测试**: 验证没有配置时使用默认值
3. **类型转换测试**: 验证int和float64类型的正确处理
4. **重试逻辑测试**: 验证重试次数和间隔控制
5. **NotFound处理测试**: 验证固定的WARNING返回

## 部署说明

1. 在七彩石配置管理系统中创建健康检查规则
2. 配置路径：`components/addon/healthchecks/rules/addon`
3. 配置格式：YAML
4. 重启相关服务使配置生效

## 监控和告警

建议监控以下指标：
- 健康检查重试次数
- NotFound错误频率
- 配置解析失败次数
