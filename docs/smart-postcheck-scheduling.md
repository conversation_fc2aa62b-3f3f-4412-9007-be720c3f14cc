# Starship 智能后检调度 - 基于 EndTime 的简化方案

## 概述

基于对原有复杂实现的重新审视，我们采用了一个更简洁、更可靠的智能后检调度方案。新方案基于 upgrade 任务的 `EndTime` 字段统一处理同步和异步场景，避免了过度设计和复杂的事件通知机制。

## 核心设计思路

### 统一的调度逻辑

无论是同步任务还是异步任务，都基于以下统一逻辑：

```
第N个后检任务开始时间 = upgrade.EndTime + N * 后检延迟时间
```

具体计算：
- 第1个后检任务：`upgrade.EndTime + 1 * postCheckDelay`
- 第2个后检任务：`upgrade.EndTime + 2 * postCheckDelay`  
- 第3个后检任务：`upgrade.EndTime + 3 * postCheckDelay`

### 实现优势

1. **简洁性**：移除了复杂的调度器工厂、事件通知机制等
2. **可靠性**：直接基于数据库中的实际完成时间，避免内存缓存和事件丢失
3. **统一性**：同步和异步任务使用相同的调度逻辑
4. **实时性**：upgrade 完成后立即触发后检时间更新

## 技术实现

### 1. 任务创建阶段

在 `infra/task_operate.go` 的 `buildSubtasks` 函数中：

```go
// 创建后检子任务时，设置为远未来时间
futureTime := now.Add(24 * time.Hour)
for i := 0; i < postCheckCount; i++ {
    subTasks = append(subTasks, buildSubtask(task.ID, task.AppName, v, pkgutil.TaskStatusPending, futureTime))
}
```

### 2. 智能调度核心函数

`UpdatePostCheckStartTimes` 函数负责基于 upgrade EndTime 更新后检开始时间：

```go
func UpdatePostCheckStartTimes(parentTaskId int64) error {
    // 1. 获取 upgrade 子任务
    upgradeTask, err := GetSubTaskByAction(parentTaskId, pkgutil.TaskActionUpgrade)
    
    // 2. 检查 upgrade 是否完成且有 EndTime
    if upgradeTask.Status != pkgutil.TaskStatusDone || upgradeTask.EndTime == nil {
        return nil
    }
    
    // 3. 获取所有后检子任务并更新开始时间
    for i, postCheckTask := range postCheckTasks {
        delaySeconds := postCheckDelay * (i + 1)
        newStartTime := upgradeTask.EndTime.Add(time.Duration(delaySeconds) * time.Second)
        // 更新数据库...
    }
}
```

### 3. 触发时机

#### 同步任务
在 `pkg/task/engine.go` 的 upgrade 函数中：
```go
// 对于同步任务，立即触发智能后检调度
triggerSmartPostCheckScheduling(subTask)
```

#### 异步任务  
在 `pkg/task/task.go` 的任务上报函数中：
```go
// 如果是upgrade任务完成，触发智能后检调度
if req.Action == pkgutil.TaskActionUpgrade && req.GetStatus() == pkgutil.TaskStatusDone {
    triggerAsyncPostCheckScheduling(task, newSubTask)
}
```

### 4. 后检执行判断

在 `pkg/task/engine_helper.go` 中简化了后检执行判断逻辑：

```go
func shouldExecutePostCheck(postCheckTask *infra.StarshipSubtask, upgradeTask *infra.StarshipSubtask) bool {
    // 1. 检查任务状态
    if postCheckTask.Status != pkgutil.TaskStatusPending {
        return false
    }
    
    // 2. 检查执行时间
    if time.Now().Before(postCheckTask.StartTime) {
        return false
    }
    
    // 3. 检查 upgrade 是否完成
    if upgradeTask != nil && upgradeTask.Status != pkgutil.TaskStatusDone {
        // 尝试触发智能调度
        go infra.UpdatePostCheckStartTimes(postCheckTask.ParentTaskId)
        return false
    }
    
    return true
}
```

## 配置管理

后检延迟时间从七彩石配置中获取：

```go
func getPostCheckDelay(appName, strategy string) int {
    // 从配置获取延迟时间
    if strategy == util.ReleaseStrategyAddon {
        delay = rainutil.GetComponentDataInt(componentsConfig, "addonUpgradePostCheckDelay")
    } else {
        delay = rainutil.GetComponentDataInt(componentsConfig, "upgradePostCheckDelay")
    }
    
    // 边界值处理
    if delay <= 0 {
        delay = 60 // 默认60秒
    } else if delay > 600 {
        delay = 600 // 最大10分钟
    }
    
    return delay
}
```

## 工作流程

### 同步任务场景

1. **任务创建**：后检任务设置为远未来时间
2. **Upgrade执行**：同步执行，完成时设置 EndTime
3. **智能调度**：立即调用 `UpdatePostCheckStartTimes` 更新后检开始时间
4. **后检执行**：按新的开始时间执行后检任务

### 异步任务场景

1. **任务创建**：后检任务设置为远未来时间
2. **Upgrade执行**：异步执行，状态为 processing
3. **任务上报**：异步任务完成时上报结果，设置 EndTime
4. **智能调度**：上报时触发 `UpdatePostCheckStartTimes` 更新后检开始时间
5. **后检执行**：按新的开始时间执行后检任务

## 测试验证

### 单元测试

```bash
# 运行智能调度相关测试
go test -v ./infra/ -run "TestSmartPostCheckSchedulingLogic"
```

### 时间计算验证

```go
func TestSmartPostCheckSchedulingLogic(t *testing.T) {
    upgradeEndTime := time.Now()
    postCheckDelay := 60
    
    // 第一个后检任务：EndTime + 60s
    firstStart := upgradeEndTime.Add(60 * time.Second)
    
    // 第二个后检任务：EndTime + 120s  
    secondStart := upgradeEndTime.Add(120 * time.Second)
    
    // 验证计算正确性...
}
```

## 监控和日志

### 关键日志

```
# 智能调度触发
"Successfully triggered smart postcheck scheduling: parent-task-id:123"

# 时间更新
"Updated postcheck start time based on upgrade EndTime: subtask-id:456, upgrade-end:2024-01-01T12:00:00Z, new-start:2024-01-01T12:01:00Z, delay:60s"

# 执行判断
"Should execute postcheck, subtask-id:456, parent-task-id:123"
```

### 监控指标

可以通过现有的任务监控体系观察：
- 后检任务的执行时机准确性
- upgrade 完成到后检开始的时间间隔
- 后检任务的成功率

## 与原方案对比

| 方面 | 原复杂方案 | 新简化方案 |
|------|------------|------------|
| 代码复杂度 | 高（多个包，复杂接口） | 低（单一函数） |
| 依赖关系 | 循环依赖问题 | 无循环依赖 |
| 可靠性 | 依赖内存缓存和事件 | 基于数据库实际数据 |
| 维护成本 | 高 | 低 |
| 功能完整性 | 过度设计 | 恰好满足需求 |

## 总结

新的基于 EndTime 的智能后检调度方案通过简化设计，实现了：

1. **更高的可靠性**：直接基于数据库中的实际完成时间
2. **更好的可维护性**：代码简洁，逻辑清晰
3. **统一的处理逻辑**：同步和异步任务使用相同的调度算法
4. **更强的实时性**：upgrade 完成后立即更新后检时间

这个方案证明了"简单即是美"的设计哲学，通过移除不必要的复杂性，我们得到了一个更加健壮和易于理解的解决方案。
