# 任务超时时间配置示例
# 此配置应放置在七彩石配置管理系统中
# 配置路径: components/{component}/config

# 示例1: apiserver组件配置
# 配置路径: components/apiserver/config
timeout: 60  # 超时时间（分钟）
location: user
namespace: kube-system
workloadType: deployment
workloadName:
  tke: kube-apiserver
  eks: eks-apiserver

---

# 示例2: scheduler组件配置  
# 配置路径: components/scheduler/config
timeout: 45  # 超时时间（分钟）
location: user
namespace: kube-system
workloadType: deployment
workloadName:
  tke: kube-scheduler
  eks: eks-scheduler

---

# 示例3: controller-manager组件配置
# 配置路径: components/controller-manager/config
timeout: 90  # 超时时间（分钟）
location: user
namespace: kube-system
workloadType: deployment
workloadName:
  tke: kube-controller-manager
  eks: eks-controller-manager

---

# 示例4: etcd组件配置（DaemonSet策略）
# 配置路径: components/etcd/config
timeout: 2880  # 超时时间（分钟，48小时）
location: user
namespace: kube-system
workloadType: daemonset
workloadName:
  tke: etcd
  eks: etcd

---

# 示例5: addon组件配置
# 配置路径: components/addon/config
timeout: 120  # 超时时间（分钟）
location: user
namespace: kube-system

---

# 配置说明:
# 1. timeout: 任务超时时间，单位为分钟
# 2. 如果没有配置timeout字段，将使用基于策略的默认超时时间：
#    - 默认策略: 40分钟
#    - DaemonSet策略: 48小时（2880分钟）
#    - GRPC策略: 2小时（120分钟）
# 3. 配置的超时时间会覆盖策略默认值
# 4. 如果配置值无效（<=0），将回退到策略默认值
# 5. 其他字段（location、namespace、workloadType等）为组件的其他配置，与超时时间无关

# 推荐配置值:
# - 轻量级组件（如配置更新）: 30-60分钟
# - 中等复杂度组件（如API服务）: 60-120分钟  
# - 重量级组件（如存储、网络）: 120-240分钟
# - DaemonSet组件: 1440-2880分钟（24-48小时）
# - GRPC异步任务: 60-240分钟
