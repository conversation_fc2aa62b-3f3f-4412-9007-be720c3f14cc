# 任务超时时间配置化改造文档

## 改造概述

本次改造将 `pkg/task/engine_helper.go` 文件中的 `isExpiredTask` 函数从硬编码超时时间改为支持基于组件的配置化超时时间，同时保持向后兼容性。

## 改造内容

### 1. 主要修改

#### 1.1 `isExpiredTask` 函数改造

**改造前**：
```go
// 硬编码的超时时间
timeout := 40 * time.Minute
if parentTask.Strategy == pkgutil.ReleaseStrategyDaemonSet {
    timeout = 48 * time.Hour
}
if parentTask.Strategy == pkgutil.ReleaseStrategyGRPC {
    timeout = 2 * time.Hour
}
```

**改造后**：
```go
// 获取基于组件配置的超时时间
timeout := getComponentTimeout(parentTask)
```

#### 1.2 新增 `getComponentTimeout` 函数

```go
func getComponentTimeout(parentTask *infra.StarshipTask) time.Duration {
    // 默认超时时间（保持向后兼容）
    defaultTimeout := 40 * time.Minute
    
    // 基于策略的默认超时时间（保持现有逻辑）
    strategyTimeout := defaultTimeout
    if parentTask.Strategy == pkgutil.ReleaseStrategyDaemonSet {
        strategyTimeout = 48 * time.Hour
    }
    if parentTask.Strategy == pkgutil.ReleaseStrategyGRPC {
        strategyTimeout = 2 * time.Hour
    }

    // 尝试从组件配置中读取超时时间
    componentsConfigContent := util.GetRainbowData(fmt.Sprintf("components/%s/config", parentTask.AppName))
    if componentsConfigContent == "" {
        return strategyTimeout
    }

    componentsConfig := make(map[string]interface{})
    if err := yaml.Unmarshal([]byte(componentsConfigContent), &componentsConfig); err != nil {
        return strategyTimeout
    }

    // 从配置中读取超时时间（单位：分钟）
    timeoutMinutes := util.GetComponentDataInt(componentsConfig, "timeout")
    if timeoutMinutes <= 0 {
        return strategyTimeout
    }

    configuredTimeout := time.Duration(timeoutMinutes) * time.Minute
    return configuredTimeout
}
```

### 2. 配置获取逻辑

#### 2.1 配置路径
- **配置路径**: `components/{component}/config`
- **配置字段**: `timeout`（单位：分钟）

#### 2.2 配置优先级
1. **组件配置的timeout字段**（最高优先级）
2. **策略默认超时时间**（中等优先级）
3. **全局默认超时时间**（最低优先级，40分钟）

#### 2.3 回退机制
- 配置不存在 → 使用策略默认值
- 配置解析失败 → 使用策略默认值
- 配置值无效（≤0） → 使用策略默认值

### 3. 兼容性保证

#### 3.1 向后兼容
- 保持原有的函数签名不变
- 保持原有的策略默认超时时间逻辑
- 没有配置时的行为与改造前完全一致

#### 3.2 策略默认值
| 策略类型 | 默认超时时间 | 说明 |
|---------|-------------|------|
| 默认策略 | 40分钟 | 普通部署策略 |
| DaemonSet | 48小时 | DaemonSet部署策略 |
| GRPC | 2小时 | GRPC异步任务策略 |

### 4. 错误处理

#### 4.1 配置读取失败
```go
if componentsConfigContent == "" {
    klog.V(5).Infof("component config not found for %s, using strategy-based timeout: %v", parentTask.AppName, strategyTimeout)
    return strategyTimeout
}
```

#### 4.2 配置解析失败
```go
if err := yaml.Unmarshal([]byte(componentsConfigContent), &componentsConfig); err != nil {
    klog.Warningf("failed to unmarshal component config for %s: %v, using strategy-based timeout: %v", parentTask.AppName, err, strategyTimeout)
    return strategyTimeout
}
```

#### 4.3 配置值无效
```go
if timeoutMinutes <= 0 {
    klog.Warningf("invalid timeout configuration for component %s: %d minutes, using strategy-based timeout: %v", parentTask.AppName, timeoutMinutes, strategyTimeout)
    return strategyTimeout
}
```

### 5. 日志增强

#### 5.1 超时日志增强
在任务过期日志中添加超时时间信息：
```go
klog.Errorf("task %d expired, phase:%s, parent task update time by utc:%s, current time by utc:%s, timeout:%v",
    parentTask.ID, parentTask.Phase, parentUpdateTimeUTC, timeNowUTC, timeout)
```

#### 5.2 配置使用日志
```go
klog.V(5).Infof("using configured timeout for component %s: %v", parentTask.AppName, configuredTimeout)
```

## 配置示例

### 基本配置
```yaml
# 配置路径: components/apiserver/config
timeout: 60  # 超时时间（分钟）
location: user
namespace: kube-system
workloadType: deployment
```

### 不同组件的推荐配置
```yaml
# API服务组件
timeout: 60

# 存储组件
timeout: 120

# DaemonSet组件（覆盖策略默认值）
timeout: 1440  # 24小时

# 轻量级组件
timeout: 30
```

## 测试覆盖

### 1. 单元测试
- **默认值测试**: 验证没有配置时使用策略默认值
- **配置解析测试**: 验证正确解析配置中的timeout字段
- **无效配置测试**: 验证无效配置时的回退逻辑
- **策略覆盖测试**: 验证配置值覆盖策略默认值
- **向后兼容测试**: 验证改造后的行为与原有逻辑兼容

### 2. 集成测试
- **任务过期判断**: 验证使用配置超时时间的任务过期判断
- **配置更新**: 验证配置更新后的实时生效
- **错误场景**: 验证各种错误场景的处理

## 部署说明

### 1. 配置部署
1. 在七彩石配置管理系统中为需要自定义超时时间的组件添加配置
2. 配置路径：`components/{component}/config`
3. 添加 `timeout` 字段，单位为分钟

### 2. 代码部署
1. 部署包含改造的代码版本
2. 无需重启服务，配置会实时生效
3. 监控日志确认配置正确加载

### 3. 验证步骤
1. 检查日志中的配置加载信息
2. 验证任务超时判断使用了正确的超时时间
3. 测试配置更新的实时生效

## 监控和告警

### 1. 监控指标
- 配置加载失败次数
- 配置解析失败次数
- 使用自定义超时时间的任务数量
- 任务过期频率变化

### 2. 告警规则
- 配置加载失败率过高
- 任务过期率异常变化
- 超时时间配置异常（过长或过短）

## 风险评估

### 1. 低风险
- **向后兼容**: 完全保持原有行为
- **渐进式改造**: 可以逐步为组件添加配置
- **回退机制**: 配置失败时自动回退到原有逻辑

### 2. 注意事项
- **配置合理性**: 需要合理设置超时时间，避免过长或过短
- **监控重要性**: 需要监控配置变更对任务执行的影响
- **文档维护**: 需要维护各组件的推荐超时时间配置
