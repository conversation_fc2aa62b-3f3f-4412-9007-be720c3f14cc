# 任务超时时间配置使用指南

## 快速开始

### 1. 为组件配置自定义超时时间

在七彩石配置管理系统中，为你的组件添加超时配置：

**配置路径**: `components/{component}/config`

**配置示例**:
```yaml
timeout: 90  # 超时时间（分钟）
location: user
namespace: kube-system
workloadType: deployment
# ... 其他配置项
```

### 2. 验证配置生效

查看日志确认配置已正确加载：
```
using configured timeout for component apiserver: 1h30m0s
```

## 配置详解

### 超时时间计算优先级

1. **组件配置** (最高优先级)
   ```yaml
   # components/apiserver/config
   timeout: 60  # 使用60分钟
   ```

2. **策略默认值** (中等优先级)
   - 普通策略: 40分钟
   - DaemonSet策略: 48小时
   - GRPC策略: 2小时

3. **全局默认值** (最低优先级)
   - 40分钟

### 配置验证规则

- ✅ **有效配置**: `timeout: 60` (正整数)
- ❌ **无效配置**: `timeout: 0` 或 `timeout: -10` (≤0的值)
- ❌ **缺失配置**: 没有`timeout`字段

无效或缺失的配置会自动回退到策略默认值。

## 实际使用场景

### 场景1: API服务组件
```yaml
# components/apiserver/config
timeout: 60  # API服务通常60分钟足够
location: user
namespace: kube-system
workloadType: deployment
```

### 场景2: 存储组件
```yaml
# components/etcd/config  
timeout: 120  # 存储组件需要更长时间
location: user
namespace: kube-system
workloadType: daemonset
```

### 场景3: 覆盖DaemonSet默认值
```yaml
# components/node-agent/config
timeout: 1440  # 24小时，覆盖默认的48小时
location: user
namespace: kube-system
workloadType: daemonset
```

### 场景4: 快速测试环境
```yaml
# components/test-component/config
timeout: 15  # 测试环境使用较短超时时间
location: user
namespace: default
```

## 推荐配置值

| 组件类型 | 推荐超时时间 | 说明 |
|---------|-------------|------|
| API服务 | 30-90分钟 | apiserver, scheduler等 |
| 存储组件 | 60-180分钟 | etcd, storage等 |
| 网络组件 | 45-120分钟 | cni, proxy等 |
| 监控组件 | 30-60分钟 | metrics, logging等 |
| DaemonSet | 12-48小时 | 节点级组件 |
| 测试组件 | 10-30分钟 | 开发测试环境 |

## 故障排查

### 问题1: 配置不生效
**症状**: 任务仍使用默认超时时间
**排查步骤**:
1. 检查配置路径是否正确: `components/{component}/config`
2. 检查YAML格式是否正确
3. 检查`timeout`字段是否为正整数
4. 查看日志中的配置加载信息

### 问题2: 任务过早超时
**症状**: 任务在预期时间前就被标记为过期
**排查步骤**:
1. 检查配置的超时时间是否过短
2. 确认任务的实际执行时间需求
3. 考虑增加超时时间配置

### 问题3: 任务超时时间过长
**症状**: 失败的任务等待很久才被标记为过期
**排查步骤**:
1. 检查配置的超时时间是否过长
2. 根据组件特性调整合理的超时时间
3. 考虑使用策略默认值

## 监控和告警

### 关键日志
```bash
# 配置加载成功
using configured timeout for component apiserver: 1h0m0s

# 配置加载失败
component config not found for apiserver, using strategy-based timeout: 40m0s
failed to unmarshal component config for apiserver: yaml: unmarshal errors, using strategy-based timeout: 40m0s

# 任务超时
task 12345 expired, phase:upgrade, parent task update time by utc:2024-01-01 10:00:00, current time by utc:2024-01-01 11:30:00, timeout:1h30m0s
```

### 监控指标
- 配置加载成功/失败率
- 不同组件的平均任务执行时间
- 任务超时频率变化

## 最佳实践

### 1. 渐进式配置
- 先为关键组件配置超时时间
- 观察效果后逐步扩展到其他组件
- 根据实际执行时间调整配置

### 2. 环境差异化
- 生产环境使用较长超时时间
- 测试环境使用较短超时时间
- 开发环境可以使用最短超时时间

### 3. 定期审查
- 定期检查各组件的实际执行时间
- 根据业务变化调整超时配置
- 清理不再使用的配置

### 4. 文档维护
- 记录每个组件的超时时间配置原因
- 维护组件超时时间的变更历史
- 提供故障排查指南

## 配置模板

### 标准API服务组件
```yaml
timeout: 60
location: user
namespace: kube-system
workloadType: deployment
workloadName:
  tke: kube-{component}
  eks: eks-{component}
```

### 标准DaemonSet组件
```yaml
timeout: 1440  # 24小时
location: user
namespace: kube-system
workloadType: daemonset
workloadName:
  tke: {component}
  eks: {component}
```

### 标准测试组件
```yaml
timeout: 30
location: user
namespace: default
workloadType: deployment
```
