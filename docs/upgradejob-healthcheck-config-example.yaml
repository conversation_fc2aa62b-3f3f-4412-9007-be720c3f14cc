# UpgradeJob健康检查配置示例
# 此配置应放置在七彩石配置管理系统中的健康检查规则中
# 配置路径: components/upgradeJob/healthchecks/rules/upgradeJob

# 最大重试次数
# 默认值: 5
# 说明: 当upgradeJob检查失败时，最多重试的次数
maxRetries: 8

# 重试间隔时间（秒）
# 默认值: 30
# 说明: 每次重试之间的等待时间
retryIntervalSeconds: 45

# 配置说明:
# 1. 这些配置参数会从 rule.Value 中解析
# 2. 如果没有配置或配置无效，将使用默认值
# 3. upgradeJob的特点是升级过程可能需要较长时间，因此默认重试次数较多
# 4. 对于生产环境，建议设置较高的重试次数和间隔时间
# 5. 对于测试环境，可以设置较低的值以加快检查速度

# 使用示例:
# 在七彩石中，该配置会被解析为 HealthCheckRule.Value:
# {
#   "maxRetries": 8,
#   "retryIntervalSeconds": 45
# }

---

# 不同环境的推荐配置

# 生产环境配置
maxRetries: 10
retryIntervalSeconds: 60

# 测试环境配置  
maxRetries: 5
retryIntervalSeconds: 30

# 开发环境配置
maxRetries: 3
retryIntervalSeconds: 15

# 快速验证配置（仅用于快速测试）
maxRetries: 2
retryIntervalSeconds: 10

---

# 配置原理说明:

# upgradeJob健康检查的重试逻辑:
# 1. 获取UpgradeJob列表 - 如果失败则重试
# 2. 验证UpgradeJob数量 - 如果不是1个则重试  
# 3. 检查升级状态:
#    - 如果升级失败(有Reason) - 立即返回错误，不重试
#    - 如果升级完成(BatchCompleteNum > BatchOrder) - 返回成功
#    - 如果升级未完成 - 重试等待
# 4. 重试间隔: 使用配置的retryIntervalSeconds
# 5. 最大重试: 使用配置的maxRetries

# 重试场景:
# ✅ 网络错误导致无法获取UpgradeJob
# ✅ UpgradeJob数量不正确（可能还在创建中）
# ✅ 升级还在进行中，未完成
# ❌ 升级明确失败（不重试，立即返回错误）

# 配置建议:
# - maxRetries: 根据升级复杂度设置，复杂升级建议8-15次
# - retryIntervalSeconds: 根据升级速度设置，慢速升级建议60-120秒
# - 避免设置过小的值，可能导致过早放弃等待
# - 避免设置过大的值，可能导致长时间占用资源
