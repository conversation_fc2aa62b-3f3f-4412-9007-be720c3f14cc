# UpgradeJob健康检查配置驱动改造文档

## 改造概述

本次改造将 `pkg/healthcheck/upgradejob/ready.go` 中的单次检查逻辑改为配置驱动的重试机制，参照addon健康检查的实现方式，从 `rule.Value` 中解析配置参数。

## 改造内容

### 1. 配置结构体

```go
// UpgradeJobHealthCheckConfig upgradeJob健康检查配置
type UpgradeJobHealthCheckConfig struct {
    // MaxRetries 最大重试次数
    MaxRetries int
    // RetryInterval 重试间隔时间
    RetryInterval time.Duration
}
```

### 2. 配置参数说明

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `maxRetries` | int | 5 | 最大重试次数 |
| `retryIntervalSeconds` | int | 30 | 每次重试之间的等待时间（秒） |

### 3. 默认值设计

考虑到upgradeJob的特点，设置了适合的默认值：
- **默认重试次数**: 5次（比addon的3次更多，因为升级过程通常需要更长时间）
- **默认重试间隔**: 30秒（比addon的10秒更长，给升级过程更多时间）

### 4. 主要改动

#### 4.1 从单次检查到重试机制

**改造前**：
```go
func (c *UpgradeJobChecker) IsReady(ctx context.Context, request *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error) {
    // 单次检查，失败即返回
    ujs, err := appclient.ApplicationV1().UpgradeJobs(request.ClusterId).List(ctx, metav1.ListOptions{...})
    if err != nil {
        return util.WrapSingleResult(util.HandleError(rsp, "get upgradeJob failed", err, baseLog))
    }
    // 没有重试逻辑
}
```

**改造后**：
```go
func (c *UpgradeJobChecker) IsReady(ctx context.Context, request *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error) {
    // 从rule.Value中解析配置参数
    config := c.parseHealthCheckConfig(request, baseLog)
    
    // 使用配置驱动的重试逻辑
    return c.checkUpgradeJobWithRetry(ctx, appclient, request, rsp, config, baseLog)
}
```

#### 4.2 新增配置解析方法

```go
func (c *UpgradeJobChecker) parseHealthCheckConfig(request *strategyprovider.TaskStrategyRequest, baseLog string) *UpgradeJobHealthCheckConfig {
    // 默认配置
    config := &UpgradeJobHealthCheckConfig{
        MaxRetries:    defaultMaxRetries,
        RetryInterval: defaultRetryInterval,
    }

    // 获取健康检查规则
    rule := request.GetHealthCheckRule(ProviderName)
    if rule == nil {
        return config
    }

    // 解析检测次数和间隔时长
    // 支持int和float64类型的配置值
    // ...
}
```

#### 4.3 新增重试逻辑方法

```go
func (c *UpgradeJobChecker) checkUpgradeJobWithRetry(ctx context.Context, appclient *versioned.Clientset, request *strategyprovider.TaskStrategyRequest, rsp *strategyprovider.TaskStrategyReply, config *UpgradeJobHealthCheckConfig, baseLog string) ([]*strategyprovider.TaskStrategyReply, error) {
    for attempt := 1; attempt <= config.MaxRetries; attempt++ {
        // 重试逻辑
        // 1. 获取UpgradeJob列表
        // 2. 验证数量
        // 3. 检查状态
        // 4. 根据结果决定是否重试
    }
}
```

### 5. 重试逻辑设计

#### 5.1 重试场景
- ✅ **网络错误**: 无法获取UpgradeJob时重试
- ✅ **数量错误**: UpgradeJob数量不是1个时重试（可能还在创建中）
- ✅ **未完成**: 升级还在进行中时重试等待
- ❌ **升级失败**: 有明确失败原因时不重试，立即返回错误

#### 5.2 重试流程
```
尝试1 -> 失败 -> 等待30秒 -> 尝试2 -> 失败 -> 等待30秒 -> ... -> 尝试5 -> 最终失败
```

#### 5.3 日志记录
```go
// 重试日志
klog.V(5).Infof("upgradeJob check attempt %d/%d, %s", attempt, config.MaxRetries, baseLog)
klog.Warningf("upgradeJob get failed on attempt %d/%d, will retry after %v: %v, %s", ...)

// 成功日志
klog.V(5).Infof("upgradeJob check pass on attempt %d, %s", attempt, baseLog)

// 配置日志
klog.V(5).Infof("upgradeJob health check config: maxRetries=%d, retryInterval=%v, %s", ...)
```

### 6. 与Addon的一致性

#### 6.1 配置结构一致
- 相同的字段名称：`MaxRetries`、`RetryInterval`
- 相同的配置参数：`maxRetries`、`retryIntervalSeconds`
- 相同的类型支持：int和float64

#### 6.2 解析逻辑一致
- 相同的配置获取方式：`request.GetHealthCheckRule(ProviderName)`
- 相同的类型转换逻辑
- 相同的默认值回退机制

#### 6.3 日志格式一致
- 相同的日志级别和格式
- 相同的错误处理方式
- 相同的配置信息输出

## 配置示例

### 基本配置
```yaml
# 七彩石配置路径: components/upgradeJob/healthchecks/rules/upgradeJob
maxRetries: 8
retryIntervalSeconds: 45
```

### 环境差异化配置
```yaml
# 生产环境
maxRetries: 10
retryIntervalSeconds: 60

# 测试环境
maxRetries: 5
retryIntervalSeconds: 30

# 开发环境
maxRetries: 3
retryIntervalSeconds: 15
```

## 兼容性保证

### 1. 向后兼容
- 没有配置时使用合理的默认值
- 保持原有的函数签名不变
- 保持原有的错误处理逻辑

### 2. 配置验证
- 支持int和float64类型的配置值
- 无效配置时自动使用默认值
- 提供详细的配置加载日志

## 测试覆盖

### 1. 配置解析测试
- 默认值测试
- 有效配置解析测试
- 无效配置回退测试
- 不同类型值转换测试

### 2. 重试逻辑测试
- 重试次数控制测试
- 重试间隔控制测试
- 不同失败场景的重试行为测试

### 3. 一致性测试
- 与addon配置结构一致性测试
- 日志格式一致性测试

## 部署说明

### 1. 配置部署
1. 在七彩石配置管理系统中创建健康检查规则
2. 配置路径：`components/upgradeJob/healthchecks/rules/upgradeJob`
3. 配置格式：YAML
4. 重启相关服务使配置生效

### 2. 验证步骤
1. 检查日志中的配置加载信息
2. 验证重试逻辑是否按配置执行
3. 测试配置更新的实时生效

## 监控和告警

### 1. 关键指标
- 配置解析成功/失败率
- 平均重试次数
- 重试成功率
- upgradeJob检查耗时

### 2. 告警规则
- 配置解析失败率过高
- 重试次数异常增加
- upgradeJob检查超时频率异常

## 最佳实践

### 1. 配置建议
- **复杂升级**: maxRetries=8-15, retryIntervalSeconds=60-120
- **简单升级**: maxRetries=3-5, retryIntervalSeconds=30-60
- **快速测试**: maxRetries=2-3, retryIntervalSeconds=10-15

### 2. 监控建议
- 监控upgradeJob的平均完成时间
- 根据实际情况调整重试参数
- 定期审查配置的合理性

### 3. 故障排查
- 检查upgradeJob的创建和状态
- 分析重试失败的具体原因
- 根据业务需求调整配置参数
