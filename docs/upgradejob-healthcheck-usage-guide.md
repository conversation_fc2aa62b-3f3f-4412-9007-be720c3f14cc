# UpgradeJob健康检查配置使用指南

## 快速开始

### 1. 为upgradeJob配置自定义重试参数

在七彩石配置管理系统中，为upgradeJob健康检查添加重试配置：

**配置路径**: `components/upgradeJob/healthchecks/rules/upgradeJob`

**配置示例**:
```yaml
maxRetries: 8  # 最大重试次数
retryIntervalSeconds: 45  # 重试间隔时间（秒）
```

### 2. 验证配置生效

查看日志确认配置已正确加载：
```
upgradeJob health check config: maxRetries=8, retryInterval=45s, traceId:xxx
```

## 配置详解

### 重试逻辑优先级

1. **组件配置** (最高优先级)
   ```yaml
   # components/upgradeJob/healthchecks/rules/upgradeJob
   maxRetries: 8
   retryIntervalSeconds: 45
   ```

2. **默认值** (最低优先级)
   - 最大重试次数: 5次
   - 重试间隔: 30秒

### 配置验证规则

- ✅ **有效配置**: `maxRetries: 8` (正整数)
- ❌ **无效配置**: `maxRetries: 0` 或 `maxRetries: -5` (≤0的值)
- ❌ **缺失配置**: 没有相应字段

无效或缺失的配置会自动使用默认值。

## UpgradeJob特点分析

### 升级过程特点
- **时间较长**: 升级过程通常需要较长时间完成
- **状态变化**: 从创建 → 执行 → 完成的状态转换
- **批次处理**: 可能涉及多个批次的升级操作

### 重试场景
- ✅ **网络错误**: 无法获取UpgradeJob时重试
- ✅ **数量错误**: UpgradeJob数量不正确时重试（可能还在创建中）
- ✅ **未完成**: 升级还在进行中时重试等待
- ❌ **升级失败**: 有明确失败原因时不重试，立即返回错误

## 实际使用场景

### 场景1: 生产环境升级
```yaml
# components/upgradeJob/healthchecks/rules/upgradeJob
maxRetries: 10  # 生产环境需要更多重试机会
retryIntervalSeconds: 60  # 给升级过程更多时间
```

### 场景2: 测试环境升级
```yaml
# components/upgradeJob/healthchecks/rules/upgradeJob
maxRetries: 5  # 适中的重试次数
retryIntervalSeconds: 30  # 标准的重试间隔
```

### 场景3: 开发环境快速验证
```yaml
# components/upgradeJob/healthchecks/rules/upgradeJob
maxRetries: 3  # 较少的重试次数
retryIntervalSeconds: 15  # 较短的重试间隔
```

### 场景4: 复杂升级任务
```yaml
# components/upgradeJob/healthchecks/rules/upgradeJob
maxRetries: 15  # 更多重试次数应对复杂升级
retryIntervalSeconds: 90  # 更长间隔等待升级完成
```

## 推荐配置值

| 升级类型 | 推荐重试次数 | 推荐间隔时间 | 说明 |
|---------|-------------|-------------|------|
| 简单升级 | 3-5次 | 15-30秒 | 快速组件升级 |
| 标准升级 | 5-8次 | 30-45秒 | 常规组件升级 |
| 复杂升级 | 8-15次 | 45-90秒 | 大型组件或批量升级 |
| 生产升级 | 10-20次 | 60-120秒 | 生产环境谨慎升级 |
| 测试升级 | 3-8次 | 15-45秒 | 测试环境快速验证 |

## 故障排查

### 问题1: 配置不生效
**症状**: upgradeJob仍使用默认重试参数
**排查步骤**:
1. 检查配置路径是否正确: `components/upgradeJob/healthchecks/rules/upgradeJob`
2. 检查YAML格式是否正确
3. 检查字段名是否正确: `maxRetries`, `retryIntervalSeconds`
4. 查看日志中的配置加载信息

### 问题2: 升级检查过早失败
**症状**: upgradeJob在升级完成前就被标记为失败
**排查步骤**:
1. 检查重试次数是否足够
2. 检查重试间隔是否合理
3. 分析upgradeJob的实际完成时间
4. 考虑增加重试次数或间隔时间

### 问题3: 升级检查等待时间过长
**症状**: 失败的upgradeJob等待很久才被检测到
**排查步骤**:
1. 检查重试次数是否过多
2. 检查重试间隔是否过长
3. 分析upgradeJob的失败模式
4. 考虑减少重试参数

## 监控和告警

### 关键日志
```bash
# 配置加载成功
upgradeJob health check config: maxRetries=8, retryInterval=45s, traceId:xxx

# 重试过程
upgradeJob check attempt 1/8, traceId:xxx
upgradeJob get failed on attempt 1/8, will retry after 45s: connection refused, traceId:xxx
upgradeJob not completed on attempt 2/8, will retry after 45s, traceId:xxx

# 检查成功
upgradeJob check pass on attempt 3, traceId:xxx

# 最终失败
upgradeJob not completed, traceId:xxx
```

### 监控指标
- 配置解析成功/失败率
- 平均重试次数
- 重试成功率
- upgradeJob检查总耗时
- 不同重试次数的分布

## 最佳实践

### 1. 配置策略
- **保守配置**: 生产环境使用较多重试次数和较长间隔
- **激进配置**: 测试环境使用较少重试次数和较短间隔
- **动态调整**: 根据历史数据调整配置参数

### 2. 监控策略
- 监控upgradeJob的平均完成时间
- 分析重试失败的原因分布
- 跟踪配置变更对成功率的影响

### 3. 运维策略
- 定期审查配置的合理性
- 根据业务变化调整重试参数
- 建立配置变更的审批流程

### 4. 故障处理
- 建立upgradeJob失败的标准处理流程
- 准备常见问题的解决方案
- 维护故障排查知识库

## 配置模板

### 标准生产环境
```yaml
maxRetries: 10
retryIntervalSeconds: 60
```

### 标准测试环境
```yaml
maxRetries: 5
retryIntervalSeconds: 30
```

### 快速验证环境
```yaml
maxRetries: 3
retryIntervalSeconds: 15
```

### 复杂升级场景
```yaml
maxRetries: 15
retryIntervalSeconds: 90
```

## 与Addon健康检查的对比

| 特性 | Addon | UpgradeJob |
|------|-------|------------|
| 默认重试次数 | 3次 | 5次 |
| 默认重试间隔 | 10秒 | 30秒 |
| 配置方式 | rule.Value | rule.Value |
| 重试场景 | 网络错误、状态检查 | 网络错误、数量检查、状态等待 |
| 失败处理 | NotFound返回WARNING | 升级失败立即返回ERROR |

## 注意事项

1. **升级失败不重试**: 当upgradeJob明确失败时（有Reason），不会重试
2. **数量检查**: 只有找到唯一的upgradeJob才会继续检查状态
3. **状态等待**: 升级未完成时会重试等待，直到达到最大重试次数
4. **配置实时生效**: 配置更新后立即生效，无需重启服务
