apiVersion: master.cloud.tencent.com/v1alpha1
kind: Master
metadata:
  annotations:
    cloud.tencent.com/autoscale-enabled: "false"
    cloud.tencent.com/deprecate-insecure-port: "true"
    cloud.tencent.com/tke-cluster-appid: "**********"
    cloud.tencent.com/tke-cluster-cbs-provisioner-disabled: "true"
    cloud.tencent.com/tke-cluster-cloud-controller-manager-enabled: "true"
    cloud.tencent.com/tke-cluster-component-eni-annotation: '{"eni":{"spec":{"peerVpcAppId":**********,"peerVpcId":"vpc-jh529y2z","name":"cls-qz2lrosl","cvmOwnerAppId":1253687700,"cvmOwnerUin":"100005258832"},"status":{"mac":"","pip":"","eniId":"","mask":0}},"useBridge":true}'
    cloud.tencent.com/tke-cluster-dns-enabled: "false"
    cloud.tencent.com/tke-cluster-eni-mutating-webhook-enabled: "false"
    cloud.tencent.com/tke-cluster-ip-masq-agent-enabled: "true"
    cloud.tencent.com/tke-cluster-level: L20
    cloud.tencent.com/tke-cluster-region: ap-qingyuan
    cloud.tencent.com/tke-cluster-vpcid: vpc-jh529y2z
    cloud.tencent.com/tke-house-keeper-enabled: "true"
    cloud.tencent.com/tke-service-controller-reuse-flag: "false"
    cloud.tencent.com/tke-vpc-cidr: ***********/16
    master.cloud.tencent.com/phasefinalizers: service-controller,hpa-metrics-server,cbs-provisioner,dns,ingress-controller,ip-masq-agent
    master.cloud.tencent.com/resource-version: "0"
    qcloud.conf: '{"region":"qy","regionName":"ap-qingyuan","zone":"","vpcID":"vpc-jh529y2z","QCloudSecretId":"","QCloudSecretKey":"","nodeNameType":"lan-ip"}'
  creationTimestamp: "2025-06-21T13:41:30Z"
  finalizers:
  - master.cloud.tencent.com/master-operator
  generation: 8
  labels:
    cloud.tencent.com/tke-cluster-id: cls-qz2lrosl
    cloud.tencent.com/tke-operator-managed: "true"
    cloud.tencent.com/tke-operator-version: 0.2.0
    master.cloud.tencent.com/operator-version: 0.2.0
  managedFields:
  - apiVersion: master.cloud.tencent.com/v1alpha1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:phaseFinalizers: {}
    manager: tke-operator
    operation: Update
    time: "2025-06-21T13:42:01Z"
  - apiVersion: master.cloud.tencent.com/v1alpha1
    fieldsType: FieldsV1
    fieldsV1:
      f:status:
        f:phase: {}
    manager: master-operator
    operation: Update
    time: "2025-06-21T13:42:45Z"
  - apiVersion: master.cloud.tencent.com/v1alpha1
    fieldsType: FieldsV1
    fieldsV1:
      f:metadata:
        f:annotations:
          .: {}
          f:cloud.tencent.com/autoscale-enabled: {}
          f:cloud.tencent.com/deprecate-insecure-port: {}
          f:cloud.tencent.com/tke-cluster-appid: {}
          f:cloud.tencent.com/tke-cluster-cbs-provisioner-disabled: {}
          f:cloud.tencent.com/tke-cluster-cloud-controller-manager-enabled: {}
          f:cloud.tencent.com/tke-cluster-component-eni-annotation: {}
          f:cloud.tencent.com/tke-cluster-dns-enabled: {}
          f:cloud.tencent.com/tke-cluster-eni-mutating-webhook-enabled: {}
          f:cloud.tencent.com/tke-cluster-ip-masq-agent-enabled: {}
          f:cloud.tencent.com/tke-cluster-level: {}
          f:cloud.tencent.com/tke-cluster-region: {}
          f:cloud.tencent.com/tke-cluster-vpcid: {}
          f:cloud.tencent.com/tke-house-keeper-enabled: {}
          f:cloud.tencent.com/tke-service-controller-reuse-flag: {}
          f:cloud.tencent.com/tke-vpc-cidr: {}
          f:master.cloud.tencent.com/phasefinalizers: {}
          f:qcloud.conf: {}
        f:finalizers: {}
        f:labels:
          .: {}
          f:cloud.tencent.com/tke-cluster-id: {}
          f:cloud.tencent.com/tke-operator-managed: {}
          f:cloud.tencent.com/tke-operator-version: {}
          f:master.cloud.tencent.com/operator-version: {}
      f:spec:
        .: {}
        f:apiServer:
          .: {}
          f:advertiseAddress: {}
          f:disableGenArgs: {}
          f:env: {}
          f:extraArgs: {}
          f:extraConfigMaps: {}
          f:hyperKube: {}
          f:image: {}
          f:insecurePort: {}
          f:livenessProbe:
            .: {}
            f:failureThreshold: {}
            f:httpGet:
              .: {}
              f:path: {}
              f:port: {}
              f:scheme: {}
            f:periodSeconds: {}
            f:successThreshold: {}
            f:timeoutSeconds: {}
          f:pod:
            .: {}
            f:affinity:
              .: {}
              f:nodeAffinity:
                .: {}
                f:preferredDuringSchedulingIgnoredDuringExecution: {}
              f:podAntiAffinity:
                .: {}
                f:preferredDuringSchedulingIgnoredDuringExecution: {}
            f:annotations:
              .: {}
              f:qcloud.CNI_CONFIG_INFO: {}
              f:tke.cloud.tencent.com/networks: {}
            f:hostAliases: {}
            f:labels:
              .: {}
              f:cloud.tencent.com/tke-cluster-level: {}
              f:kube-apiserver: {}
              f:qcloud-app: {}
            f:minReadySeconds: {}
          f:readinessProbe:
            .: {}
            f:failureThreshold: {}
            f:httpGet:
              .: {}
              f:path: {}
              f:port: {}
              f:scheme: {}
            f:periodSeconds: {}
            f:successThreshold: {}
            f:timeoutSeconds: {}
          f:resources:
            .: {}
            f:limits:
              .: {}
              f:cpu: {}
              f:memory: {}
              f:tke.cloud.tencent.com/eni: {}
            f:requests:
              .: {}
              f:cpu: {}
              f:memory: {}
              f:tke.cloud.tencent.com/eni: {}
          f:securePort: {}
          f:tls:
            .: {}
            f:apiserverExtraSans: {}
        f:controllerManager:
          .: {}
          f:disableGenArgs: {}
          f:env: {}
          f:extraArgs: {}
          f:extraConfigMaps: {}
          f:hyperKube: {}
          f:image: {}
          f:livenessProbe:
            .: {}
            f:exec:
              .: {}
              f:command: {}
            f:initialDelaySeconds: {}
            f:successThreshold: {}
            f:timeoutSeconds: {}
          f:pod:
            .: {}
            f:affinity:
              .: {}
              f:nodeAffinity:
                .: {}
                f:preferredDuringSchedulingIgnoredDuringExecution: {}
              f:podAntiAffinity:
                .: {}
                f:preferredDuringSchedulingIgnoredDuringExecution: {}
            f:annotations:
              .: {}
              f:qcloud.CNI_CONFIG_INFO: {}
              f:tke.cloud.tencent.com/networks: {}
            f:hostAliases: {}
            f:labels:
              .: {}
              f:cloud.tencent.com/tke-cluster-level: {}
            f:minReadySeconds: {}
          f:resources:
            .: {}
            f:limits:
              .: {}
              f:cpu: {}
              f:memory: {}
              f:tke.cloud.tencent.com/eni: {}
            f:requests:
              .: {}
              f:cpu: {}
              f:memory: {}
              f:tke.cloud.tencent.com/eni: {}
        f:etcd:
          .: {}
          f:clientCertsSecrets: {}
          f:servers: {}
        f:network:
          .: {}
          f:clusterDomain: {}
          f:nodeCidrMaskSize: {}
          f:podCidr: {}
          f:serviceCidr: {}
        f:scheduler:
          .: {}
          f:disableGenArgs: {}
          f:env: {}
          f:extraArgs: {}
          f:extraConfigMaps: {}
          f:hyperKube: {}
          f:image: {}
          f:livenessProbe:
            .: {}
            f:exec:
              .: {}
              f:command: {}
            f:initialDelaySeconds: {}
            f:successThreshold: {}
            f:timeoutSeconds: {}
          f:pod:
            .: {}
            f:affinity:
              .: {}
              f:nodeAffinity:
                .: {}
                f:preferredDuringSchedulingIgnoredDuringExecution: {}
              f:podAntiAffinity:
                .: {}
                f:preferredDuringSchedulingIgnoredDuringExecution: {}
            f:annotations:
              .: {}
              f:tke.cloud.tencent.com/networks: {}
            f:hostAliases: {}
            f:labels:
              .: {}
              f:cloud.tencent.com/tke-cluster-level: {}
            f:minReadySeconds: {}
          f:resources:
            .: {}
            f:limits:
              .: {}
              f:cpu: {}
              f:memory: {}
            f:requests:
              .: {}
              f:cpu: {}
              f:memory: {}
        f:version: {}
      f:status: {}
    manager: nightsWatch
    operation: Update
    time: "2025-06-21T13:43:58Z"
  name: cls-qz2lrosl
  namespace: cls-qz2lrosl
  resourceVersion: "2399552731"
  uid: b338750f-c6e6-492c-b93a-9728467f9dae
spec:
  apiServer:
    advertiseAddress: **************
    disableGenArgs: true
    env:
    - name: CLUSTER_ID
      value: cls-qz2lrosl
    - name: APPID
      value: "**********"
    - name: PATH
      value: /usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/
    extraArgs:
    - advertise-address=**************
    - allow-privileged=true
    - authorization-mode=RBAC,Node
    - bind-address=0.0.0.0
    - client-ca-file=/etc/kubernetes/files/apiserver/cluster.crt
    - enable-admission-plugins=NamespaceLifecycle,LimitRanger,ServiceAccount,PersistentVolumeLabel,DefaultStorageClass,ResourceQuota,NodeRestriction,MutatingAdmissionWebhook,ValidatingAdmissionWebhook
    - enable-aggregator-routing=true
    - enable-bootstrap-token-auth=true
    - etcd-cafile=/etc/kubernetes/etcd/secrets/etcd-ca.crt
    - etcd-certfile=/etc/kubernetes/etcd/secrets/etcd-client.crt
    - etcd-keyfile=/etc/kubernetes/etcd/secrets/etcd-client.key
    - etcd-prefix=/cls-qz2lrosl
    - etcd-servers=https://qy-tke-vpc-etcd-23.kube-system.svc.cluster.local:2379
    - external-hostname=cls-qz2lrosl.ccs.tencent-cloud.com
    - feature-gates=ResolveExternalNameService=true
    - kubelet-client-certificate=/etc/kubernetes/files/apiserver/apiserver.crt
    - kubelet-client-key=/etc/kubernetes/files/apiserver/apiserver.key
    - kubelet-preferred-address-types=InternalIP,ExternalIP
    - proxy-client-cert-file=/etc/kubernetes/files/apiserver/apiserver.crt
    - proxy-client-key-file=/etc/kubernetes/files/apiserver/apiserver.key
    - requestheader-client-ca-file=/etc/kubernetes/files/apiserver/cluster.crt
    - requestheader-extra-headers-prefix=X-Remote-Extra-
    - requestheader-group-headers=X-Remote-Group
    - requestheader-username-headers=X-Remote-User
    - secure-port=60002
    - service-account-issuer=https://kubernetes.default.svc.cluster.local
    - service-account-key-file=/etc/kubernetes/files/apiserver/service-account.key
    - service-account-signing-key-file=/etc/kubernetes/files/apiserver/service-account.key
    - service-cluster-ip-range=***********/22
    - tls-cert-file=/etc/kubernetes/files/apiserver/apiserver.crt
    - tls-private-key-file=/etc/kubernetes/files/apiserver/apiserver.key
    - token-auth-file=/etc/kubernetes/known_tokens.csv
    extraConfigMaps:
    - mountAt: /etc/kubernetes
      name: cls-qz2lrosl-extra-config
    hyperKube: true
    image: ccr.ccs.tencentyun.com/tkeimages/hyperkube:v1.24.4-tke.28
    insecurePort: 60001
    livenessProbe:
      failureThreshold: 6
      httpGet:
        path: /healthz?exclude=etcd
        port: 60002
        scheme: HTTPS
      periodSeconds: 10
      successThreshold: 1
      timeoutSeconds: 10
    pod:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - preference:
              matchExpressions:
              - key: level
                operator: In
                values:
                - small
              - key: tke.cloud.tencent.com/component
                operator: In
                values:
                - normal
            weight: 10
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: master.cloud.tencent.com/name
                  operator: In
                  values:
                  - cls-qz2lrosl
                - key: master.cloud.tencent.com/component
                  operator: In
                  values:
                  - apiserver
              topologyKey: failure-domain.beta.kubernetes.io/zone
            weight: 50
          - podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: master.cloud.tencent.com/name
                  operator: In
                  values:
                  - cls-qz2lrosl
                - key: master.cloud.tencent.com/component
                  operator: In
                  values:
                  - apiserver
              topologyKey: kubernetes.io/hostname
            weight: 30
      annotations:
        qcloud.CNI_CONFIG_INFO: '{"eni":{"spec":{"peerVpcAppId":**********,"peerVpcId":"vpc-jh529y2z","name":"cls-qz2lrosl","cvmOwnerAppId":1253687700,"cvmOwnerUin":"100005258832"},"status":{"mac":"","pip":"","eniId":"","mask":0}},"useBridge":true}'
        tke.cloud.tencent.com/networks: tke-bridge,tke-eni,tke-route
      hostAliases:
      - hostnames:
        - cbs.api.qcloud.com
        - cvm.api.qcloud.com
        - lb.api.qcloud.com
        - snapshot.api.qcloud.com
        - monitor.api.qcloud.com
        - scaling.api.qcloud.com
        - ccs.api.qcloud.com
        - tag.api.qcloud.com
        ip: ************
      - hostnames:
        - tke.internal.tencentcloudapi.com
        - clb.internal.tencentcloudapi.com
        - cvm.internal.tencentcloudapi.com
        - tag.internal.tencentcloudapi.com
        - cbs.tencentcloudapi.com
        - cvm.tencentcloudapi.com
        - vpc.tencentcloudapi.com
        ip: ************
      labels:
        cloud.tencent.com/tke-cluster-level: L20
        kube-apiserver: "true"
        qcloud-app: kube-apiserver
      minReadySeconds: 45
    readinessProbe:
      failureThreshold: 6
      httpGet:
        path: /healthz?exclude=etcd
        port: 60002
        scheme: HTTPS
      periodSeconds: 10
      successThreshold: 1
      timeoutSeconds: 10
    resources:
      limits:
        cpu: "1"
        memory: 3Gi
        tke.cloud.tencent.com/eni: "1"
      requests:
        cpu: 250m
        memory: 1Gi
        tke.cloud.tencent.com/eni: "1"
    securePort: 60002
    tls:
      apiserverExtraSans:
      - *******
      - ************
      - **************
      - kubernetes-master
      - cls-qz2lrosl.ccs.tencent-cloud.com
      - cls-qz2lrosl.ccs.tencent-cloud.com
  controllerManager:
    disableGenArgs: true
    env:
    - name: CLUSTER_ID
      value: cls-qz2lrosl
    - name: APPID
      value: "**********"
    - name: PATH
      value: /usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/
    extraArgs:
    - allocate-node-cidrs=true
    - allow-untagged-cloud=true
    - authentication-kubeconfig=/etc/kubernetes/files/controller-manager/kubeconfig.controller-manager
    - authorization-kubeconfig=/etc/kubernetes/files/controller-manager/kubeconfig.controller-manager
    - cluster-cidr=*********/16
    - cluster-name=cls-qz2lrosl
    - cluster-signing-cert-file=/etc/kubernetes/files/controller-manager/cluster.crt
    - cluster-signing-key-file=/etc/kubernetes/files/controller-manager/cluster.key
    - configure-cloud-routes=true
    - kubeconfig=/etc/kubernetes/files/controller-manager/kubeconfig.controller-manager
    - node-cidr-mask-size=26
    - root-ca-file=/etc/kubernetes/files/controller-manager/cluster.crt
    - service-account-private-key-file=/etc/kubernetes/files/controller-manager/service-account.key
    - service-cluster-ip-range=***********/22
    - use-service-account-credentials=true
    - terminated-pod-gc-threshold=300
    extraConfigMaps:
    - mountAt: /etc/kubernetes
      name: cls-qz2lrosl-extra-config
    hyperKube: true
    image: ccr.ccs.tencentyun.com/tkeimages/hyperkube:v1.24.4-tke.28
    livenessProbe:
      exec:
        command:
        - bash
        - -c
        - curl -k --connect-timeout 5 -s https://cls-qz2lrosl-apiserver-service:60002/healthz?exclude=etcd
          | grep ok || exit 1
      initialDelaySeconds: 60
      successThreshold: 1
      timeoutSeconds: 5
    pod:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - preference:
              matchExpressions:
              - key: level
                operator: In
                values:
                - small
              - key: tke.cloud.tencent.com/component
                operator: In
                values:
                - normal
            weight: 10
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: master.cloud.tencent.com/name
                  operator: In
                  values:
                  - cls-qz2lrosl
                - key: master.cloud.tencent.com/component
                  operator: In
                  values:
                  - controller-manager
              topologyKey: failure-domain.beta.kubernetes.io/zone
            weight: 50
          - podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: master.cloud.tencent.com/name
                  operator: In
                  values:
                  - cls-qz2lrosl
                - key: master.cloud.tencent.com/component
                  operator: In
                  values:
                  - controller-manager
              topologyKey: kubernetes.io/hostname
            weight: 30
      annotations:
        qcloud.CNI_CONFIG_INFO: '{"eni":{"spec":{"peerVpcAppId":**********,"peerVpcId":"vpc-jh529y2z","name":"cls-qz2lrosl","cvmOwnerAppId":1253687700,"cvmOwnerUin":"100005258832"},"status":{"mac":"","pip":"","eniId":"","mask":0}},"useBridge":true}'
        tke.cloud.tencent.com/networks: tke-bridge,tke-eni,tke-route
      hostAliases:
      - hostnames:
        - cbs.api.qcloud.com
        - cvm.api.qcloud.com
        - lb.api.qcloud.com
        - snapshot.api.qcloud.com
        - monitor.api.qcloud.com
        - scaling.api.qcloud.com
        - ccs.api.qcloud.com
        - tag.api.qcloud.com
        ip: ************
      - hostnames:
        - tke.internal.tencentcloudapi.com
        - clb.internal.tencentcloudapi.com
        - cvm.internal.tencentcloudapi.com
        - tag.internal.tencentcloudapi.com
        - cbs.tencentcloudapi.com
        - cvm.tencentcloudapi.com
        - vpc.tencentcloudapi.com
        ip: ************
      labels:
        cloud.tencent.com/tke-cluster-level: L20
      minReadySeconds: 15
    resources:
      limits:
        cpu: 300m
        memory: 800Mi
        tke.cloud.tencent.com/eni: "1"
      requests:
        cpu: 100m
        memory: 200Mi
        tke.cloud.tencent.com/eni: "1"
  etcd:
    clientCertsSecrets: cls-qz2lrosl-etcd-certs
    servers:
    - https://qy-tke-vpc-etcd-23.kube-system.svc.cluster.local:2379
  network:
    clusterDomain: cluster.local
    nodeCidrMaskSize: 26
    podCidr: *********/16
    serviceCidr: ***********/22
  scheduler:
    disableGenArgs: true
    env:
    - name: CLUSTER_ID
      value: cls-qz2lrosl
    - name: APPID
      value: "**********"
    - name: PATH
      value: /usr/local/sbin:/usr/local/bin:/usr/sbin:/usr/bin:/sbin:/bin:/
    extraArgs:
    - authentication-kubeconfig=/etc/kubernetes/files/scheduler/kubeconfig.scheduler
    - authorization-kubeconfig=/etc/kubernetes/files/scheduler/kubeconfig.scheduler
    - config=/etc/kubernetes/scheduler-config.yaml
    - feature-gates=EnableCBSSkipProvision=true
    - kubeconfig=/etc/kubernetes/files/scheduler/kubeconfig.scheduler
    extraConfigMaps:
    - mountAt: /etc/kubernetes
      name: cls-qz2lrosl-extra-config
    hyperKube: true
    image: ccr.ccs.tencentyun.com/tkeimages/hyperkube:v1.24.4-tke.28
    livenessProbe:
      exec:
        command:
        - bash
        - -c
        - curl -k --connect-timeout 5 -s https://cls-qz2lrosl-apiserver-service:60002/healthz?exclude=etcd
          | grep ok || exit 1
      initialDelaySeconds: 60
      successThreshold: 1
      timeoutSeconds: 5
    pod:
      affinity:
        nodeAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - preference:
              matchExpressions:
              - key: level
                operator: In
                values:
                - small
              - key: tke.cloud.tencent.com/component
                operator: In
                values:
                - normal
            weight: 10
        podAntiAffinity:
          preferredDuringSchedulingIgnoredDuringExecution:
          - podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: master.cloud.tencent.com/name
                  operator: In
                  values:
                  - cls-qz2lrosl
                - key: master.cloud.tencent.com/component
                  operator: In
                  values:
                  - scheduler
              topologyKey: failure-domain.beta.kubernetes.io/zone
            weight: 50
          - podAffinityTerm:
              labelSelector:
                matchExpressions:
                - key: master.cloud.tencent.com/name
                  operator: In
                  values:
                  - cls-qz2lrosl
                - key: master.cloud.tencent.com/component
                  operator: In
                  values:
                  - scheduler
              topologyKey: kubernetes.io/hostname
            weight: 30
      annotations:
        tke.cloud.tencent.com/networks: tke-bridge,tke-route
      hostAliases:
      - hostnames:
        - cbs.api.qcloud.com
        - cvm.api.qcloud.com
        - lb.api.qcloud.com
        - snapshot.api.qcloud.com
        - monitor.api.qcloud.com
        - scaling.api.qcloud.com
        - ccs.api.qcloud.com
        - tag.api.qcloud.com
        ip: ************
      - hostnames:
        - tke.internal.tencentcloudapi.com
        - clb.internal.tencentcloudapi.com
        - cvm.internal.tencentcloudapi.com
        - tag.internal.tencentcloudapi.com
        - cbs.tencentcloudapi.com
        - cvm.tencentcloudapi.com
        - vpc.tencentcloudapi.com
        ip: ************
      labels:
        cloud.tencent.com/tke-cluster-level: L20
      minReadySeconds: 15
    resources:
      limits:
        cpu: 300m
        memory: 800Mi
      requests:
        cpu: 100m
        memory: 200Mi
  version: 1.24.4
status:
  phase: running
  phaseFinalizers: null
