# 示例：健康检查配置中添加Prometheus指标检查
# 文件路径：components/apiserver/healthchecks/config
defaultPreCheckPlugins:
  - blackList
  - daemonsetReady
  - deploymentReady
  - metricsCheck  # 添加Prometheus指标检查
defaultPostCheckPlugins:
  - blackList
  - daemonsetReady
  - deploymentReady
defaultRollbackSupportPlugins:
  - blackList
  - daemonsetReady
  - deploymentReady

---
# 示例：Prometheus指标检查规则配置
# 文件路径：components/apiserver/healthchecks/rules/metricsCheck
prometheusConfig:
  - name : "apiserver可用性检查"
    query: |
      sum(
        irate(apiserver_request_total{
          tke_cluster_instance_id=~"${clusterId}",
          code=~"20.*",
          verb=~"GET|LIST"
        }[5m])
      ) / sum(
        irate(apiserver_request_total{
          tke_cluster_instance_id=~"${clusterId}",
          code!~"404|400|409|403",
          verb=~"GET|LIST"
        }[5m])
      )
    threshold: 0.95
    operator: ">="
    datasource: "tke-user-apiserver"
  - name : "apiserverCPU使用检查"
    query: |
      sum by(pod) (
        rate(container_cpu_usage_seconds_total{
          region="${region}",
          container!="POD",
          image!="",
          namespace="${clusterId}",
          pod=~"(.*)apiserver(.*)",
          container="apiserver"
        }[2m])
      ) / on(pod) group_left() (
        sum by(pod) (
          kube_pod_container_resource_limits_cpu_cores{
            region="${region}",
            namespace="${clusterId}",
            pod=~"(.*)apiserver(.*)",
            container="apiserver"
          }
        )
      )
    threshold: 0.5
    operator: "<="
    datasource: "global-prometheus"
  - name : "apiserver内存使用检查"
    query: |
      sum by(pod) (
        container_memory_usage_bytes{
          region="${region}",
          container!="POD",
          image!="",
          namespace="${clusterId}",
          pod=~"(.*)apiserver(.*)",
          container="apiserver" 
        }
      ) / on(pod) group_left() (
        sum by(pod) (
          kube_pod_container_resource_limits_memory_bytes{
            namespace="${clusterId}",
            pod=~"(.*)apiserver(.*)",
            container="apiserver" 
          }
        )
      )
    threshold: 0.90
    operator: "<="
    datasource: "global-prometheus"
---




{
    "status": "success",
    "data": {
        "resultType": "vector",
        "result": [
            {
                "metric": {
                    "pod": "cls-fs9x0bv9-apiserver-5b96ffc78d-q77kt"
                },
                "value": [
                    1753338616.146,
                    "0.06182386796766271"
                ]
            },
            {
                "metric": {
                    "pod": "cls-fs9x0bv9-apiserver-5b96ffc78d-8bgzq"
                },
                "value": [
                    1753338616.146,
                    "0.09286150489933788"
                ]
            },
            {
                "metric": {
                    "pod": "cls-fs9x0bv9-apiserver-5b96ffc78d-hj2t4"
                },
                "value": [
                    1753338616.146,
                    "0.07260331227444113"
                ]
            }
        ]
    }
}