module git.woa.com/kmetis/starship

go 1.22.2

require (
	cloud.tencent.com/mervynwang/scheduler-tools v0.0.0-00010101000000-000000000000
	cloud.tencent.com/tke/master-operator v0.0.0-00010101000000-000000000000
	git.code.oa.com/rainbow/golang-sdk v0.5.5
	git.code.oa.com/rainbow/proto v1.94.0
	git.woa.com/ianvs/ianvs-sdk v1.1.0
	git.woa.com/k8s/node-service v0.0.0-20240511030321-b66609b0a5a4
	git.woa.com/kamp/camp-api v0.0.2-0.20250207031806-d46da3b4baa9
	git.woa.com/tad-dev/api v0.0.32
	git.woa.com/tencentcloud.hrt/dencrypt/enigmarotor v0.2.3
	git.woa.com/tke/tke-monitor v1.4.51
	github.com/google/uuid v1.6.0
	github.com/grpc-ecosystem/go-grpc-prometheus v1.2.0
	//github.com/grpc-ecosystem/go-grpc-middleware/providers/prometheus v1.0.1
	github.com/prometheus/client_golang v1.20.4
	github.com/spf13/viper v1.15.0
	github.com/stretchr/testify v1.9.0
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cls v1.0.1072
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common v1.0.1072
	golang.org/x/sync v0.9.0
	google.golang.org/grpc v1.67.1
	google.golang.org/protobuf v1.35.1
	gopkg.in/yaml.v2 v2.4.0
	gorm.io/driver/mysql v1.5.7
	gorm.io/gorm v1.25.12
	k8s.io/api v0.30.1
	k8s.io/apimachinery v0.30.1
	k8s.io/client-go v12.0.0+incompatible
	k8s.io/klog v1.0.0
	k8s.io/klog/v2 v2.130.1
	k8s.io/kubernetes v1.17.5
	sigs.k8s.io/yaml v1.4.0
	tkestack.io/tke v1.20.13-tke-application
)

require github.com/hashicorp/go-version v1.2.1

require (
	git.woa.com/kmetis/kmetis v0.0.0-00010101000000-000000000000
	github.com/spf13/cobra v1.8.0
	github.com/spf13/pflag v1.0.5
)

require (
	filippo.io/edwards25519 v1.1.0 // indirect
	git.code.oa.com/devsec/protoc-gen-secv v0.3.4 // indirect
	git.code.oa.com/polaris/polaris-go v0.12.12 // indirect
	git.code.oa.com/trpc-go/trpc v0.1.2 // indirect
	git.code.oa.com/trpc-go/trpc-go v0.18.3 // indirect
	git.code.oa.com/trpc-go/trpc-metrics-runtime v0.4.0 // indirect
	git.code.oa.com/trpc-go/trpc-naming-polaris v0.5.17 // indirect
	git.woa.com/KAMP/basic/tlog v0.0.1 // indirect
	git.woa.com/KAMP/crd/selfhealing v0.0.3 // indirect
	git.woa.com/STKE/basic/quota/quota_server v0.0.0-20240722104050-1aa5a9d411a0 // indirect
	git.woa.com/STKE/protobuf/trpc-go/basic/quota/quota_server v0.0.0-20240801083042-4735e9b92927 // indirect
	git.woa.com/STKE/protobuf/trpc-go/common/quota/cluster v0.0.0-20240801081856-ba26559715e0 // indirect
	git.woa.com/STKE/protobuf/trpc-go/common/quota/project v0.0.0-20240801080215-24765e7d5c33 // indirect
	git.woa.com/STKE/protobuf/trpc-go/common/quota/resource v0.0.0-20240801080215-24765e7d5c33 // indirect
	git.woa.com/STKE/statefulsetplus-operator-api v1.23.1 // indirect
	git.woa.com/baicaoyuan/moss v0.13.2 // indirect
	git.woa.com/jce/jce v1.2.0 // indirect
	git.woa.com/kamp/apis v0.0.4 // indirect
	git.woa.com/kateway/multi-cluster-ingress-api v0.0.0-20240417072558-d91feb8ea033 // indirect
	git.woa.com/kateway/tke-service-config v1.0.17 // indirect
	git.woa.com/polaris/polaris-go/v2 v2.6.4 // indirect
	git.woa.com/polaris/polaris-server-api/api/metric v1.0.2 // indirect
	git.woa.com/polaris/polaris-server-api/api/monitor v1.0.7 // indirect
	git.woa.com/polaris/polaris-server-api/api/v1/grpc v1.0.2 // indirect
	git.woa.com/polaris/polaris-server-api/api/v1/model v1.1.6 // indirect
	git.woa.com/polaris/polaris-server-api/api/v2/grpc v1.0.0 // indirect
	git.woa.com/polaris/polaris-server-api/api/v2/model v1.0.4 // indirect
	git.woa.com/tad-dev/cronappa/api v0.0.0-20240102122114-5567c57b97b2 // indirect
	git.woa.com/tad-dev/workflow/api v0.0.0-20240314085324-18ee0358672e // indirect
	git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/cam v1.0.586 // indirect
	git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/common v1.0.842 // indirect
	git.woa.com/tencentcloud-internal/tencentcloud-sdk-go/tencentcloud/eb v1.0.782 // indirect
	git.woa.com/tke/logconfig v0.2.19 // indirect
	git.woa.com/tke/net/tke-service-config v1.0.16 // indirect
	git.woa.com/trpc-go/go_reuseport v1.7.0 // indirect
	git.woa.com/trpc-go/tnet v0.0.17 // indirect
	git.woa.com/zhiyan-log/agent/protocol v1.0.0 // indirect
	github.com/BurntSushi/toml v1.4.0 // indirect
	github.com/Masterminds/semver/v3 v3.2.1 // indirect
	github.com/NYTimes/gziphandler v1.1.1 // indirect
	github.com/andybalholm/brotli v1.1.0 // indirect
	github.com/antlr/antlr4/runtime/Go/antlr/v4 v4.0.0-20230512164433-5d1fd1a340c9 // indirect
	github.com/argoproj/argo-workflows/v3 v3.5.1 // indirect
	github.com/asaskevich/govalidator v0.0.0-20200907205600-7a23bdc65eef // indirect
	github.com/beorn7/perks v1.0.1 // indirect
	github.com/blang/semver/v4 v4.0.0 // indirect
	github.com/cenkalti/backoff/v4 v4.3.0 // indirect
	github.com/cespare/xxhash/v2 v2.3.0 // indirect
	github.com/clusternet/clusternet v0.16.0 // indirect
	github.com/containerd/containerd v1.6.18 // indirect
	github.com/containerd/ttrpc v1.2.2 // indirect
	github.com/coreos/go-semver v0.3.1 // indirect
	github.com/coreos/go-systemd/v22 v22.5.0 // indirect
	github.com/davecgh/go-spew v1.1.2-0.20180830191138-d8f796af33cc // indirect
	github.com/distribution/reference v0.5.0 // indirect
	github.com/elastic/elastic-transport-go/v8 v8.5.0 // indirect
	github.com/elastic/go-elasticsearch v0.0.0 // indirect
	github.com/elastic/go-elasticsearch/v8 v8.13.1 // indirect
	github.com/elliotchance/orderedmap/v2 v2.2.0 // indirect
	github.com/emicklei/go-restful/v3 v3.11.0 // indirect
	github.com/evanphx/json-patch v5.9.0+incompatible // indirect
	github.com/fatih/camelcase v1.0.0 // indirect
	github.com/felixge/httpsnoop v1.0.4 // indirect
	github.com/fsnotify/fsnotify v1.7.0 // indirect
	github.com/gabriel-vasile/mimetype v1.4.7 // indirect
	github.com/gertd/go-pluralize v0.2.1 // indirect
	github.com/ghodss/yaml v1.0.0 // indirect
	github.com/go-logr/logr v1.4.2 // indirect
	github.com/go-logr/stdr v1.2.2 // indirect
	github.com/go-openapi/jsonpointer v0.21.0 // indirect
	github.com/go-openapi/jsonreference v0.21.0 // indirect
	github.com/go-openapi/swag v0.23.0 // indirect
	github.com/go-playground/form/v4 v4.2.1 // indirect
	github.com/go-playground/locales v0.14.1 // indirect
	github.com/go-playground/universal-translator v0.18.1 // indirect
	github.com/go-playground/validator/v10 v10.21.0 // indirect
	github.com/go-sql-driver/mysql v1.8.1 // indirect
	github.com/go-xorm/xorm v0.7.9 // indirect
	github.com/gogo/protobuf v1.3.2 // indirect
	github.com/golang-jwt/jwt/v5 v5.0.0 // indirect
	github.com/golang-migrate/migrate/v4 v4.15.2 // indirect
	github.com/golang/groupcache v0.0.0-20210331224755-41bb18bfe9da // indirect
	github.com/golang/mock v1.7.0-rc.1 // indirect
	github.com/golang/protobuf v1.5.4 // indirect
	github.com/golang/snappy v0.0.4 // indirect
	github.com/google/cel-go v0.17.8 // indirect
	github.com/google/flatbuffers v24.3.25+incompatible // indirect
	github.com/google/gnostic-models v0.6.9-0.20230804172637-c7be7c783f49 // indirect
	github.com/google/go-cmp v0.6.0 // indirect
	github.com/google/gofuzz v1.2.0 // indirect
	github.com/gorilla/websocket v1.5.0 // indirect
	github.com/grpc-ecosystem/grpc-gateway v1.16.0 // indirect
	github.com/grpc-ecosystem/grpc-gateway/v2 v2.20.0 // indirect
	github.com/hashicorp/errwrap v1.1.0 // indirect
	github.com/hashicorp/go-multierror v1.1.1 // indirect
	github.com/hashicorp/hcl v1.0.0 // indirect
	github.com/huandu/go-clone v1.6.0 // indirect
	github.com/huandu/go-clone/generic v1.6.0 // indirect
	github.com/imdario/mergo v0.3.13 // indirect
	github.com/inconshreveable/mousetrap v1.1.0 // indirect
	github.com/jinzhu/configor v1.2.1 // indirect
	github.com/jinzhu/copier v0.4.0 // indirect
	github.com/jinzhu/inflection v1.0.0 // indirect
	github.com/jinzhu/now v1.1.5 // indirect
	github.com/josharian/intern v1.0.0 // indirect
	github.com/json-iterator/go v1.1.12 // indirect
	github.com/klauspost/compress v1.17.9 // indirect
	github.com/kr/fs v0.1.0 // indirect
	github.com/kubernetes-csi/external-snapshotter/client/v4 v4.2.1-0.20211230034650-36933011bfb5 // indirect
	github.com/kylelemons/godebug v1.1.0 // indirect
	github.com/leodido/go-urn v1.4.0 // indirect
	github.com/lestrrat-go/strftime v1.1.0 // indirect
	github.com/magiconair/properties v1.8.7 // indirect
	github.com/mailru/easyjson v0.7.7 // indirect
	github.com/mitchellh/go-homedir v1.1.0 // indirect
	github.com/mitchellh/mapstructure v1.5.0 // indirect
	github.com/moby/spdystream v0.2.0 // indirect
	github.com/moby/sys/mountinfo v0.6.2 // indirect
	github.com/modern-go/concurrent v0.0.0-20180306012644-bacd9c7ef1dd // indirect
	github.com/modern-go/reflect2 v1.0.2 // indirect
	github.com/munnerz/goautoneg v0.0.0-20191010083416-a7dc8b61c822 // indirect
	github.com/mxk/go-flowrate v0.0.0-20140419014527-cca7078d478f // indirect
	github.com/natefinch/lumberjack v2.0.0+incompatible // indirect
	github.com/opencontainers/go-digest v1.0.0 // indirect
	github.com/opencontainers/selinux v1.11.0 // indirect
	github.com/opentracing/opentracing-go v1.2.0 // indirect
	github.com/panjf2000/ants/v2 v2.9.1 // indirect
	github.com/patrickmn/go-cache v2.1.0+incompatible // indirect
	github.com/pelletier/go-toml/v2 v2.2.2 // indirect
	github.com/pierrec/lz4/v4 v4.1.21 // indirect
	github.com/pkg/errors v0.9.1 // indirect
	github.com/pkg/sftp v1.13.1 // indirect
	github.com/pmezard/go-difflib v1.0.1-0.20181226105442-5d4384ee4fb2 // indirect
	github.com/prometheus/client_model v0.6.1 // indirect
	github.com/prometheus/common v0.55.0 // indirect
	github.com/prometheus/procfs v0.15.1 // indirect
	github.com/r3labs/sse/v2 v2.10.0 // indirect
	github.com/remeh/sizedwaitgroup v1.0.0 // indirect
	github.com/samber/lo v1.39.0 // indirect
	github.com/samber/mo v1.8.0 // indirect
	github.com/segmentio/ksuid v1.0.3 // indirect
	github.com/sirupsen/logrus v1.9.3 // indirect
	github.com/spaolacci/murmur3 v1.1.0 // indirect
	github.com/spf13/afero v1.10.0 // indirect
	github.com/spf13/cast v1.6.0 // indirect
	github.com/spf13/jwalterweatherman v1.1.0 // indirect
	github.com/stoewer/go-strcase v1.3.0 // indirect
	github.com/subosito/gotenv v1.6.0 // indirect
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cdb v1.0.720 // indirect
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/clb v1.0.774 // indirect
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/monitor v1.0.644 // indirect
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/sts v1.0.315 // indirect
	github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/tag v1.0.425 // indirect
	github.com/thoas/go-funk v0.9.1 // indirect
	github.com/tidwall/gjson v1.17.0 // indirect
	github.com/tidwall/match v1.1.1 // indirect
	github.com/tidwall/pretty v1.2.1 // indirect
	github.com/tidwall/sjson v1.2.5 // indirect
	github.com/uber/jaeger-client-go v2.25.0+incompatible // indirect
	github.com/uber/jaeger-lib v2.4.1+incompatible // indirect
	github.com/valyala/bytebufferpool v1.0.0 // indirect
	github.com/valyala/fasthttp v1.54.0 // indirect
	github.com/wumansgy/goEncrypt v1.1.0 // indirect
	go.etcd.io/etcd/api/v3 v3.5.14 // indirect
	go.etcd.io/etcd/client/pkg/v3 v3.5.14 // indirect
	go.etcd.io/etcd/client/v3 v3.5.14 // indirect
	go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc v0.42.0 // indirect
	go.opentelemetry.io/contrib/instrumentation/net/http/otelhttp v0.52.0 // indirect
	go.opentelemetry.io/otel v1.31.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace v1.27.0 // indirect
	go.opentelemetry.io/otel/exporters/otlp/otlptrace/otlptracegrpc v1.27.0 // indirect
	go.opentelemetry.io/otel/metric v1.31.0 // indirect
	go.opentelemetry.io/otel/sdk v1.31.0 // indirect
	go.opentelemetry.io/otel/trace v1.31.0 // indirect
	go.opentelemetry.io/proto/otlp v1.2.0 // indirect
	go.uber.org/atomic v1.11.0 // indirect
	go.uber.org/automaxprocs v1.5.3 // indirect
	go.uber.org/multierr v1.11.0 // indirect
	go.uber.org/zap v1.27.0 // indirect
	golang.org/x/crypto v0.29.0 // indirect
	golang.org/x/exp v0.0.0-20240613232115-7f521ea00fb8 // indirect
	golang.org/x/net v0.31.0 // indirect
	golang.org/x/oauth2 v0.23.0 // indirect
	golang.org/x/sys v0.27.0 // indirect
	golang.org/x/term v0.26.0 // indirect
	golang.org/x/text v0.20.0 // indirect
	golang.org/x/time v0.7.0 // indirect
	google.golang.org/genproto v0.0.0-20240604185151-ef581f913117 // indirect
	google.golang.org/genproto/googleapis/api v0.0.0-20241015192408-796eee8c2d53 // indirect
	google.golang.org/genproto/googleapis/rpc v0.0.0-20241015192408-796eee8c2d53 // indirect
	gopkg.in/cenkalti/backoff.v1 v1.1.0 // indirect
	gopkg.in/go-playground/validator.v9 v9.29.1 // indirect
	gopkg.in/inf.v0 v0.9.1 // indirect
	gopkg.in/ini.v1 v1.67.0 // indirect
	gopkg.in/natefinch/lumberjack.v2 v2.2.1 // indirect
	gopkg.in/yaml.v3 v3.0.1 // indirect
	gorm.io/plugin/opentelemetry v0.1.4 // indirect
	gorm.io/plugin/opentracing v0.0.0-20211220013347-7d2b2af23560 // indirect
	gorm.io/plugin/soft_delete v1.2.0 // indirect
	helm.sh/helm/v3 v3.10.3 // indirect
	k8s.io/apiextensions-apiserver v0.26.3 // indirect
	k8s.io/apiserver v0.30.0 // indirect
	k8s.io/cloud-provider v0.30.0 // indirect
	k8s.io/component-base v0.30.0 // indirect
	k8s.io/component-helpers v0.30.0 // indirect
	k8s.io/controller-manager v0.30.0 // indirect
	k8s.io/csi-translation-lib v0.0.0 // indirect
	k8s.io/dynamic-resource-allocation v0.0.0 // indirect
	k8s.io/kms v0.30.0 // indirect
	k8s.io/kube-openapi v0.0.0-20240228011516-70dd3763d340 // indirect
	k8s.io/kube-scheduler v0.30.0 // indirect
	k8s.io/kubelet v0.30.0 // indirect
	k8s.io/mount-utils v0.0.0 // indirect
	k8s.io/utils v0.0.0-20241104100929-3ea5e8cea738 // indirect
	sigs.k8s.io/apiserver-network-proxy/konnectivity-client v0.29.0 // indirect
	sigs.k8s.io/controller-runtime v0.14.6 // indirect
	sigs.k8s.io/json v0.0.0-20221116044647-bc3834ca7abd // indirect
	sigs.k8s.io/structured-merge-diff/v4 v4.4.1 // indirect
	xorm.io/builder v0.3.6 // indirect
	xorm.io/core v0.7.2-0.20190928055935-90aeac8d08eb // indirect
)

replace (
	cloud.tencent.com/mervynwang/scheduler-tools => ./staging/src/cloud.tencent.com/mervynwang/scheduler-tools
	cloud.tencent.com/tke/master-operator => ./staging/src/cloud.tencent.com/tke/master-operator
	git.code.oa.com/tke-micro/tke-quality-inspector => git.woa.com/alexmlqi/tke-quality-inspector v0.0.0-20231010135807-1640daeb0063
	git.woa.com/kmetis/kmetis => ./staging/src/git.woa.com/kmetis/kmetis
	github.com/NetEase-Object-Storage/nos-golang-sdk => github.com/karuppiah7890/nos-golang-sdk v0.0.0-20191116042345-0792ba35abcc
	github.com/chartmuseum/storage => github.com/choujimmy/storage v0.5.1-0.20191225102245-210f7683d0a6
	github.com/google/gnostic => github.com/google/gnostic v0.7.0
	github.com/prometheus/common => github.com/prometheus/common v0.60.0
	google.golang.org/grpc => google.golang.org/grpc v1.69.2
	k8s.io/api => k8s.io/api v0.30.0
	k8s.io/apiextensions-apiserver => k8s.io/apiextensions-apiserver v0.30.0
	k8s.io/apimachinery => k8s.io/apimachinery v0.30.0
	k8s.io/apiserver => k8s.io/apiserver v0.30.0
	k8s.io/cli-runtime => k8s.io/cli-runtime v0.30.0
	k8s.io/client-go => k8s.io/client-go v0.30.0
	k8s.io/cloud-provider => k8s.io/cloud-provider v0.30.0
	k8s.io/cluster-bootstrap => k8s.io/cluster-bootstrap v0.30.0
	k8s.io/code-generator => k8s.io/code-generator v0.30.0
	k8s.io/component-base => k8s.io/component-base v0.30.0
	k8s.io/component-helpers => git.woa.com/k8s/kubernetes/staging/src/k8s.io/component-helpers v0.0.0-20250103033427-681b66df7c3c
	k8s.io/controller-manager => k8s.io/controller-manager v0.30.0
	k8s.io/cri-api => k8s.io/cri-api v0.30.0
	k8s.io/csi-translation-lib => k8s.io/csi-translation-lib v0.30.0
	k8s.io/dynamic-resource-allocation => k8s.io/dynamic-resource-allocation v0.30.0
	k8s.io/endpointslice => k8s.io/endpointslice v0.30.0
	k8s.io/kms => k8s.io/kms v0.30.0
	k8s.io/kube-aggregator => k8s.io/kube-aggregator v0.30.0
	k8s.io/kube-controller-manager => k8s.io/kube-controller-manager v0.30.0
	k8s.io/kube-openapi => k8s.io/kube-openapi v0.0.0-20240228011516-70dd3763d340
	k8s.io/kube-proxy => k8s.io/kube-proxy v0.30.0
	k8s.io/kube-scheduler => k8s.io/kube-scheduler v0.30.0
	k8s.io/kubectl => k8s.io/kubectl v0.30.0
	k8s.io/kubelet => k8s.io/kubelet v0.30.0
	k8s.io/kubernetes => git.woa.com/k8s/kubernetes v1.30.0
	k8s.io/legacy-cloud-providers => k8s.io/legacy-cloud-providers v0.30.0
	k8s.io/metrics => k8s.io/metrics v0.30.0
	k8s.io/mount-utils => git.woa.com/k8s/kubernetes/staging/src/k8s.io/mount-utils v0.0.0-20250103033427-681b66df7c3c
	k8s.io/node-api => k8s.io/node-api v0.30.0
	k8s.io/pod-security-admission => k8s.io/pod-security-admission v0.30.0
	k8s.io/sample-apiserver => k8s.io/sample-apiserver v0.30.0
	k8s.io/sample-cli-plugin => k8s.io/sample-cli-plugin v0.30.0
	k8s.io/sample-controller => k8s.io/sample-controller v0.30.0
	tkestack.io/tke => git.woa.com/tke/tkestack v1.20.13-tke-application
)
