package infra

import (
	"fmt"
	"os"
	"sync"

	"gorm.io/driver/mysql"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
	"k8s.io/klog"

	"git.woa.com/kmetis/starship/pkg/config"
	"git.woa.com/kmetis/starship/pkg/util"
)

// DB 连接信息, 包含超时时间
var dbSource = "%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local&timeout=10s"

type MysqlUtil struct {
	db   *gorm.DB
	once *sync.Once
}

// 管理连接，确保每个db单例
var utilManager = new(sync.Map)

func InitDbSource(db config.Database) {
	// DB的密码经过加密处理，这里需要解密后使用
	// NOTICE: 测试环境的密码可以直接使用，因为加密方式是保密的，我们不知道加密方式。
	key := os.Getenv("ENIGMA_KEYS_PATH")
	if key == "" {
		klog.Infof("encrypt key not configured")
		dbSource = fmt.Sprintf(dbSource, db.DbUser, db.DbPasswd, db.DbHost, db.DbPort, db.DbDatabase)
		return
	}
	rawPasswd, err := util.Decrypt(db.DbPasswd)
	if err != nil {
		msg := fmt.Sprintf("decrypt passwd error: %s", err.Error())
		panic(msg)
	}
	dbSource = fmt.Sprintf(dbSource, db.DbUser, rawPasswd, db.DbHost, db.DbPort, db.DbDatabase)
}

// NewSqlUtil 获取mysql 的client
func NewSqlUtil() (*MysqlUtil, error) {
	if dbSource == "" {
		return nil, fmt.Errorf("get mysql client error: dbSource is empty")
	}
	var sqlUtil *MysqlUtil

	if val, ok := utilManager.Load(dbSource); ok {

		// 如果存在，直接使用
		sqlUtil = val.(*MysqlUtil)
	} else {

		// 如果不存在，需创建新的db
		sqlUtil = new(MysqlUtil)
		sqlUtil.once = new(sync.Once)
		val1, _ := utilManager.LoadOrStore(dbSource, sqlUtil)
		sqlUtil = val1.(*MysqlUtil)
	}

	var err error
	var dbIns *gorm.DB = nil

	// 利用once确保每个db只被初始化一次
	sqlUtil.once.Do(
		func() {
			klog.Infof("start init database")
			dbIns, err = gorm.Open(mysql.Open(dbSource), &gorm.Config{
				Logger: logger.Default.LogMode(logger.Info),
			})
			if err != nil {
				return
			}
			sqlUtil.db = dbIns
		},
	)

	if err != nil {
		sqlUtil.once = new(sync.Once)
		klog.Errorf("open db error: %s", err.Error())
		return nil, err
	}
	if sqlUtil.db == nil {
		return nil, fmt.Errorf("get SQLUtil error: db is nil")
	}

	return sqlUtil, nil
}
