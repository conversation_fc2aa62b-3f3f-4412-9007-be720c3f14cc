package infra

import "time"

/*
// 对于表中的 Revision 字段，不同资源时，记录不同的版本信息
// deploy: 记录 revision num，用于查找 rs
// sts: 记录 controllerrevision name，用于查找 controllerrevision
// masterCRD：记录 imagetag ，用于回滚 master 对象

select * from starship_revision;
+----+--------+-----------+---------+------------+--------------+---------------------+----------+---------------------+
| Id | TaskId | SubTaskId | AppName | Strategy   | ResourceType | ResourceInfo        | Revision | CreateTime          |
+----+--------+-----------+---------+------------+--------------+---------------------+----------+---------------------+
|  1 |    304 |       364 | coredns | deployment | Deployment   | kube-system/coredns | 19       | 2025-04-27 12:07:22 |
+----+--------+-----------+---------+------------+--------------+---------------------+----------+---------------------+
1 <USER> <GROUP> set (0.00 sec)

*/

// revision
// 当变更完成后，会记录到db中，其中关键数据是 Revision ，用于回滚
type StarshipRevision struct {
	ID           int64     `gorm:"column:Id;type:bigint(20);comment:主键id;NOT NULL" json:"Id"`
	TaskId       int64     `gorm:"column:TaskId;type:bigint(20);comment:任务id;NOT NULL" json:"TaskId"`
	SubTaskId    int64     `gorm:"column:SubTaskId;type:bigint(20);comment:子任务id;NOT NULL" json:"SubTaskId"`
	AppName      string    `gorm:"column:AppName;type:varchar(100);comment:app名称;NOT NULL" json:"AppName"`
	Strategy     string    `gorm:"column:Strategy;type:varchar(100);comment:发布策略" json:"Strategy"`
	ResourceType string    `gorm:"column:ResourceType;type:varchar(100);comment:资源类型" json:"ResourceType"`
	ResourceInfo string    `gorm:"column:ResourceInfo;type:varchar(100);comment:资源信息" json:"ResourceInfo"`
	Revision     string    `gorm:"column:Revision;type:varchar(100);comment:变更信息" json:"Revision"`
	CreateTime   time.Time `gorm:"column:CreateTime;type:datetime;default:CURRENT_TIMESTAMP;comment:创建时间" json:"CreateTime"`
}

func (m *StarshipRevision) TableName() string {
	return "starship_revision"
}
