/*
任务操作表
*/
package infra

import (
	"context"
	"fmt"
	"k8s.io/klog/v2"
)

// 创建版本记录
func CreateRevision(revision *StarshipRevision) error {
	// 创建任务
	if revision == nil {
		return fmt.Errorf("revision is empty")
	}

	x, err := NewSqlUtil()
	if err != nil {
		klog.Errorf("get sql util error: %s", err.Error())
		return err
	}

	// 注意：这里定义DB操作的超时时间，避免耗时久导致接口没有响应
	ctx, cancel := context.WithTimeout(context.Background(), DB_CONNECTION_TIMEOUT_SECONDS)
	defer cancel()

	// 启动事务
	tx := x.db.WithContext(ctx).Begin()
	res := tx.Create(revision)
	if res.Error != nil {
		klog.Errorf("create revision error: %s", res.Error.Error())
		tx.Rollback()
		return fmt.Errorf("create revision error: %s", res.Error.Error())
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		klog.Errorf("commit transaction error: %s", err.Error())
		return err
	}
	return nil
}

func GetRevisionByTask(taskId int64) (*StarshipRevision, error) {
	x, err := NewSqlUtil()
	if err != nil {
		klog.Errorf("get sql util error: %s", err.Error())
		return nil, err
	}

	ctx, cancel := context.WithTimeout(context.Background(), DB_CONNECTION_TIMEOUT_SECONDS)
	defer cancel()

	rv := &StarshipRevision{}
	res := x.db.WithContext(ctx).Where("TaskId = ?", taskId).First(rv)
	if res.Error != nil {
		klog.Errorf("get revision error: %s", res.Error.Error())
		return nil, res.Error
	}
	return rv, nil
}
