package infra

import "time"

// 风险隐患
type StarshipRisks struct {
	ID              int64     `gorm:"column:Id;type:bigint(20);comment:主键id;NOT NULL" json:"Id"`
	SubTaskId       int64     `gorm:"column:SubTaskId;type:bigint(20);comment:子任务id;NOT NULL" json:"SubTaskId"`
	AppName         string    `gorm:"column:AppName;type:varchar(100);comment:组件名称" json:"AppName"`
	Name            string    `gorm:"column:Name;type:varchar(256);comment:隐患名称" json:"Name"`
	Code            string    `gorm:"column:Code;type:varchar(100);comment:隐患错误码" json:"Code"`
	Detail          string    `gorm:"column:Detail;type:varchar(1024);comment:隐患详情" json:"Detail"`
	Level           string    `gorm:"column:Level;type:varchar(50);comment:隐患级别" json:"Level"`
	Solution        string    `gorm:"column:Solution;type:varchar(1024);comment:解决方案" json:"Solution"`
	CreateTime      time.Time `gorm:"column:CreateTime;type:datetime;default:CURRENT_TIMESTAMP;comment:create time" json:"CreateTime"`
	HealthCheckName string    `gorm:"column:HealthCheckName;type:varchar(100);comment:健康检查项名称" json:"HealthCheckName"`
}

func (m *StarshipRisks) TableName() string {
	return "starship_risks"
}
