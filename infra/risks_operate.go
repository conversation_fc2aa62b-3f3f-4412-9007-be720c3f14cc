/*
风险操作表
*/
package infra

import (
	"context"
	"fmt"
	"time"

	"git.woa.com/kmetis/starship/pkg/util"
	"k8s.io/klog/v2"
)

const (
	RISK_EXPIRE_DAYS = 7    // 清理过去7天的risks 记录
	BATCH_SIZE       = 1000 // 单次清理的记录数量
)

// 创建风险
func CreateRisk(risks []*StarshipRisks) error {
	if len(risks) == 0 {
		return nil
	}
	x, err := NewSqlUtil()
	if err != nil {
		klog.Errorf("get sql util error: %s", err.Error())
		return err
	}

	// 注意：这里定义DB操作的超时时间，避免耗时久导致接口没有响应
	ctx, cancel := context.WithTimeout(context.Background(), DB_CONNECTION_TIMEOUT_SECONDS)
	defer cancel()

	// 适配DB定义的字段长度
	for _, v := range risks {
		v.Detail = util.AdaptAttributeMaxLength(v.Detail)
		v.Solution = util.AdaptAttributeMaxLength(v.Solution)
	}
	res := x.db.WithContext(ctx).Create(risks)
	if res.Error != nil {
		klog.Errorf("create risk error: %s", res.Error.Error())
		return fmt.Errorf("create risk error: %s", res.Error.Error())
	}
	// 如果插入记录条数不等于传入条数，则打印日志
	if res.RowsAffected != int64(len(risks)) {
		klog.Errorf("create risk error: rowsAffected(%d) != len(risks)(%d)", res.RowsAffected, len(risks))
	}
	return nil
}

// 查询过期的风险项
func DeleteExpiredRisks() error {
	klog.Infof("start delete expired risks")
	x, err := NewSqlUtil()
	if err != nil {
		klog.Errorf("get sql util error: %s", err.Error())
		return err
	}

	// 执行分批删除操作
	for {
		tx := x.db.Table((&StarshipRisks{}).TableName())
		risks := []*StarshipRisks{}
		// 注意：这里定义查询DB的超时时间，避免耗时久导致接口没有响应
		ctx, cancel := context.WithTimeout(context.Background(), DB_CONNECTION_TIMEOUT_SECONDS)
		defer cancel()
		tx = tx.WithContext(ctx)

		tx = tx.Where("CreateTime < ?", time.Now().Add(-1*RISK_EXPIRE_DAYS*24*time.Hour)) // 清理过去7天数据
		res := tx.Limit(BATCH_SIZE).Delete(&risks)                                        // 分批清理
		if res.Error != nil {
			klog.Errorf("delete expired risks error: %s", res.Error.Error())
			// 这里不要continue，否则会一直死循环
			return res.Error
		}
		// 如果受影响的行数为0，表示已经删除完所有符合条件的行，退出循环
		if res.RowsAffected == 0 {
			break
		}
		time.Sleep(10 * time.Second)
	}

	klog.Infof("risks created in the past 7 days have been deleted.")
	return nil
}

func GetAllRisksBySubTask(subTaskId int64, healthCheckName string) ([]*StarshipRisks, error) {
	x, err := NewSqlUtil()
	if err != nil {
		klog.Errorf("get sql util error: %s", err.Error())
		return nil, err
	}

	tx := x.db.Table((&StarshipRisks{}).TableName())
	// 注意：这里定义查询DB的超时时间，避免耗时久导致接口没有响应
	ctx, cancel := context.WithTimeout(context.Background(), DB_CONNECTION_TIMEOUT_SECONDS)
	defer cancel()
	tx = tx.WithContext(ctx)

	risks := []*StarshipRisks{}
	tx = tx.Where("SubTaskId = ?", subTaskId)
	if healthCheckName != "" {
		tx = tx.Where("HealthCheckName = ?", healthCheckName)
	}

	res := tx.Find(&risks)
	if res.Error != nil {
		klog.Errorf("query risks error: %s", res.Error.Error())
		return nil, res.Error
	}
	return risks, nil
}
