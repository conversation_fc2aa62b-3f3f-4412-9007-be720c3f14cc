package infra

import "time"

// subtask
type StarshipSubtask struct {
	ID           int64      `gorm:"column:Id;type:bigint(20);comment:子任务id;NOT NULL" json:"Id"`
	ParentTaskId int64      `gorm:"column:ParentTaskId;type:bigint(20);comment:父任务id;NOT NULL" json:"ParentTaskId"`
	AppName      string     `gorm:"column:AppName;type:varchar(100);comment:app名称;NOT NULL" json:"AppName"`
	Action       string     `gorm:"column:Action;type:varchar(100);comment:操作类型;NOT NULL" json:"Action"`
	Status       string     `gorm:"column:Status;type:varchar(50);comment:任务状态" json:"Status"`
	Reason       string     `gorm:"column:Reason;type:varchar(1024);comment:任务失败原因" json:"Reason"`
	StartTime    time.Time  `gorm:"column:StartTime;type:datetime;comment:任务开始时间" json:"StartTime"`
	EndTime      *time.Time `gorm:"column:EndTime;type:datetime;comment:任务结束时间" json:"EndTime"`
	CostTime     int        `gorm:"column:CostTime;type:int(9);comment:发布耗时" json:"CostTime"`
	UpdateTime   time.Time  `gorm:"column:UpdateTime;type:datetime;default:CURRENT_TIMESTAMP;comment:update time" json:"UpdateTime"`
	CreateTime   time.Time  `gorm:"column:CreateTime;type:datetime;default:CURRENT_TIMESTAMP;comment:create time" json:"CreateTime"`
}

func (m *StarshipSubtask) TableName() string {
	return "starship_subtask"
}
