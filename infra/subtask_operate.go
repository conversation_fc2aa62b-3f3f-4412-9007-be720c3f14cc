/*
子任务操作表
*/
package infra

import (
	"context"
	"fmt"

	"git.woa.com/kmetis/starship/pkg/util"
	"k8s.io/klog/v2"
)

// 更新子任务
func UpdateSubTask(subTask *StarshipSubtask) error {
	if subTask == nil {
		return fmt.Errorf("subTask is empty")
	}
	if subTask.ID == 0 {
		return fmt.Errorf("subTask id is empty")
	}
	x, err := NewSqlUtil()
	if err != nil {
		klog.Errorf("get sql util error: %s", err.Error())
		return err
	}

	// 注意：这里定义DB操作的超时时间，避免耗时久导致接口没有响应
	ctx, cancel := context.WithTimeout(context.Background(), DB_CONNECTION_TIMEOUT_SECONDS)
	defer cancel()

	updateColumns := make(map[string]interface{})
	if subTask.Status != "" {
		updateColumns["Status"] = subTask.Status
	}
	// 适配DB定义的字段长度
	if subTask.Reason != "" {
		updateColumns["Reason"] = util.AdaptAttributeMaxLength(subTask.Reason)
	}
	if subTask.CostTime != 0 {
		updateColumns["CostTime"] = subTask.CostTime
	}
	if subTask.EndTime != nil && !subTask.EndTime.IsZero() {
		updateColumns["EndTime"] = subTask.EndTime
	}
	res := x.db.WithContext(ctx).Table((&StarshipSubtask{}).TableName()).Where("Id = ?", subTask.ID).Updates(updateColumns)
	if res.Error != nil {
		klog.Errorf("update subTask error: %s", res.Error.Error())
		return fmt.Errorf("update subTask error: %s", res.Error.Error())
	}
	// 如果指定的记录没有更新，就打印一个错误日志，表示不符合预期
	if res.RowsAffected == 0 {
		klog.Errorf("update subTask error:%s, subTask id:%d", "no subtask updated", subTask.ID)
	}

	return nil
}

// 查询子任务
func GetSubTask(subtaskId int64) (*StarshipSubtask, error) {
	x, err := NewSqlUtil()
	if err != nil {
		klog.Errorf("get sql util error: %s", err.Error())
		return nil, err
	}

	// 注意：这里定义DB操作的超时时间，避免耗时久导致接口没有响应
	ctx, cancel := context.WithTimeout(context.Background(), DB_CONNECTION_TIMEOUT_SECONDS)
	defer cancel()

	subTask := &StarshipSubtask{}
	res := x.db.WithContext(ctx).Where("Id = ?", subtaskId).First(subTask)
	if res.Error != nil {
		klog.Errorf("get subTask error: %s", res.Error.Error())
		return nil, fmt.Errorf("get subTask error: %s", res.Error.Error())
	}
	return subTask, nil
}

func GetSubTaskByParent(parentId int64, action string) ([]*StarshipSubtask, error) {
	x, err := NewSqlUtil()
	if err != nil {
		klog.Errorf("get sql util error: %s", err.Error())
		return nil, err
	}

	tx := x.db.Table((&StarshipSubtask{}).TableName())
	// 注意：这里定义查询DB的超时时间，避免耗时久导致接口没有响应
	ctx, cancel := context.WithTimeout(context.Background(), DB_CONNECTION_TIMEOUT_SECONDS)
	defer cancel()
	tx = tx.WithContext(ctx)

	subTasks := []*StarshipSubtask{}
	tx = tx.Where("ParentTaskId = ?", parentId)
	if action != "" {
		tx = tx.Where("Action = ?", action)
	}
	tx = tx.Order("StartTime ASC")

	res := tx.Find(&subTasks)
	if res.Error != nil {
		klog.Errorf("query unstarted subTasks error: %s", res.Error.Error())
		return nil, res.Error
	}
	return subTasks, nil
}

// 获取precheck/upgrade/postcheck子任务
func GetSubTaskByAction(parentId int64, action string) (*StarshipSubtask, error) {
	x, err := NewSqlUtil()
	if err != nil {
		klog.Errorf("get sql util error: %s", err.Error())
		return nil, err
	}

	tx := x.db.Table((&StarshipSubtask{}).TableName())
	// 注意：这里定义查询DB的超时时间，避免耗时久导致接口没有响应
	ctx, cancel := context.WithTimeout(context.Background(), DB_CONNECTION_TIMEOUT_SECONDS)
	defer cancel()
	tx = tx.WithContext(ctx)

	subTask := &StarshipSubtask{}
	tx = tx.Where("ParentTaskId = ? and Action=?", parentId, action)
	res := tx.Limit(1).Find(&subTask)
	if res.Error != nil {
		klog.Errorf("query unstarted subTask error: %s", res.Error.Error())
		return nil, res.Error
	}
	return subTask, nil
}
