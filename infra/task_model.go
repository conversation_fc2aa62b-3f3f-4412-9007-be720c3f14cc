// 定义DB的数据结构
package infra

import "time"

// Starship Cloud Native Release System
type StarshipTask struct {
	ID            int64     `gorm:"column:Id;type:bigint(20);AUTO_INCREMENT;comment:任务id;NOT NULL" json:"Id"`
	Name          string    `gorm:"column:Name;type:varchar(100);comment:任务名称;NOT NULL" json:"Name"`
	AppName       string    `gorm:"column:AppName;type:varchar(100);comment:app名称;NOT NULL" json:"AppName"`
	Type          string    `gorm:"column:Type;type:varchar(100);comment:任务类型;NOT NULL" json:"Type"`
	Phase         string    `gorm:"column:Phase;type:varchar(100);comment:任务阶段;NOT NULL" json:"Phase"`
	ClusterId     string    `gorm:"column:ClusterId;type:varchar(50);comment:集群id;NOT NULL" json:"ClusterId"`
	Namespace     string    `gorm:"column:Namespace;type:varchar(100);comment:负载的namespace;NOT NULL" json:"Namespace"`
	MetaClusterId string    `gorm:"column:MetaClusterId;type:varchar(50);comment:meta集群id" json:"MetaClusterId"`
	Region        string    `gorm:"column:Region;type:varchar(30);comment:地域信息;NOT NULL" json:"Region"`
	ProductName   string    `gorm:"column:ProductName;type:varchar(50);comment:ProductName" json:"ProductName"`
	ImageTag      string    `gorm:"column:ImageTag;type:varchar(100);comment:镜像tag" json:"ImageTag"`
	Envs          string    `gorm:"column:Envs;type:varchar(100);comment:环境变量列表" json:"Envs"`
	Token         string    `gorm:"column:Token;type:varchar(256);comment:token信息" json:"Token"`
	User          string    `gorm:"column:User;type:varchar(100);comment:变更执行人" json:"User"`
	ExtendInfo    string    `gorm:"column:ExtendInfo;type:varchar(2048);comment:扩展信息" json:"ExtendInfo"`
	CostTime      int       `gorm:"column:CostTime;type:int(9);comment:发布耗时, 单位：秒" json:"CostTime"`
	Strategy      string    `gorm:"column:Strategy;type:varchar(100);comment:发布策略" json:"Strategy"`
	PluginVersion string    `gorm:"column:PluginVersion;type:varchar(50);comment:插件版本" json:"PluginVersion"`
	Timeout       int       `gorm:"column:Timeout;type:int(9);comment:超时时间, 单位：秒" json:"Timeout"`
	ChangeId      string    `gorm:"column:ChangeId;type:varchar(100);comment:发布单id;NOT NULL" json:"ChangeId"`
	BatchId       int       `gorm:"column:BatchId;type:int(9);comment:变更批次id" json:"BatchId"`
	Status        string    `gorm:"column:Status;type:varchar(50);comment:任务状态" json:"Status"`
	Reason        string    `gorm:"column:Reason;type:varchar(1024);comment:任务失败原因" json:"Reason"`
	TraceId       string    `gorm:"column:TraceId;type:varchar(256);comment:TraceId" json:"TraceId"`
	UpdateTime    time.Time `gorm:"column:UpdateTime;type:datetime;default:CURRENT_TIMESTAMP;comment:update time" json:"UpdateTime"`
	CreateTime    time.Time `gorm:"column:CreateTime;type:datetime;default:CURRENT_TIMESTAMP;comment:create time" json:"CreateTime"`
}

// 四个字段一起作为唯一键约束 (ClusterId, AppName, Type, ChangeId)

func (m *StarshipTask) TableName() string {
	return "starship_task"
}
