/*
任务操作表
*/
package infra

import (
	"context"
	"fmt"
	"strings"
	"time"

	"gopkg.in/yaml.v2"
	"k8s.io/klog/v2"

	rainutil "git.woa.com/kmetis/starship/pkg/task/util"
	"git.woa.com/kmetis/starship/pkg/util"
	pkgutil "git.woa.com/kmetis/starship/pkg/util"
)

const (
	DB_CONNECTION_TIMEOUT_SECONDS = 10 * time.Second // DB查询超时设置
)

// 创建任务和子任务, 使用事务
func CreateTaskAndSubTasks(task *StarshipTask) (int64, error) {
	// 创建任务
	if task == nil {
		return 0, fmt.Errorf("task is empty")
	}

	x, err := NewSqlUtil()
	if err != nil {
		klog.Errorf("get sql util error: %s", err.Error())
		return 0, err
	}

	// 注意：这里定义DB操作的超时时间，避免耗时久导致接口没有响应
	ctx, cancel := context.WithTimeout(context.Background(), DB_CONNECTION_TIMEOUT_SECONDS)
	defer cancel()

	// 启动事务
	tx := x.db.WithContext(ctx).Begin()

	// 适配DB定义的字段长度
	task.Reason = util.AdaptAttributeMaxLength(task.Reason)
	res := tx.Create(task)
	if res.Error != nil {
		klog.Errorf("create task error: %s", res.Error.Error())
		tx.Rollback()
		return 0, fmt.Errorf("create task error: %s", res.Error.Error())
	}

	subTasks, err := buildSubtasks(task, task.AppName)
	if err != nil {
		klog.Errorf("build subtasks error: %s", err.Error())
		tx.Rollback()
		return 0, err
	}

	res = tx.Create(subTasks)
	if res.Error != nil {
		klog.Errorf("create subtask error: %s", res.Error.Error())
		tx.Rollback()
		return 0, fmt.Errorf("create subtask error: %s", res.Error.Error())
	}

	// 提交事务
	if err = tx.Commit().Error; err != nil {
		klog.Errorf("commit transaction error: %s", err.Error())
		return 0, err
	}
	return task.ID, nil
}

// 更新任务
func UpdateTask(task *StarshipTask) error {
	if task == nil {
		return fmt.Errorf("task is empty")
	}
	if task.ID == 0 {
		return fmt.Errorf("task id is empty")
	}
	x, err := NewSqlUtil()
	if err != nil {
		klog.Errorf("get sql util error: %s", err.Error())
		return err
	}

	// 适配DB定义的字段长度
	task.Reason = util.AdaptAttributeMaxLength(task.Reason)
	// 注意：这里定义DB操作的超时时间，避免耗时久导致接口没有响应
	ctx, cancel := context.WithTimeout(context.Background(), DB_CONNECTION_TIMEOUT_SECONDS)
	defer cancel()

	res := x.db.WithContext(ctx).Where("id = ?", task.ID).Save(task)
	if res.Error != nil {
		klog.Errorf("update task error: %s", res.Error.Error())
		return fmt.Errorf("update task error: %s", res.Error.Error())
	}
	// 更新记录为0，打印日志记录
	if res.RowsAffected == 0 {
		klog.Errorf("update task error: %s, task-id:%d", "no task updated", task.ID)
	}
	return nil
}

// 查询任务
func GetTask(taskId int64) (*StarshipTask, error) {
	x, err := NewSqlUtil()
	if err != nil {
		klog.Errorf("get sql util error: %s", err.Error())
		return nil, err
	}

	// 注意：这里定义DB操作的超时时间，避免耗时久导致接口没有响应
	ctx, cancel := context.WithTimeout(context.Background(), DB_CONNECTION_TIMEOUT_SECONDS)
	defer cancel()

	task := &StarshipTask{}
	res := x.db.WithContext(ctx).Where("id = ?", taskId).First(task)
	if res.Error != nil {
		klog.Errorf("get task error: %s", res.Error.Error())
		return nil, res.Error
	}
	return task, nil
}

// 查询任务 - 根据ClusterId, AppName, Type, ChangeId（非必填）查询，返回最新的任务
func GetTaskByCondition(clusterId, appName, taskType, changeId string) (*StarshipTask, error) {
	// 参数检查
	if clusterId == "" || appName == "" || taskType == "" {
		return nil, fmt.Errorf("cluster id, app name or task type is empty")
	}
	x, err := NewSqlUtil()
	if err != nil {
		klog.Errorf("get sql util error: %s", err.Error())
		return nil, err
	}

	// 注意：这里定义DB操作的超时时间，避免耗时久导致接口没有响应
	ctx, cancel := context.WithTimeout(context.Background(), DB_CONNECTION_TIMEOUT_SECONDS)
	defer cancel()

	// 注意这里不能用x.db = x.db.Where()，这样会把x.db的值改写掉，影响第二次进入该函数的请求
	tx := x.db.Table((&StarshipTask{}).TableName())
	tx = tx.WithContext(ctx)

	tx = tx.Where("ClusterId = ? AND AppName = ? AND Type = ?", clusterId, appName, taskType)
	if changeId != "" {
		tx = tx.Where("ChangeId = ?", changeId)
	}
	task := &StarshipTask{}
	res := tx.Order("CreateTime desc").Limit(1).First(task)
	if res.Error != nil {
		klog.Errorf("get task error: %s", res.Error.Error())
		return nil, res.Error
	}
	return task, nil
}

// 查询未完成的任务
func GetUncompletedTask() ([]*StarshipTask, error) {
	region, err := pkgutil.GetLongRegion()
	if err != nil {
		klog.Errorf("get long region error: %s", err.Error())
		return nil, err
	}

	x, err := NewSqlUtil()
	if err != nil {
		klog.Errorf("get sql util error: %s", err.Error())
		return nil, err
	}

	tx := x.db.Table((&StarshipTask{}).TableName())
	// 注意：这里定义查询DB的超时时间，避免耗时久导致接口没有响应
	ctx, cancel := context.WithTimeout(context.Background(), DB_CONNECTION_TIMEOUT_SECONDS)
	defer cancel()
	tx = tx.WithContext(ctx)

	tasks := []*StarshipTask{}
	tx = tx.Where("Status NOT IN (?) and Region=?", []string{pkgutil.TaskStatusDone}, region)
	res := tx.Find(&tasks)
	if res.Error != nil {
		klog.Errorf("query uncompleted tasks error: %s", res.Error.Error())
		return nil, res.Error
	}
	return tasks, nil
}

func buildSubtasks(task *StarshipTask, appName string) ([]*StarshipSubtask, error) {
	// 获取后检配置
	componentsConfigContent := rainutil.GetRainbowData(fmt.Sprintf("components/%s/config", appName))
	componentsConfig := make(map[string]interface{})
	if err := yaml.Unmarshal([]byte(componentsConfigContent), &componentsConfig); err != nil {
		klog.Errorf("unmarshal components config failed: %v", err)
		return nil, err
	}

	// 获取后检执行次数
	postCheckCount := rainutil.GetComponentDataInt(componentsConfig, "postCheckExecutionCount")
	if postCheckCount == 0 {
		postCheckCount = 1
	} else if postCheckCount > 3 {
		postCheckCount = 3
	}

	// 获取后检延迟时间
	var postCheckDelay int
	if task.Strategy == util.ReleaseStrategyAddon {
		postCheckDelay = rainutil.GetComponentDataInt(componentsConfig, "addonUpgradePostCheckDelay")
	} else {
		postCheckDelay = rainutil.GetComponentDataInt(componentsConfig, "upgradePostCheckDelay")
	}
	if postCheckDelay > 600 {
		postCheckDelay = 600
	}

	types := strings.Split(task.Type, ",")
	// rollback任务拆分3个子任务
	if task.Type == pkgutil.TaskActionRollback {
		types = []string{pkgutil.TaskActionPreCheck, pkgutil.TaskActionRollback, pkgutil.TaskActionPostCheck}
	}
	var subTasks []*StarshipSubtask
	now := time.Now()

	for _, v := range types {
		if v == pkgutil.TaskActionPostCheck && postCheckCount > 0 {
			// 创建多个后检子任务
			for i := 0; i < postCheckCount; i++ {
				// 计算后检开始时间：升级预计耗时10s + 配置的延迟时间 * (i + 1)
				startTime := now.Add(time.Duration(10+postCheckDelay*(i+1)) * time.Second)
				subTasks = append(subTasks, buildSubtask(task.ID, task.AppName, v, pkgutil.TaskStatusPending, startTime))
			}
		} else {
			// 非后检任务，开始时间为当前时间
			subTasks = append(subTasks, buildSubtask(task.ID, task.AppName, v, pkgutil.TaskStatusPending, now))
		}
	}
	return subTasks, nil
}

func buildSubtask(parentTaskId int64, appName, action, status string, startTime time.Time) *StarshipSubtask {
	return &StarshipSubtask{
		ParentTaskId: parentTaskId,
		AppName:      appName,
		Action:       action,
		Status:       status,
		StartTime:    startTime,
		EndTime:      nil,
	}
}
