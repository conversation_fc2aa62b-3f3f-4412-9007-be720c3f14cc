package lock

import (
	"context"
	"os"
	"time"

	"git.woa.com/kmetis/starship/pkg/task"

	"github.com/google/uuid"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/leaderelection"
	"k8s.io/client-go/tools/leaderelection/resourcelock"
	"k8s.io/klog"
)

// 通过选分布式锁，设置变量
// TODO: 选主切换，但是任务没有执行完怎么办: 一个deploy有多个pod，
// 但是只有1个pod能执行任务，此时使用选主机制，哪个pod是主，则执行任务。
// 但是k8s抢到锁执行时，存在pod上一个任务没有完成的情况，k8s 通过configmap实现变量在多个pod里共享，
// 只有当前是主，且configmap里isTaskFinished设置为True，才能执行任务。
func RunLeaderElection(namespace string) {
	config, err := rest.InClusterConfig()
	if err != nil {
		panic(err.Error())
	}

	clientset, err := kubernetes.NewForConfig(config)
	if err != nil {
		panic(err.Error())
	}
	operatorID, err := GenerateIdentity()
	if err != nil {
		panic(err.Error())
	}

	// 指定锁的资源对象，这里使用了Lease资源，还支持configmap，endpoint，或者multilock(即多种配合使用)
	lock := &resourcelock.LeaseLock{
		LeaseMeta: metav1.ObjectMeta{
			Name:      "starship-task-lock",
			Namespace: namespace, // TODO: 换成从ENV读取
		},
		Client: clientset.CoordinationV1(),
		LockConfig: resourcelock.ResourceLockConfig{
			Identity: operatorID,
		},
	}

	// 进行选举
	ctx := context.TODO()
	leaderelection.RunOrDie(ctx, leaderelection.LeaderElectionConfig{
		Lock:            lock,
		ReleaseOnCancel: true,
		LeaseDuration:   60 * time.Second, //租约时长，不能设置太长，因为主pod被删除后，需要等待租约过期才会触发重新选主（主pod被删除不会触发重新选主）
		RenewDeadline:   15 * time.Second, //leader刷新资源锁超时时间
		RetryPeriod:     5 * time.Second,  //调用资源锁间隔
		//回调函数，根据选举不同事件触发
		Callbacks: leaderelection.LeaderCallbacks{
			OnStartedLeading: func(ctx context.Context) {
				klog.Infof("leader elected: %s", operatorID)
				task.RunTask()
			},
			OnStoppedLeading: func() {
				// 进程退出
				klog.Infof("leader lost: %s", operatorID)
				os.Exit(1)
			},
			OnNewLeader: func(identity string) {
				//当产生新的leader后执行的方法
				if identity == operatorID {
					return
				}
				klog.Infof("leader re-election complete: %s", identity)
			},
		},
	})
}

func GenerateIdentity() (string, error) {
	hostname, err := os.Hostname()
	if err != nil {
		return "", err
	}

	// add a uniquifier so that two processes on the same host don't accidentally both become active
	identity := hostname + "_" + uuid.NewString()
	return identity, nil
}
