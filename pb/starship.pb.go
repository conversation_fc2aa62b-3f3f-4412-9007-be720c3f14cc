// Copyright 2015 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.1
// 	protoc        v4.25.1
// source: pb/starship.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ComponentHealthyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Product       string  `protobuf:"bytes,1,opt,name=product,proto3" json:"product,omitempty"`
	Region        string  `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	ClusterId     string  `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	ComponentName string  `protobuf:"bytes,4,opt,name=component_name,json=componentName,proto3" json:"component_name,omitempty"`
	MetaType      *string `protobuf:"bytes,5,opt,name=meta_type,json=metaType,proto3,oneof" json:"meta_type,omitempty"`
	PolicyName    *string `protobuf:"bytes,6,opt,name=policy_name,json=policyName,proto3,oneof" json:"policy_name,omitempty"`
}

func (x *ComponentHealthyRequest) Reset() {
	*x = ComponentHealthyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_starship_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ComponentHealthyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComponentHealthyRequest) ProtoMessage() {}

func (x *ComponentHealthyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_starship_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComponentHealthyRequest.ProtoReflect.Descriptor instead.
func (*ComponentHealthyRequest) Descriptor() ([]byte, []int) {
	return file_pb_starship_proto_rawDescGZIP(), []int{0}
}

func (x *ComponentHealthyRequest) GetProduct() string {
	if x != nil {
		return x.Product
	}
	return ""
}

func (x *ComponentHealthyRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *ComponentHealthyRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *ComponentHealthyRequest) GetComponentName() string {
	if x != nil {
		return x.ComponentName
	}
	return ""
}

func (x *ComponentHealthyRequest) GetMetaType() string {
	if x != nil && x.MetaType != nil {
		return *x.MetaType
	}
	return ""
}

func (x *ComponentHealthyRequest) GetPolicyName() string {
	if x != nil && x.PolicyName != nil {
		return *x.PolicyName
	}
	return ""
}

type ComponentHealthyReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrMessage      string `protobuf:"bytes,1,opt,name=err_message,json=errMessage,proto3" json:"err_message,omitempty"`
	HealthCheckName string `protobuf:"bytes,2,opt,name=health_check_name,json=healthCheckName,proto3" json:"health_check_name,omitempty"` // 健康检查项名称
	RiskName        string `protobuf:"bytes,3,opt,name=risk_name,json=riskName,proto3" json:"risk_name,omitempty"`                        // 隐患名称
	Level           string `protobuf:"bytes,4,opt,name=level,proto3" json:"level,omitempty"`                                              // 隐患级别
	Code            string `protobuf:"bytes,5,opt,name=code,proto3" json:"code,omitempty"`                                                // 隐患错误码，可选值：PASS、FAILED、ERROR
	Detail          string `protobuf:"bytes,6,opt,name=detail,proto3" json:"detail,omitempty"`                                            // 隐患详情，检查不通过时，提示不通过原因，检查失败时，上报错误信息
	Solution        string `protobuf:"bytes,7,opt,name=solution,proto3" json:"solution,omitempty"`                                        // 解决方案，检查不通过时，提示对应的解决方案
}

func (x *ComponentHealthyReply) Reset() {
	*x = ComponentHealthyReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_starship_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ComponentHealthyReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ComponentHealthyReply) ProtoMessage() {}

func (x *ComponentHealthyReply) ProtoReflect() protoreflect.Message {
	mi := &file_pb_starship_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ComponentHealthyReply.ProtoReflect.Descriptor instead.
func (*ComponentHealthyReply) Descriptor() ([]byte, []int) {
	return file_pb_starship_proto_rawDescGZIP(), []int{1}
}

func (x *ComponentHealthyReply) GetErrMessage() string {
	if x != nil {
		return x.ErrMessage
	}
	return ""
}

func (x *ComponentHealthyReply) GetHealthCheckName() string {
	if x != nil {
		return x.HealthCheckName
	}
	return ""
}

func (x *ComponentHealthyReply) GetRiskName() string {
	if x != nil {
		return x.RiskName
	}
	return ""
}

func (x *ComponentHealthyReply) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

func (x *ComponentHealthyReply) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *ComponentHealthyReply) GetDetail() string {
	if x != nil {
		return x.Detail
	}
	return ""
}

func (x *ComponentHealthyReply) GetSolution() string {
	if x != nil {
		return x.Solution
	}
	return ""
}

type NodeHealthyRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Product    string   `protobuf:"bytes,1,opt,name=product,proto3" json:"product,omitempty"`
	Region     string   `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	ClusterId  string   `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	Nodes      []string `protobuf:"bytes,4,rep,name=nodes,proto3" json:"nodes,omitempty"`
	PolicyName *string  `protobuf:"bytes,5,opt,name=policy_name,json=policyName,proto3,oneof" json:"policy_name,omitempty"`
	Timestamp  *int64   `protobuf:"varint,6,opt,name=timestamp,proto3,oneof" json:"timestamp,omitempty"` // milliseconds(time.UnixMilli)
}

func (x *NodeHealthyRequest) Reset() {
	*x = NodeHealthyRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_starship_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeHealthyRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeHealthyRequest) ProtoMessage() {}

func (x *NodeHealthyRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_starship_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeHealthyRequest.ProtoReflect.Descriptor instead.
func (*NodeHealthyRequest) Descriptor() ([]byte, []int) {
	return file_pb_starship_proto_rawDescGZIP(), []int{2}
}

func (x *NodeHealthyRequest) GetProduct() string {
	if x != nil {
		return x.Product
	}
	return ""
}

func (x *NodeHealthyRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *NodeHealthyRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *NodeHealthyRequest) GetNodes() []string {
	if x != nil {
		return x.Nodes
	}
	return nil
}

func (x *NodeHealthyRequest) GetPolicyName() string {
	if x != nil && x.PolicyName != nil {
		return *x.PolicyName
	}
	return ""
}

func (x *NodeHealthyRequest) GetTimestamp() int64 {
	if x != nil && x.Timestamp != nil {
		return *x.Timestamp
	}
	return 0
}

type NodeHealthyReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Skip            bool     `protobuf:"varint,1,opt,name=skip,proto3" json:"skip,omitempty"`
	ErrMessage      string   `protobuf:"bytes,2,opt,name=err_message,json=errMessage,proto3" json:"err_message,omitempty"`
	UnhealthyNodes  []string `protobuf:"bytes,3,rep,name=unhealthy_nodes,json=unhealthyNodes,proto3" json:"unhealthy_nodes,omitempty"`
	EvictionNodes   []string `protobuf:"bytes,4,rep,name=eviction_nodes,json=evictionNodes,proto3" json:"eviction_nodes,omitempty"`
	HealthCheckName string   `protobuf:"bytes,5,opt,name=health_check_name,json=healthCheckName,proto3" json:"health_check_name,omitempty"` // 健康检查项名称
	RiskName        string   `protobuf:"bytes,6,opt,name=risk_name,json=riskName,proto3" json:"risk_name,omitempty"`                        // 隐患名称
	Level           string   `protobuf:"bytes,7,opt,name=level,proto3" json:"level,omitempty"`                                              // 隐患级别
	Code            string   `protobuf:"bytes,8,opt,name=code,proto3" json:"code,omitempty"`                                                // 隐患错误码，可选值：PASS、FAILED、ERROR
	Detail          string   `protobuf:"bytes,9,opt,name=detail,proto3" json:"detail,omitempty"`                                            // 隐患详情，检查不通过时，提示不通过原因，检查失败时，上报错误信息
	Solution        string   `protobuf:"bytes,10,opt,name=solution,proto3" json:"solution,omitempty"`                                       // 解决方案，检查不通过时，提示对应的解决方案
}

func (x *NodeHealthyReply) Reset() {
	*x = NodeHealthyReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_starship_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NodeHealthyReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NodeHealthyReply) ProtoMessage() {}

func (x *NodeHealthyReply) ProtoReflect() protoreflect.Message {
	mi := &file_pb_starship_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NodeHealthyReply.ProtoReflect.Descriptor instead.
func (*NodeHealthyReply) Descriptor() ([]byte, []int) {
	return file_pb_starship_proto_rawDescGZIP(), []int{3}
}

func (x *NodeHealthyReply) GetSkip() bool {
	if x != nil {
		return x.Skip
	}
	return false
}

func (x *NodeHealthyReply) GetErrMessage() string {
	if x != nil {
		return x.ErrMessage
	}
	return ""
}

func (x *NodeHealthyReply) GetUnhealthyNodes() []string {
	if x != nil {
		return x.UnhealthyNodes
	}
	return nil
}

func (x *NodeHealthyReply) GetEvictionNodes() []string {
	if x != nil {
		return x.EvictionNodes
	}
	return nil
}

func (x *NodeHealthyReply) GetHealthCheckName() string {
	if x != nil {
		return x.HealthCheckName
	}
	return ""
}

func (x *NodeHealthyReply) GetRiskName() string {
	if x != nil {
		return x.RiskName
	}
	return ""
}

func (x *NodeHealthyReply) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

func (x *NodeHealthyReply) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *NodeHealthyReply) GetDetail() string {
	if x != nil {
		return x.Detail
	}
	return ""
}

func (x *NodeHealthyReply) GetSolution() string {
	if x != nil {
		return x.Solution
	}
	return ""
}

type CreateDryRunTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Product       string `protobuf:"bytes,1,opt,name=product,proto3" json:"product,omitempty"`
	Region        string `protobuf:"bytes,2,opt,name=region,proto3" json:"region,omitempty"`
	ClusterId     string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`
	ComponentName string `protobuf:"bytes,4,opt,name=component_name,json=componentName,proto3" json:"component_name,omitempty"`
	MetaType      string `protobuf:"bytes,5,opt,name=meta_type,json=metaType,proto3" json:"meta_type,omitempty"`
	TaskName      string `protobuf:"bytes,6,opt,name=task_name,json=taskName,proto3" json:"task_name,omitempty"`
	TaskTimeout   string `protobuf:"bytes,7,opt,name=task_timeout,json=taskTimeout,proto3" json:"task_timeout,omitempty"`
}

func (x *CreateDryRunTaskRequest) Reset() {
	*x = CreateDryRunTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_starship_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDryRunTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDryRunTaskRequest) ProtoMessage() {}

func (x *CreateDryRunTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_starship_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDryRunTaskRequest.ProtoReflect.Descriptor instead.
func (*CreateDryRunTaskRequest) Descriptor() ([]byte, []int) {
	return file_pb_starship_proto_rawDescGZIP(), []int{4}
}

func (x *CreateDryRunTaskRequest) GetProduct() string {
	if x != nil {
		return x.Product
	}
	return ""
}

func (x *CreateDryRunTaskRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CreateDryRunTaskRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *CreateDryRunTaskRequest) GetComponentName() string {
	if x != nil {
		return x.ComponentName
	}
	return ""
}

func (x *CreateDryRunTaskRequest) GetMetaType() string {
	if x != nil {
		return x.MetaType
	}
	return ""
}

func (x *CreateDryRunTaskRequest) GetTaskName() string {
	if x != nil {
		return x.TaskName
	}
	return ""
}

func (x *CreateDryRunTaskRequest) GetTaskTimeout() string {
	if x != nil {
		return x.TaskTimeout
	}
	return ""
}

type CreateDryRunTaskReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
}

func (x *CreateDryRunTaskReply) Reset() {
	*x = CreateDryRunTaskReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_starship_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDryRunTaskReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDryRunTaskReply) ProtoMessage() {}

func (x *CreateDryRunTaskReply) ProtoReflect() protoreflect.Message {
	mi := &file_pb_starship_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDryRunTaskReply.ProtoReflect.Descriptor instead.
func (*CreateDryRunTaskReply) Descriptor() ([]byte, []int) {
	return file_pb_starship_proto_rawDescGZIP(), []int{5}
}

func (x *CreateDryRunTaskReply) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

type GetDryRunTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId string `protobuf:"bytes,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
}

func (x *GetDryRunTaskRequest) Reset() {
	*x = GetDryRunTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_starship_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDryRunTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDryRunTaskRequest) ProtoMessage() {}

func (x *GetDryRunTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_starship_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDryRunTaskRequest.ProtoReflect.Descriptor instead.
func (*GetDryRunTaskRequest) Descriptor() ([]byte, []int) {
	return file_pb_starship_proto_rawDescGZIP(), []int{6}
}

func (x *GetDryRunTaskRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

type GetDryRunTaskReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Status        string `protobuf:"bytes,1,opt,name=status,proto3" json:"status,omitempty"`
	ElapsedSecond uint32 `protobuf:"varint,2,opt,name=elapsed_second,json=elapsedSecond,proto3" json:"elapsed_second,omitempty"`
	Message       string `protobuf:"bytes,3,opt,name=message,proto3" json:"message,omitempty"`
}

func (x *GetDryRunTaskReply) Reset() {
	*x = GetDryRunTaskReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_starship_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDryRunTaskReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDryRunTaskReply) ProtoMessage() {}

func (x *GetDryRunTaskReply) ProtoReflect() protoreflect.Message {
	mi := &file_pb_starship_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDryRunTaskReply.ProtoReflect.Descriptor instead.
func (*GetDryRunTaskReply) Descriptor() ([]byte, []int) {
	return file_pb_starship_proto_rawDescGZIP(), []int{7}
}

func (x *GetDryRunTaskReply) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *GetDryRunTaskReply) GetElapsedSecond() uint32 {
	if x != nil {
		return x.ElapsedSecond
	}
	return 0
}

func (x *GetDryRunTaskReply) GetMessage() string {
	if x != nil {
		return x.Message
	}
	return ""
}

type CancelTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region string `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	TaskId string `protobuf:"bytes,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
}

func (x *CancelTaskRequest) Reset() {
	*x = CancelTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_starship_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelTaskRequest) ProtoMessage() {}

func (x *CancelTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_starship_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelTaskRequest.ProtoReflect.Descriptor instead.
func (*CancelTaskRequest) Descriptor() ([]byte, []int) {
	return file_pb_starship_proto_rawDescGZIP(), []int{8}
}

func (x *CancelTaskRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CancelTaskRequest) GetTaskId() string {
	if x != nil {
		return x.TaskId
	}
	return ""
}

type CancelTaskReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrMessage string `protobuf:"bytes,1,opt,name=err_message,json=errMessage,proto3" json:"err_message,omitempty"`
}

func (x *CancelTaskReply) Reset() {
	*x = CancelTaskReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_starship_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CancelTaskReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CancelTaskReply) ProtoMessage() {}

func (x *CancelTaskReply) ProtoReflect() protoreflect.Message {
	mi := &file_pb_starship_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CancelTaskReply.ProtoReflect.Descriptor instead.
func (*CancelTaskReply) Descriptor() ([]byte, []int) {
	return file_pb_starship_proto_rawDescGZIP(), []int{9}
}

func (x *CancelTaskReply) GetErrMessage() string {
	if x != nil {
		return x.ErrMessage
	}
	return ""
}

type RollbackTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Region string `protobuf:"bytes,1,opt,name=region,proto3" json:"region,omitempty"`
	TaskId int64  `protobuf:"varint,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
}

func (x *RollbackTaskRequest) Reset() {
	*x = RollbackTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_starship_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RollbackTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RollbackTaskRequest) ProtoMessage() {}

func (x *RollbackTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_starship_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RollbackTaskRequest.ProtoReflect.Descriptor instead.
func (*RollbackTaskRequest) Descriptor() ([]byte, []int) {
	return file_pb_starship_proto_rawDescGZIP(), []int{10}
}

func (x *RollbackTaskRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *RollbackTaskRequest) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

type RollbackTaskReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	ErrMessage string `protobuf:"bytes,1,opt,name=err_message,json=errMessage,proto3" json:"err_message,omitempty"`
	TaskId     int64  `protobuf:"varint,2,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`
}

func (x *RollbackTaskReply) Reset() {
	*x = RollbackTaskReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_starship_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RollbackTaskReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RollbackTaskReply) ProtoMessage() {}

func (x *RollbackTaskReply) ProtoReflect() protoreflect.Message {
	mi := &file_pb_starship_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RollbackTaskReply.ProtoReflect.Descriptor instead.
func (*RollbackTaskReply) Descriptor() ([]byte, []int) {
	return file_pb_starship_proto_rawDescGZIP(), []int{11}
}

func (x *RollbackTaskReply) GetErrMessage() string {
	if x != nil {
		return x.ErrMessage
	}
	return ""
}

func (x *RollbackTaskReply) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

// 创建任务请求
type CreateTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Name          string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`                                         // 任务名称
	AppName       string `protobuf:"bytes,2,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`                    // app名称
	Type          string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`                                         // 任务类型
	Phase         string `protobuf:"bytes,4,opt,name=phase,proto3" json:"phase,omitempty"`                                       // 任务阶段
	ClusterId     string `protobuf:"bytes,5,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`              // 集群id
	Namespace     string `protobuf:"bytes,6,opt,name=namespace,proto3" json:"namespace,omitempty"`                               // 负载的namespace
	MetaclusterId string `protobuf:"bytes,7,opt,name=metacluster_id,json=metaclusterId,proto3" json:"metacluster_id,omitempty"`  // meta集群id
	Region        string `protobuf:"bytes,8,opt,name=region,proto3" json:"region,omitempty"`                                     // 地域信息
	ProductName   string `protobuf:"bytes,9,opt,name=product_name,json=productName,proto3" json:"product_name,omitempty"`        // productname
	ImageTag      string `protobuf:"bytes,10,opt,name=image_tag,json=imageTag,proto3" json:"image_tag,omitempty"`                // 镜像tag
	Envs          string `protobuf:"bytes,11,opt,name=envs,proto3" json:"envs,omitempty"`                                        // 环境变量列表
	Token         string `protobuf:"bytes,12,opt,name=token,proto3" json:"token,omitempty"`                                      // token信息
	User          string `protobuf:"bytes,13,opt,name=user,proto3" json:"user,omitempty"`                                        // 变更执行人
	ExtendInfo    string `protobuf:"bytes,14,opt,name=extend_info,json=extendInfo,proto3" json:"extend_info,omitempty"`          // 扩展信息
	Strategy      string `protobuf:"bytes,15,opt,name=strategy,proto3" json:"strategy,omitempty"`                                // 发布策略
	PluginVersion string `protobuf:"bytes,16,opt,name=plugin_version,json=pluginVersion,proto3" json:"plugin_version,omitempty"` // 插件版本
	Timeout       int32  `protobuf:"varint,17,opt,name=timeout,proto3" json:"timeout,omitempty"`                                 // 超时时间, 单位：秒
	ChangeId      string `protobuf:"bytes,18,opt,name=change_id,json=changeId,proto3" json:"change_id,omitempty"`                // 发布单id
	BatchId       int32  `protobuf:"varint,19,opt,name=batch_id,json=batchId,proto3" json:"batch_id,omitempty"`                  // 变更批次id
	TraceId       string `protobuf:"bytes,20,opt,name=trace_id,json=traceId,proto3" json:"trace_id,omitempty"`                   // traceid
	ClientToken   string `protobuf:"bytes,21,opt,name=client_token,json=clientToken,proto3" json:"client_token,omitempty"`       // 客户端token
}

func (x *CreateTaskRequest) Reset() {
	*x = CreateTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_starship_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTaskRequest) ProtoMessage() {}

func (x *CreateTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_starship_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTaskRequest.ProtoReflect.Descriptor instead.
func (*CreateTaskRequest) Descriptor() ([]byte, []int) {
	return file_pb_starship_proto_rawDescGZIP(), []int{12}
}

func (x *CreateTaskRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateTaskRequest) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *CreateTaskRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *CreateTaskRequest) GetPhase() string {
	if x != nil {
		return x.Phase
	}
	return ""
}

func (x *CreateTaskRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *CreateTaskRequest) GetNamespace() string {
	if x != nil {
		return x.Namespace
	}
	return ""
}

func (x *CreateTaskRequest) GetMetaclusterId() string {
	if x != nil {
		return x.MetaclusterId
	}
	return ""
}

func (x *CreateTaskRequest) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *CreateTaskRequest) GetProductName() string {
	if x != nil {
		return x.ProductName
	}
	return ""
}

func (x *CreateTaskRequest) GetImageTag() string {
	if x != nil {
		return x.ImageTag
	}
	return ""
}

func (x *CreateTaskRequest) GetEnvs() string {
	if x != nil {
		return x.Envs
	}
	return ""
}

func (x *CreateTaskRequest) GetToken() string {
	if x != nil {
		return x.Token
	}
	return ""
}

func (x *CreateTaskRequest) GetUser() string {
	if x != nil {
		return x.User
	}
	return ""
}

func (x *CreateTaskRequest) GetExtendInfo() string {
	if x != nil {
		return x.ExtendInfo
	}
	return ""
}

func (x *CreateTaskRequest) GetStrategy() string {
	if x != nil {
		return x.Strategy
	}
	return ""
}

func (x *CreateTaskRequest) GetPluginVersion() string {
	if x != nil {
		return x.PluginVersion
	}
	return ""
}

func (x *CreateTaskRequest) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *CreateTaskRequest) GetChangeId() string {
	if x != nil {
		return x.ChangeId
	}
	return ""
}

func (x *CreateTaskRequest) GetBatchId() int32 {
	if x != nil {
		return x.BatchId
	}
	return 0
}

func (x *CreateTaskRequest) GetTraceId() string {
	if x != nil {
		return x.TraceId
	}
	return ""
}

func (x *CreateTaskRequest) GetClientToken() string {
	if x != nil {
		return x.ClientToken
	}
	return ""
}

// 创建任务返回体
type CreateTaskReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId int64  `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"` // 任务id
	Code   int32  `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`                   // 返回码
	Reason string `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`                // 失败原因
}

func (x *CreateTaskReply) Reset() {
	*x = CreateTaskReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_starship_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTaskReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTaskReply) ProtoMessage() {}

func (x *CreateTaskReply) ProtoReflect() protoreflect.Message {
	mi := &file_pb_starship_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTaskReply.ProtoReflect.Descriptor instead.
func (*CreateTaskReply) Descriptor() ([]byte, []int) {
	return file_pb_starship_proto_rawDescGZIP(), []int{13}
}

func (x *CreateTaskReply) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *CreateTaskReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *CreateTaskReply) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

// 查询任务详情请求
type DescribeTaskRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId    int64  `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`         // 任务id
	ChangeId  string `protobuf:"bytes,2,opt,name=change_id,json=changeId,proto3" json:"change_id,omitempty"`    // 发布单id
	ClusterId string `protobuf:"bytes,3,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"` // 集群id
	AppName   string `protobuf:"bytes,4,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`       // app名称
	Type      string `protobuf:"bytes,5,opt,name=type,proto3" json:"type,omitempty"`                            // 任务类型
}

func (x *DescribeTaskRequest) Reset() {
	*x = DescribeTaskRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_starship_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeTaskRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeTaskRequest) ProtoMessage() {}

func (x *DescribeTaskRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_starship_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeTaskRequest.ProtoReflect.Descriptor instead.
func (*DescribeTaskRequest) Descriptor() ([]byte, []int) {
	return file_pb_starship_proto_rawDescGZIP(), []int{14}
}

func (x *DescribeTaskRequest) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *DescribeTaskRequest) GetChangeId() string {
	if x != nil {
		return x.ChangeId
	}
	return ""
}

func (x *DescribeTaskRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *DescribeTaskRequest) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *DescribeTaskRequest) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

// 查询任务详情返回体
type DescribeTaskReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	TaskId     int64      `protobuf:"varint,1,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`             // 任务id
	Name       string     `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                                // 任务名称
	AppName    string     `protobuf:"bytes,3,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`           // app名称
	Type       string     `protobuf:"bytes,4,opt,name=type,proto3" json:"type,omitempty"`                                // 任务类型
	Stage      string     `protobuf:"bytes,5,opt,name=stage,proto3" json:"stage,omitempty"`                              // 任务阶段
	Envs       string     `protobuf:"bytes,6,opt,name=envs,proto3" json:"envs,omitempty"`                                // 环境变量列表
	ExtendInfo string     `protobuf:"bytes,7,opt,name=extend_info,json=extendInfo,proto3" json:"extend_info,omitempty"`  // 扩展信息，比如镜像tag等
	Region     string     `protobuf:"bytes,8,opt,name=region,proto3" json:"region,omitempty"`                            // 地域信息
	ClusterId  string     `protobuf:"bytes,9,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`     // 集群id
	Timeout    int32      `protobuf:"varint,10,opt,name=timeout,proto3" json:"timeout,omitempty"`                        // 任务超时控制
	ChangeId   string     `protobuf:"bytes,11,opt,name=change_id,json=changeId,proto3" json:"change_id,omitempty"`       // 发布单id
	Batch      int32      `protobuf:"varint,12,opt,name=batch,proto3" json:"batch,omitempty"`                            // 变更批次
	Status     string     `protobuf:"bytes,13,opt,name=status,proto3" json:"status,omitempty"`                           // 任务状态
	Reason     string     `protobuf:"bytes,14,opt,name=reason,proto3" json:"reason,omitempty"`                           // 失败原因
	UpdateTime string     `protobuf:"bytes,15,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"` // 更新时间
	CreateTime string     `protobuf:"bytes,16,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"` // 创建时间
	Subtasks   []*SubTask `protobuf:"bytes,17,rep,name=subtasks,proto3" json:"subtasks,omitempty"`                       // 子任务信息
}

func (x *DescribeTaskReply) Reset() {
	*x = DescribeTaskReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_starship_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DescribeTaskReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DescribeTaskReply) ProtoMessage() {}

func (x *DescribeTaskReply) ProtoReflect() protoreflect.Message {
	mi := &file_pb_starship_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DescribeTaskReply.ProtoReflect.Descriptor instead.
func (*DescribeTaskReply) Descriptor() ([]byte, []int) {
	return file_pb_starship_proto_rawDescGZIP(), []int{15}
}

func (x *DescribeTaskReply) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *DescribeTaskReply) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DescribeTaskReply) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *DescribeTaskReply) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *DescribeTaskReply) GetStage() string {
	if x != nil {
		return x.Stage
	}
	return ""
}

func (x *DescribeTaskReply) GetEnvs() string {
	if x != nil {
		return x.Envs
	}
	return ""
}

func (x *DescribeTaskReply) GetExtendInfo() string {
	if x != nil {
		return x.ExtendInfo
	}
	return ""
}

func (x *DescribeTaskReply) GetRegion() string {
	if x != nil {
		return x.Region
	}
	return ""
}

func (x *DescribeTaskReply) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *DescribeTaskReply) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *DescribeTaskReply) GetChangeId() string {
	if x != nil {
		return x.ChangeId
	}
	return ""
}

func (x *DescribeTaskReply) GetBatch() int32 {
	if x != nil {
		return x.Batch
	}
	return 0
}

func (x *DescribeTaskReply) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *DescribeTaskReply) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *DescribeTaskReply) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *DescribeTaskReply) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *DescribeTaskReply) GetSubtasks() []*SubTask {
	if x != nil {
		return x.Subtasks
	}
	return nil
}

// 子任务信息
type SubTask struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubtaskId    int64   `protobuf:"varint,1,opt,name=subtask_id,json=subtaskId,proto3" json:"subtask_id,omitempty"`            // 子任务id
	ParentTaskId int64   `protobuf:"varint,2,opt,name=parent_task_id,json=parentTaskId,proto3" json:"parent_task_id,omitempty"` // 父任务id
	AppName      string  `protobuf:"bytes,3,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`                   // app名称
	Action       string  `protobuf:"bytes,4,opt,name=action,proto3" json:"action,omitempty"`                                    // 操作类型
	Status       string  `protobuf:"bytes,5,opt,name=status,proto3" json:"status,omitempty"`                                    // 任务状态
	Reason       string  `protobuf:"bytes,6,opt,name=reason,proto3" json:"reason,omitempty"`                                    // 任务失败原因
	CostTime     int32   `protobuf:"varint,7,opt,name=cost_time,json=costTime,proto3" json:"cost_time,omitempty"`               // 发布耗时
	UpdateTime   string  `protobuf:"bytes,8,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`          // 更新时间
	CreateTime   string  `protobuf:"bytes,9,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`          // 创建时间
	Risks        []*Risk `protobuf:"bytes,10,rep,name=risks,proto3" json:"risks,omitempty"`                                     // 风险信息
}

func (x *SubTask) Reset() {
	*x = SubTask{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_starship_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubTask) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubTask) ProtoMessage() {}

func (x *SubTask) ProtoReflect() protoreflect.Message {
	mi := &file_pb_starship_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubTask.ProtoReflect.Descriptor instead.
func (*SubTask) Descriptor() ([]byte, []int) {
	return file_pb_starship_proto_rawDescGZIP(), []int{16}
}

func (x *SubTask) GetSubtaskId() int64 {
	if x != nil {
		return x.SubtaskId
	}
	return 0
}

func (x *SubTask) GetParentTaskId() int64 {
	if x != nil {
		return x.ParentTaskId
	}
	return 0
}

func (x *SubTask) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *SubTask) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

func (x *SubTask) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *SubTask) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *SubTask) GetCostTime() int32 {
	if x != nil {
		return x.CostTime
	}
	return 0
}

func (x *SubTask) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *SubTask) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *SubTask) GetRisks() []*Risk {
	if x != nil {
		return x.Risks
	}
	return nil
}

// 风险信息
type Risk struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Id              int64  `protobuf:"varint,1,opt,name=id,proto3" json:"id,omitempty"`                                                    // 主键id
	SubtaskId       int64  `protobuf:"varint,2,opt,name=subtask_id,json=subtaskId,proto3" json:"subtask_id,omitempty"`                     // 子任务id
	AppName         string `protobuf:"bytes,3,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`                            // 组件名称
	Name            string `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`                                                 // 隐患名称
	Code            string `protobuf:"bytes,5,opt,name=code,proto3" json:"code,omitempty"`                                                 // 隐患错误码
	Detail          string `protobuf:"bytes,6,opt,name=detail,proto3" json:"detail,omitempty"`                                             // 隐患详情
	Level           string `protobuf:"bytes,7,opt,name=level,proto3" json:"level,omitempty"`                                               // 隐患级别
	Solution        string `protobuf:"bytes,8,opt,name=solution,proto3" json:"solution,omitempty"`                                         // 解决方案
	CreateTime      string `protobuf:"bytes,9,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`                   // 创建时间
	HealthCheckName string `protobuf:"bytes,10,opt,name=health_check_name,json=healthCheckName,proto3" json:"health_check_name,omitempty"` // 健康检查项名称
}

func (x *Risk) Reset() {
	*x = Risk{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_starship_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Risk) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Risk) ProtoMessage() {}

func (x *Risk) ProtoReflect() protoreflect.Message {
	mi := &file_pb_starship_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Risk.ProtoReflect.Descriptor instead.
func (*Risk) Descriptor() ([]byte, []int) {
	return file_pb_starship_proto_rawDescGZIP(), []int{17}
}

func (x *Risk) GetId() int64 {
	if x != nil {
		return x.Id
	}
	return 0
}

func (x *Risk) GetSubtaskId() int64 {
	if x != nil {
		return x.SubtaskId
	}
	return 0
}

func (x *Risk) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *Risk) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Risk) GetCode() string {
	if x != nil {
		return x.Code
	}
	return ""
}

func (x *Risk) GetDetail() string {
	if x != nil {
		return x.Detail
	}
	return ""
}

func (x *Risk) GetLevel() string {
	if x != nil {
		return x.Level
	}
	return ""
}

func (x *Risk) GetSolution() string {
	if x != nil {
		return x.Solution
	}
	return ""
}

func (x *Risk) GetCreateTime() string {
	if x != nil {
		return x.CreateTime
	}
	return ""
}

func (x *Risk) GetHealthCheckName() string {
	if x != nil {
		return x.HealthCheckName
	}
	return ""
}

// 上报任务执行结果请求
type ReportTaskResultRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubtaskId  int64   `protobuf:"varint,1,opt,name=subtask_id,json=subtaskId,proto3" json:"subtask_id,omitempty"`   // 子任务id
	AppName    string  `protobuf:"bytes,2,opt,name=app_name,json=appName,proto3" json:"app_name,omitempty"`          // app名称
	Status     string  `protobuf:"bytes,3,opt,name=status,proto3" json:"status,omitempty"`                           // 任务状态
	Reason     string  `protobuf:"bytes,4,opt,name=reason,proto3" json:"reason,omitempty"`                           // 失败原因
	CostTime   int32   `protobuf:"varint,5,opt,name=cost_time,json=costTime,proto3" json:"cost_time,omitempty"`      // 发布耗时
	UpdateTime string  `protobuf:"bytes,6,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"` // 更新时间
	ClusterId  string  `protobuf:"bytes,7,opt,name=cluster_id,json=clusterId,proto3" json:"cluster_id,omitempty"`    // 集群id
	TaskId     int64   `protobuf:"varint,8,opt,name=task_id,json=taskId,proto3" json:"task_id,omitempty"`            // 任务id
	Risks      []*Risk `protobuf:"bytes,9,rep,name=risks,proto3" json:"risks,omitempty"`                             // 风险信息
	Action     string  `protobuf:"bytes,10,opt,name=action,proto3" json:"action,omitempty"`                          // 操作类型
}

func (x *ReportTaskResultRequest) Reset() {
	*x = ReportTaskResultRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_starship_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportTaskResultRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportTaskResultRequest) ProtoMessage() {}

func (x *ReportTaskResultRequest) ProtoReflect() protoreflect.Message {
	mi := &file_pb_starship_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportTaskResultRequest.ProtoReflect.Descriptor instead.
func (*ReportTaskResultRequest) Descriptor() ([]byte, []int) {
	return file_pb_starship_proto_rawDescGZIP(), []int{18}
}

func (x *ReportTaskResultRequest) GetSubtaskId() int64 {
	if x != nil {
		return x.SubtaskId
	}
	return 0
}

func (x *ReportTaskResultRequest) GetAppName() string {
	if x != nil {
		return x.AppName
	}
	return ""
}

func (x *ReportTaskResultRequest) GetStatus() string {
	if x != nil {
		return x.Status
	}
	return ""
}

func (x *ReportTaskResultRequest) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

func (x *ReportTaskResultRequest) GetCostTime() int32 {
	if x != nil {
		return x.CostTime
	}
	return 0
}

func (x *ReportTaskResultRequest) GetUpdateTime() string {
	if x != nil {
		return x.UpdateTime
	}
	return ""
}

func (x *ReportTaskResultRequest) GetClusterId() string {
	if x != nil {
		return x.ClusterId
	}
	return ""
}

func (x *ReportTaskResultRequest) GetTaskId() int64 {
	if x != nil {
		return x.TaskId
	}
	return 0
}

func (x *ReportTaskResultRequest) GetRisks() []*Risk {
	if x != nil {
		return x.Risks
	}
	return nil
}

func (x *ReportTaskResultRequest) GetAction() string {
	if x != nil {
		return x.Action
	}
	return ""
}

// 上报任务执行结果返回体
type ReportTaskResultReply struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	SubtaskId int64  `protobuf:"varint,1,opt,name=subtask_id,json=subtaskId,proto3" json:"subtask_id,omitempty"` // 任务id
	Code      int32  `protobuf:"varint,2,opt,name=code,proto3" json:"code,omitempty"`                            // 返回码
	Reason    string `protobuf:"bytes,3,opt,name=reason,proto3" json:"reason,omitempty"`                         // 失败原因
}

func (x *ReportTaskResultReply) Reset() {
	*x = ReportTaskResultReply{}
	if protoimpl.UnsafeEnabled {
		mi := &file_pb_starship_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReportTaskResultReply) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReportTaskResultReply) ProtoMessage() {}

func (x *ReportTaskResultReply) ProtoReflect() protoreflect.Message {
	mi := &file_pb_starship_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReportTaskResultReply.ProtoReflect.Descriptor instead.
func (*ReportTaskResultReply) Descriptor() ([]byte, []int) {
	return file_pb_starship_proto_rawDescGZIP(), []int{19}
}

func (x *ReportTaskResultReply) GetSubtaskId() int64 {
	if x != nil {
		return x.SubtaskId
	}
	return 0
}

func (x *ReportTaskResultReply) GetCode() int32 {
	if x != nil {
		return x.Code
	}
	return 0
}

func (x *ReportTaskResultReply) GetReason() string {
	if x != nil {
		return x.Reason
	}
	return ""
}

var File_pb_starship_proto protoreflect.FileDescriptor

var file_pb_starship_proto_rawDesc = []byte{
	0x0a, 0x11, 0x70, 0x62, 0x2f, 0x73, 0x74, 0x61, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x12, 0x02, 0x70, 0x62, 0x1a, 0x0f, 0x70, 0x62, 0x2f, 0x74, 0x70, 0x5f, 0x72,
	0x70, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xf7, 0x01, 0x0a, 0x17, 0x43, 0x6f, 0x6d,
	0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65,
	0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x63,
	0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x09,
	0x6d, 0x65, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48,
	0x00, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x88, 0x01, 0x01, 0x12, 0x24,
	0x0a, 0x0b, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x48, 0x01, 0x52, 0x0a, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x88, 0x01, 0x01, 0x42, 0x0c, 0x0a, 0x0a, 0x5f, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x42, 0x0e, 0x0a, 0x0c, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x22, 0xdf, 0x01, 0x0a, 0x15, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74,
	0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1f, 0x0a, 0x0b,
	0x65, 0x72, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x65, 0x72, 0x72, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x2a, 0x0a,
	0x11, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68,
	0x43, 0x68, 0x65, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x72, 0x69, 0x73,
	0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x72, 0x69,
	0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x6f, 0x6c, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f, 0x6c, 0x75,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0xe2, 0x01, 0x0a, 0x12, 0x4e, 0x6f, 0x64, 0x65, 0x48, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x70,
	0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72,
	0x6f, 0x64, 0x75, 0x63, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05,
	0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03, 0x28, 0x09, 0x52, 0x05, 0x6e, 0x6f, 0x64,
	0x65, 0x73, 0x12, 0x24, 0x0a, 0x0b, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x48, 0x00, 0x52, 0x0a, 0x70, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x21, 0x0a, 0x09, 0x74, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x03, 0x48, 0x01, 0x52, 0x09, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x88, 0x01, 0x01, 0x42, 0x0e, 0x0a, 0x0c, 0x5f,
	0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x42, 0x0c, 0x0a, 0x0a, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x22, 0xbe, 0x02, 0x0a, 0x10, 0x4e, 0x6f,
	0x64, 0x65, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x12,
	0x0a, 0x04, 0x73, 0x6b, 0x69, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x04, 0x73, 0x6b,
	0x69, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x72, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x72, 0x72, 0x4d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x12, 0x27, 0x0a, 0x0f, 0x75, 0x6e, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79,
	0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0e, 0x75, 0x6e,
	0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x4e, 0x6f, 0x64, 0x65, 0x73, 0x12, 0x25, 0x0a, 0x0e,
	0x65, 0x76, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x04,
	0x20, 0x03, 0x28, 0x09, 0x52, 0x0d, 0x65, 0x76, 0x69, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x6f,
	0x64, 0x65, 0x73, 0x12, 0x2a, 0x0a, 0x11, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x63, 0x68,
	0x65, 0x63, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f,
	0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x1b, 0x0a, 0x09, 0x72, 0x69, 0x73, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x72, 0x69, 0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05,
	0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x6c, 0x65, 0x76,
	0x65, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69, 0x6c, 0x12, 0x1a,
	0x0a, 0x08, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x73, 0x6f, 0x6c, 0x75, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xee, 0x01, 0x0a, 0x17, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x72, 0x79, 0x52, 0x75, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x18, 0x0a, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63,
	0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75, 0x73,
	0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x63, 0x6f, 0x6d, 0x70, 0x6f,
	0x6e, 0x65, 0x6e, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x63, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b,
	0x0a, 0x09, 0x6d, 0x65, 0x74, 0x61, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x6d, 0x65, 0x74, 0x61, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x74, 0x61, 0x73, 0x6b, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x74, 0x61, 0x73, 0x6b,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x74, 0x61, 0x73, 0x6b, 0x54, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x22, 0x30, 0x0a, 0x15, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x72, 0x79, 0x52, 0x75, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0x2f, 0x0a,
	0x14, 0x47, 0x65, 0x74, 0x44, 0x72, 0x79, 0x52, 0x75, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0x6d,
	0x0a, 0x12, 0x47, 0x65, 0x74, 0x44, 0x72, 0x79, 0x52, 0x75, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x25, 0x0a, 0x0e,
	0x65, 0x6c, 0x61, 0x70, 0x73, 0x65, 0x64, 0x5f, 0x73, 0x65, 0x63, 0x6f, 0x6e, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0d, 0x52, 0x0d, 0x65, 0x6c, 0x61, 0x70, 0x73, 0x65, 0x64, 0x53, 0x65, 0x63,
	0x6f, 0x6e, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x44, 0x0a,
	0x11, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x74, 0x61, 0x73,
	0x6b, 0x49, 0x64, 0x22, 0x32, 0x0a, 0x0f, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x72, 0x72, 0x5f, 0x6d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x72, 0x72,
	0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x22, 0x46, 0x0a, 0x13, 0x52, 0x6f, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22,
	0x4d, 0x0a, 0x11, 0x52, 0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x65, 0x70, 0x6c, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x72, 0x72, 0x5f, 0x6d, 0x65, 0x73, 0x73,
	0x61, 0x67, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x72, 0x72, 0x4d, 0x65,
	0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x22, 0xda,
	0x04, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x70, 0x68, 0x61, 0x73, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x70, 0x68, 0x61, 0x73, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1c, 0x0a, 0x09,
	0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x6d, 0x65,
	0x74, 0x61, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x6d, 0x65, 0x74, 0x61, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x72, 0x6f,
	0x64, 0x75, 0x63, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x70, 0x72, 0x6f, 0x64, 0x75, 0x63, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x69, 0x6d, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x61, 0x67, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x69, 0x6d, 0x61, 0x67, 0x65, 0x54, 0x61, 0x67, 0x12, 0x12, 0x0a, 0x04, 0x65, 0x6e, 0x76,
	0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x65, 0x6e, 0x76, 0x73, 0x12, 0x14, 0x0a,
	0x05, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x73, 0x65, 0x72, 0x18, 0x0d, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x75, 0x73, 0x65, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x6e,
	0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78,
	0x74, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x67, 0x79, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x74, 0x72, 0x61,
	0x74, 0x65, 0x67, 0x79, 0x12, 0x25, 0x0a, 0x0e, 0x70, 0x6c, 0x75, 0x67, 0x69, 0x6e, 0x5f, 0x76,
	0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x70, 0x6c,
	0x75, 0x67, 0x69, 0x6e, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x18, 0x0a, 0x07, 0x74,
	0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x11, 0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x69,
	0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x12, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65,
	0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x62, 0x61, 0x74, 0x63, 0x68, 0x5f, 0x69, 0x64, 0x18, 0x13,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x62, 0x61, 0x74, 0x63, 0x68, 0x49, 0x64, 0x12, 0x19, 0x0a,
	0x08, 0x74, 0x72, 0x61, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x14, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x74, 0x72, 0x61, 0x63, 0x65, 0x49, 0x64, 0x12, 0x21, 0x0a, 0x0c, 0x63, 0x6c, 0x69, 0x65,
	0x6e, 0x74, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x15, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0x56, 0x0a, 0x0f, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x17,
	0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52,
	0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x72,
	0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61,
	0x73, 0x6f, 0x6e, 0x22, 0x99, 0x01, 0x0a, 0x13, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x17, 0x0a, 0x07, 0x74,
	0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61,
	0x73, 0x6b, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49,
	0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x22,
	0xd9, 0x03, 0x0a, 0x11, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70,
	0x65, 0x12, 0x14, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x73, 0x74, 0x61, 0x67, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x65, 0x6e, 0x76, 0x73, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x65, 0x6e, 0x76, 0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x65,
	0x78, 0x74, 0x65, 0x6e, 0x64, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x6e, 0x64, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x16, 0x0a, 0x06,
	0x72, 0x65, 0x67, 0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65,
	0x67, 0x69, 0x6f, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x18, 0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x0a,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x1b, 0x0a,
	0x09, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x63, 0x68, 0x61, 0x6e, 0x67, 0x65, 0x49, 0x64, 0x12, 0x14, 0x0a, 0x05, 0x62, 0x61,
	0x74, 0x63, 0x68, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x62, 0x61, 0x74, 0x63, 0x68,
	0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73,
	0x6f, 0x6e, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e,
	0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x27, 0x0a, 0x08, 0x73, 0x75, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x18, 0x11,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x0b, 0x2e, 0x70, 0x62, 0x2e, 0x53, 0x75, 0x62, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x08, 0x73, 0x75, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x73, 0x22, 0xb0, 0x02, 0x0a, 0x07,
	0x53, 0x75, 0x62, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x75, 0x62,
	0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0e, 0x70, 0x61, 0x72, 0x65, 0x6e, 0x74,
	0x5f, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x0c,
	0x70, 0x61, 0x72, 0x65, 0x6e, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f,
	0x6e, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12,
	0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x63, 0x6f, 0x73, 0x74, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x1e,
	0x0a, 0x05, 0x72, 0x69, 0x73, 0x6b, 0x73, 0x18, 0x0a, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x08, 0x2e,
	0x70, 0x62, 0x2e, 0x52, 0x69, 0x73, 0x6b, 0x52, 0x05, 0x72, 0x69, 0x73, 0x6b, 0x73, 0x22, 0x8f,
	0x02, 0x0a, 0x04, 0x52, 0x69, 0x73, 0x6b, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1d, 0x0a, 0x0a, 0x73, 0x75, 0x62, 0x74, 0x61,
	0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x09, 0x73, 0x75, 0x62,
	0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x70, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x70, 0x70, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x64, 0x65, 0x74,
	0x61, 0x69, 0x6c, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x64, 0x65, 0x74, 0x61, 0x69,
	0x6c, 0x12, 0x14, 0x0a, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x6c, 0x65, 0x76, 0x65, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x73, 0x6f, 0x6c, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6f, 0x6c, 0x75, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x1f, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x2a, 0x0a, 0x11, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x63,
	0x68, 0x65, 0x63, 0x6b, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0xb1, 0x02, 0x0a, 0x17, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52,
	0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x1d, 0x0a, 0x0a,
	0x73, 0x75, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x03,
	0x52, 0x09, 0x73, 0x75, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x61,
	0x70, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61,
	0x70, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x16,
	0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x63, 0x6f, 0x73, 0x74, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x63, 0x6f, 0x73, 0x74, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x03, 0x52, 0x06, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x05,
	0x72, 0x69, 0x73, 0x6b, 0x73, 0x18, 0x09, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x08, 0x2e, 0x70, 0x62,
	0x2e, 0x52, 0x69, 0x73, 0x6b, 0x52, 0x05, 0x72, 0x69, 0x73, 0x6b, 0x73, 0x12, 0x16, 0x0a, 0x06,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x61, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x22, 0x62, 0x0a, 0x15, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x12, 0x1d, 0x0a,
	0x0a, 0x73, 0x75, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x03, 0x52, 0x09, 0x73, 0x75, 0x62, 0x74, 0x61, 0x73, 0x6b, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x63, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x63, 0x6f, 0x64, 0x65,
	0x12, 0x16, 0x0a, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x72, 0x65, 0x61, 0x73, 0x6f, 0x6e, 0x32, 0xd7, 0x05, 0x0a, 0x0d, 0x48, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x65, 0x72, 0x12, 0x4e, 0x0a, 0x12, 0x49, 0x73,
	0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79,
	0x12, 0x1b, 0x2e, 0x70, 0x62, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x48,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e,
	0x70, 0x62, 0x2e, 0x43, 0x6f, 0x6d, 0x70, 0x6f, 0x6e, 0x65, 0x6e, 0x74, 0x48, 0x65, 0x61, 0x6c,
	0x74, 0x68, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x3f, 0x0a, 0x0d, 0x49, 0x73,
	0x4e, 0x6f, 0x64, 0x65, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x12, 0x16, 0x2e, 0x70, 0x62,
	0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x79, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x14, 0x2e, 0x70, 0x62, 0x2e, 0x4e, 0x6f, 0x64, 0x65, 0x48, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x79, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x4c, 0x0a, 0x10, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x72, 0x79, 0x52, 0x75, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x12,
	0x1b, 0x2e, 0x70, 0x62, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x72, 0x79, 0x52, 0x75,
	0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x19, 0x2e, 0x70,
	0x62, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x72, 0x79, 0x52, 0x75, 0x6e, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x49, 0x0a, 0x13, 0x47, 0x65, 0x74,
	0x44, 0x72, 0x79, 0x52, 0x75, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74,
	0x12, 0x18, 0x2e, 0x70, 0x62, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x72, 0x79, 0x52, 0x75, 0x6e, 0x54,
	0x61, 0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x70, 0x62, 0x2e,
	0x47, 0x65, 0x74, 0x44, 0x72, 0x79, 0x52, 0x75, 0x6e, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70,
	0x6c, 0x79, 0x22, 0x00, 0x12, 0x3a, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61,
	0x73, 0x6b, 0x12, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61,
	0x73, 0x6b, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x70, 0x62, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00,
	0x12, 0x4c, 0x0a, 0x10, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x1b, 0x2e, 0x70, 0x62, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74,
	0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x19, 0x2e, 0x70, 0x62, 0x2e, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73,
	0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x40,
	0x0a, 0x0c, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x17,
	0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x73, 0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x61, 0x73, 0x6b,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x44, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x62, 0x65, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00,
	0x12, 0x3a, 0x0a, 0x0a, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x15,
	0x2e, 0x70, 0x62, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65, 0x6c, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x13, 0x2e, 0x70, 0x62, 0x2e, 0x43, 0x61, 0x6e, 0x63, 0x65,
	0x6c, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x40, 0x0a, 0x0c,
	0x52, 0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x61, 0x73, 0x6b, 0x12, 0x17, 0x2e, 0x70,
	0x62, 0x2e, 0x52, 0x6f, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x15, 0x2e, 0x70, 0x62, 0x2e, 0x52, 0x6f, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x70, 0x6c, 0x79, 0x22, 0x00, 0x12, 0x52,
	0x0a, 0x12, 0x54, 0x50, 0x52, 0x65, 0x70, 0x6f, 0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65,
	0x73, 0x75, 0x6c, 0x74, 0x12, 0x1d, 0x2e, 0x70, 0x62, 0x2e, 0x54, 0x50, 0x52, 0x65, 0x70, 0x6f,
	0x72, 0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x1b, 0x2e, 0x70, 0x62, 0x2e, 0x54, 0x50, 0x52, 0x65, 0x70, 0x6f, 0x72,
	0x74, 0x54, 0x61, 0x73, 0x6b, 0x52, 0x65, 0x73, 0x75, 0x6c, 0x74, 0x52, 0x65, 0x70, 0x6c, 0x79,
	0x22, 0x00, 0x42, 0x40, 0x0a, 0x13, 0x69, 0x6f, 0x2e, 0x67, 0x72, 0x70, 0x63, 0x2e, 0x73, 0x74,
	0x61, 0x72, 0x73, 0x68, 0x69, 0x70, 0x2e, 0x70, 0x62, 0x42, 0x07, 0x50, 0x62, 0x50, 0x72, 0x6f,
	0x74, 0x6f, 0x50, 0x01, 0x5a, 0x1e, 0x67, 0x69, 0x74, 0x2e, 0x77, 0x6f, 0x61, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x6b, 0x6d, 0x65, 0x74, 0x69, 0x73, 0x2f, 0x73, 0x74, 0x61, 0x72, 0x73, 0x68, 0x69,
	0x70, 0x2f, 0x70, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_pb_starship_proto_rawDescOnce sync.Once
	file_pb_starship_proto_rawDescData = file_pb_starship_proto_rawDesc
)

func file_pb_starship_proto_rawDescGZIP() []byte {
	file_pb_starship_proto_rawDescOnce.Do(func() {
		file_pb_starship_proto_rawDescData = protoimpl.X.CompressGZIP(file_pb_starship_proto_rawDescData)
	})
	return file_pb_starship_proto_rawDescData
}

var file_pb_starship_proto_msgTypes = make([]protoimpl.MessageInfo, 20)
var file_pb_starship_proto_goTypes = []interface{}{
	(*ComponentHealthyRequest)(nil),   // 0: pb.ComponentHealthyRequest
	(*ComponentHealthyReply)(nil),     // 1: pb.ComponentHealthyReply
	(*NodeHealthyRequest)(nil),        // 2: pb.NodeHealthyRequest
	(*NodeHealthyReply)(nil),          // 3: pb.NodeHealthyReply
	(*CreateDryRunTaskRequest)(nil),   // 4: pb.CreateDryRunTaskRequest
	(*CreateDryRunTaskReply)(nil),     // 5: pb.CreateDryRunTaskReply
	(*GetDryRunTaskRequest)(nil),      // 6: pb.GetDryRunTaskRequest
	(*GetDryRunTaskReply)(nil),        // 7: pb.GetDryRunTaskReply
	(*CancelTaskRequest)(nil),         // 8: pb.CancelTaskRequest
	(*CancelTaskReply)(nil),           // 9: pb.CancelTaskReply
	(*RollbackTaskRequest)(nil),       // 10: pb.RollbackTaskRequest
	(*RollbackTaskReply)(nil),         // 11: pb.RollbackTaskReply
	(*CreateTaskRequest)(nil),         // 12: pb.CreateTaskRequest
	(*CreateTaskReply)(nil),           // 13: pb.CreateTaskReply
	(*DescribeTaskRequest)(nil),       // 14: pb.DescribeTaskRequest
	(*DescribeTaskReply)(nil),         // 15: pb.DescribeTaskReply
	(*SubTask)(nil),                   // 16: pb.SubTask
	(*Risk)(nil),                      // 17: pb.Risk
	(*ReportTaskResultRequest)(nil),   // 18: pb.ReportTaskResultRequest
	(*ReportTaskResultReply)(nil),     // 19: pb.ReportTaskResultReply
	(*TPReportTaskResultRequest)(nil), // 20: pb.TPReportTaskResultRequest
	(*TPReportTaskResultReply)(nil),   // 21: pb.TPReportTaskResultReply
}
var file_pb_starship_proto_depIdxs = []int32{
	16, // 0: pb.DescribeTaskReply.subtasks:type_name -> pb.SubTask
	17, // 1: pb.SubTask.risks:type_name -> pb.Risk
	17, // 2: pb.ReportTaskResultRequest.risks:type_name -> pb.Risk
	0,  // 3: pb.HealthChecker.IsComponentHealthy:input_type -> pb.ComponentHealthyRequest
	2,  // 4: pb.HealthChecker.IsNodeHealthy:input_type -> pb.NodeHealthyRequest
	4,  // 5: pb.HealthChecker.CreateDryRunTask:input_type -> pb.CreateDryRunTaskRequest
	6,  // 6: pb.HealthChecker.GetDryRunTaskResult:input_type -> pb.GetDryRunTaskRequest
	12, // 7: pb.HealthChecker.CreateTask:input_type -> pb.CreateTaskRequest
	18, // 8: pb.HealthChecker.ReportTaskResult:input_type -> pb.ReportTaskResultRequest
	14, // 9: pb.HealthChecker.DescribeTask:input_type -> pb.DescribeTaskRequest
	8,  // 10: pb.HealthChecker.CancelTask:input_type -> pb.CancelTaskRequest
	10, // 11: pb.HealthChecker.RollbackTask:input_type -> pb.RollbackTaskRequest
	20, // 12: pb.HealthChecker.TPReportTaskResult:input_type -> pb.TPReportTaskResultRequest
	1,  // 13: pb.HealthChecker.IsComponentHealthy:output_type -> pb.ComponentHealthyReply
	3,  // 14: pb.HealthChecker.IsNodeHealthy:output_type -> pb.NodeHealthyReply
	5,  // 15: pb.HealthChecker.CreateDryRunTask:output_type -> pb.CreateDryRunTaskReply
	7,  // 16: pb.HealthChecker.GetDryRunTaskResult:output_type -> pb.GetDryRunTaskReply
	13, // 17: pb.HealthChecker.CreateTask:output_type -> pb.CreateTaskReply
	19, // 18: pb.HealthChecker.ReportTaskResult:output_type -> pb.ReportTaskResultReply
	15, // 19: pb.HealthChecker.DescribeTask:output_type -> pb.DescribeTaskReply
	9,  // 20: pb.HealthChecker.CancelTask:output_type -> pb.CancelTaskReply
	11, // 21: pb.HealthChecker.RollbackTask:output_type -> pb.RollbackTaskReply
	21, // 22: pb.HealthChecker.TPReportTaskResult:output_type -> pb.TPReportTaskResultReply
	13, // [13:23] is the sub-list for method output_type
	3,  // [3:13] is the sub-list for method input_type
	3,  // [3:3] is the sub-list for extension type_name
	3,  // [3:3] is the sub-list for extension extendee
	0,  // [0:3] is the sub-list for field type_name
}

func init() { file_pb_starship_proto_init() }
func file_pb_starship_proto_init() {
	if File_pb_starship_proto != nil {
		return
	}
	file_pb_tp_rpc_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_pb_starship_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ComponentHealthyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_starship_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ComponentHealthyReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_starship_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NodeHealthyRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_starship_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NodeHealthyReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_starship_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDryRunTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_starship_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDryRunTaskReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_starship_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDryRunTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_starship_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDryRunTaskReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_starship_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_starship_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CancelTaskReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_starship_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RollbackTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_starship_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RollbackTaskReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_starship_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_starship_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTaskReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_starship_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeTaskRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_starship_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DescribeTaskReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_starship_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubTask); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_starship_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Risk); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_starship_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportTaskResultRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_pb_starship_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReportTaskResultReply); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_pb_starship_proto_msgTypes[0].OneofWrappers = []interface{}{}
	file_pb_starship_proto_msgTypes[2].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_pb_starship_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   20,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_pb_starship_proto_goTypes,
		DependencyIndexes: file_pb_starship_proto_depIdxs,
		MessageInfos:      file_pb_starship_proto_msgTypes,
	}.Build()
	File_pb_starship_proto = out.File
	file_pb_starship_proto_rawDesc = nil
	file_pb_starship_proto_goTypes = nil
	file_pb_starship_proto_depIdxs = nil
}
