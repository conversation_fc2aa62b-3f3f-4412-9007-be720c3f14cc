// Copyright 2015 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

option go_package = "git.woa.com/kmetis/starship/pb";
option java_multiple_files = true;
option java_package = "io.grpc.starship.pb";
option java_outer_classname = "PbProto";

package pb;

import "pb/tp_rpc.proto";

// The HealthChecker service definition.
service HealthChecker {
  // IsComponentHealthy Check if component is healthy
  rpc IsComponentHealthy (ComponentHealthyRequest) returns (ComponentHealthyReply) {}

  // IsNodeHealthy Check if node is healthy
  rpc IsNodeHealthy (NodeHealthyRequest) returns (NodeHealthyReply) {}

  // CreateDryRunTask Creates dry run task for specified component
  rpc CreateDryRunTask(CreateDryRunTaskRequest) returns (CreateDryRunTaskReply) {}

  // GetDryRunTaskResult get the result of dry run task
  rpc GetDryRunTaskResult(GetDryRunTaskRequest) returns (GetDryRunTaskReply) {}

  // CreateTask Creates task, such as precheck, aftercheck, etc. for specified component
  rpc CreateTask(CreateTaskRequest) returns (CreateTaskReply) {}

  // ReportTaskResult report the result of task
  rpc ReportTaskResult(ReportTaskResultRequest) returns (ReportTaskResultReply) {}

  // DescribeTask get the result of task
  rpc DescribeTask(DescribeTaskRequest) returns (DescribeTaskReply) {}

  // CancelTask cancel task
  rpc CancelTask(CancelTaskRequest) returns (CancelTaskReply) {}

  // RollbackTask rollback task
  rpc RollbackTask(RollbackTaskRequest) returns (RollbackTaskReply) {}

  // TPReportTaskResult report the result of task
  rpc TPReportTaskResult(TPReportTaskResultRequest) returns (TPReportTaskResultReply) {}
}

message ComponentHealthyRequest {
  string product = 1;
  string region = 2;
  string cluster_id = 3;
  string component_name = 4;
  optional string meta_type = 5;
  optional string policy_name = 6;
}

message ComponentHealthyReply {
  string err_message = 1;
  string health_check_name = 2;   // 健康检查项名称
  string risk_name = 3;           // 隐患名称
  string level = 4;               // 隐患级别
  string code = 5;                // 隐患错误码，可选值：PASS、FAILED、ERROR
  string detail = 6;              // 隐患详情，检查不通过时，提示不通过原因，检查失败时，上报错误信息
  string solution = 7;            // 解决方案，检查不通过时，提示对应的解决方案
}

message NodeHealthyRequest {
  string product = 1;
  string region = 2;
  string cluster_id = 3;
  repeated string nodes = 4;
  optional string policy_name = 5;
  optional int64 timestamp = 6; // milliseconds(time.UnixMilli)
}

message NodeHealthyReply {
  bool skip = 1;
  string err_message = 2;
  repeated string unhealthy_nodes = 3;
  repeated string eviction_nodes = 4;
  string   health_check_name = 5;   // 健康检查项名称
  string   risk_name = 6;           // 隐患名称
  string   level = 7;               // 隐患级别
  string   code = 8;                // 隐患错误码，可选值：PASS、FAILED、ERROR
  string   detail = 9;              // 隐患详情，检查不通过时，提示不通过原因，检查失败时，上报错误信息
  string   solution = 10;           // 解决方案，检查不通过时，提示对应的解决方案
}

message CreateDryRunTaskRequest {
  string product = 1;
  string region = 2;
  string cluster_id = 3;
  string component_name = 4;
  string meta_type = 5;
  string task_name = 6;
  string task_timeout = 7;
}

message CreateDryRunTaskReply {
  string task_id = 1;
}


message GetDryRunTaskRequest {
  string task_id = 1;
}

message GetDryRunTaskReply {
  string status = 1;
  uint32 elapsed_second = 2;
  string message = 3;
}

message CancelTaskRequest {
  string region =1;
  string task_id = 2;
}

message CancelTaskReply {
  string err_message = 1;
}

message RollbackTaskRequest {
  string region =1;
  int64 task_id = 2;
}

message RollbackTaskReply {
  string err_message = 1;
  int64 task_id = 2;
}

// 创建任务请求
message CreateTaskRequest {
  string name = 1;            // 任务名称
  string app_name = 2;        // app名称
  string type = 3;            // 任务类型
  string phase = 4;           // 任务阶段
  string cluster_id = 5;      // 集群id
  string namespace = 6;       // 负载的namespace
  string metacluster_id = 7;  // meta集群id
  string region = 8;          // 地域信息
  string product_name = 9;    // productname
  string image_tag = 10;      // 镜像tag
  string envs = 11;           // 环境变量列表
  string token = 12;          // token信息
  string user = 13;           // 变更执行人
  string extend_info = 14;    // 扩展信息
  string strategy = 15;       // 发布策略
  string plugin_version = 16; // 插件版本
  int32  timeout = 17;        // 超时时间, 单位：秒
  string change_id = 18;      // 发布单id
  int32  batch_id = 19;       // 变更批次id
  string trace_id = 20;       // traceid
  string client_token = 21;   // 客户端token
}

// 创建任务返回体
message CreateTaskReply {
  int64  task_id = 1; // 任务id
  int32  code = 2;    // 返回码
  string reason = 3;  // 失败原因
}

// 查询任务详情请求
message DescribeTaskRequest {
  int64  task_id = 1;    // 任务id
  string change_id = 2;  // 发布单id
  string cluster_id = 3; // 集群id
  string app_name = 4;   // app名称
  string type = 5;       // 任务类型
}

// 查询任务详情返回体
message DescribeTaskReply {
  int64    task_id = 1;            // 任务id
  string   name = 2;               // 任务名称
  string   app_name = 3;           // app名称
  string   type = 4;               // 任务类型
  string   stage = 5;              // 任务阶段
  string   envs = 6;               // 环境变量列表
  string   extend_info = 7;        // 扩展信息，比如镜像tag等
  string   region = 8;             // 地域信息
  string   cluster_id = 9;         // 集群id
  int32    timeout = 10;           // 任务超时控制
  string   change_id = 11;         // 发布单id
  int32    batch = 12;             // 变更批次
  string   status = 13;            // 任务状态
  string   reason = 14;            // 失败原因
  string   update_time = 15;       // 更新时间
  string   create_time = 16;       // 创建时间
  repeated SubTask subtasks = 17;  // 子任务信息
}

// 子任务信息
message SubTask {
  int64    subtask_id = 1;     // 子任务id
  int64    parent_task_id = 2; // 父任务id
  string   app_name = 3;       // app名称
  string   action = 4;         // 操作类型
  string   status = 5;         // 任务状态
  string   reason = 6;         // 任务失败原因
  int32    cost_time = 7;      // 发布耗时
  string   update_time = 8;     // 更新时间
  string   create_time = 9;     // 创建时间
  repeated Risk risks = 10;    // 风险信息
}

// 风险信息
message Risk {
  int64  id = 1;                 // 主键id
  int64  subtask_id = 2;         // 子任务id
  string app_name = 3;           // 组件名称
  string name = 4;               // 隐患名称
  string code = 5;               // 隐患错误码
  string detail = 6;             // 隐患详情
  string level = 7;              // 隐患级别
  string solution = 8;           // 解决方案
  string create_time = 9;         // 创建时间
  string health_check_name = 10; // 健康检查项名称
}

// 上报任务执行结果请求
message ReportTaskResultRequest {
  int64    subtask_id = 1;     // 子任务id
  string   app_name = 2;       // app名称
  string   status = 3;         // 任务状态
  string   reason = 4;         // 失败原因
  int32    cost_time = 5;      // 发布耗时
  string   update_time = 6;    // 更新时间
  string   cluster_id = 7;     // 集群id
  int64    task_id = 8;        // 任务id
  repeated Risk risks = 9;     // 风险信息
  string   action = 10;        // 操作类型
}

// 上报任务执行结果返回体
message ReportTaskResultReply {
  int64  subtask_id = 1; // 任务id
  int32  code = 2;       // 返回码
  string reason = 3;     // 失败原因
}
