// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v4.25.1
// source: pb/starship.proto

package pb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// HealthCheckerClient is the client API for HealthChecker service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type HealthCheckerClient interface {
	// IsComponentHealthy Check if component is healthy
	IsComponentHealthy(ctx context.Context, in *ComponentHealthyRequest, opts ...grpc.CallOption) (*ComponentHealthyReply, error)
	// IsNodeHealthy Check if node is healthy
	IsNodeHealthy(ctx context.Context, in *NodeHealthyRequest, opts ...grpc.CallOption) (*NodeHealthyReply, error)
	// CreateDryRunTask Creates dry run task for specified component
	CreateDryRunTask(ctx context.Context, in *CreateDryRunTaskRequest, opts ...grpc.CallOption) (*CreateDryRunTaskReply, error)
	// GetDryRunTaskResult get the result of dry run task
	GetDryRunTaskResult(ctx context.Context, in *GetDryRunTaskRequest, opts ...grpc.CallOption) (*GetDryRunTaskReply, error)
	// CreateTask Creates task, such as precheck, aftercheck, etc. for specified component
	CreateTask(ctx context.Context, in *CreateTaskRequest, opts ...grpc.CallOption) (*CreateTaskReply, error)
	// ReportTaskResult report the result of task
	ReportTaskResult(ctx context.Context, in *ReportTaskResultRequest, opts ...grpc.CallOption) (*ReportTaskResultReply, error)
	// DescribeTask get the result of task
	DescribeTask(ctx context.Context, in *DescribeTaskRequest, opts ...grpc.CallOption) (*DescribeTaskReply, error)
	// CancelTask cancel task
	CancelTask(ctx context.Context, in *CancelTaskRequest, opts ...grpc.CallOption) (*CancelTaskReply, error)
	// RollbackTask rollback task
	RollbackTask(ctx context.Context, in *RollbackTaskRequest, opts ...grpc.CallOption) (*RollbackTaskReply, error)
	// TPReportTaskResult report the result of task
	TPReportTaskResult(ctx context.Context, in *TPReportTaskResultRequest, opts ...grpc.CallOption) (*TPReportTaskResultReply, error)
}

type healthCheckerClient struct {
	cc grpc.ClientConnInterface
}

func NewHealthCheckerClient(cc grpc.ClientConnInterface) HealthCheckerClient {
	return &healthCheckerClient{cc}
}

func (c *healthCheckerClient) IsComponentHealthy(ctx context.Context, in *ComponentHealthyRequest, opts ...grpc.CallOption) (*ComponentHealthyReply, error) {
	out := new(ComponentHealthyReply)
	err := c.cc.Invoke(ctx, "/pb.HealthChecker/IsComponentHealthy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *healthCheckerClient) IsNodeHealthy(ctx context.Context, in *NodeHealthyRequest, opts ...grpc.CallOption) (*NodeHealthyReply, error) {
	out := new(NodeHealthyReply)
	err := c.cc.Invoke(ctx, "/pb.HealthChecker/IsNodeHealthy", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *healthCheckerClient) CreateDryRunTask(ctx context.Context, in *CreateDryRunTaskRequest, opts ...grpc.CallOption) (*CreateDryRunTaskReply, error) {
	out := new(CreateDryRunTaskReply)
	err := c.cc.Invoke(ctx, "/pb.HealthChecker/CreateDryRunTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *healthCheckerClient) GetDryRunTaskResult(ctx context.Context, in *GetDryRunTaskRequest, opts ...grpc.CallOption) (*GetDryRunTaskReply, error) {
	out := new(GetDryRunTaskReply)
	err := c.cc.Invoke(ctx, "/pb.HealthChecker/GetDryRunTaskResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *healthCheckerClient) CreateTask(ctx context.Context, in *CreateTaskRequest, opts ...grpc.CallOption) (*CreateTaskReply, error) {
	out := new(CreateTaskReply)
	err := c.cc.Invoke(ctx, "/pb.HealthChecker/CreateTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *healthCheckerClient) ReportTaskResult(ctx context.Context, in *ReportTaskResultRequest, opts ...grpc.CallOption) (*ReportTaskResultReply, error) {
	out := new(ReportTaskResultReply)
	err := c.cc.Invoke(ctx, "/pb.HealthChecker/ReportTaskResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *healthCheckerClient) DescribeTask(ctx context.Context, in *DescribeTaskRequest, opts ...grpc.CallOption) (*DescribeTaskReply, error) {
	out := new(DescribeTaskReply)
	err := c.cc.Invoke(ctx, "/pb.HealthChecker/DescribeTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *healthCheckerClient) CancelTask(ctx context.Context, in *CancelTaskRequest, opts ...grpc.CallOption) (*CancelTaskReply, error) {
	out := new(CancelTaskReply)
	err := c.cc.Invoke(ctx, "/pb.HealthChecker/CancelTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *healthCheckerClient) RollbackTask(ctx context.Context, in *RollbackTaskRequest, opts ...grpc.CallOption) (*RollbackTaskReply, error) {
	out := new(RollbackTaskReply)
	err := c.cc.Invoke(ctx, "/pb.HealthChecker/RollbackTask", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *healthCheckerClient) TPReportTaskResult(ctx context.Context, in *TPReportTaskResultRequest, opts ...grpc.CallOption) (*TPReportTaskResultReply, error) {
	out := new(TPReportTaskResultReply)
	err := c.cc.Invoke(ctx, "/pb.HealthChecker/TPReportTaskResult", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// HealthCheckerServer is the server API for HealthChecker service.
// All implementations must embed UnimplementedHealthCheckerServer
// for forward compatibility
type HealthCheckerServer interface {
	// IsComponentHealthy Check if component is healthy
	IsComponentHealthy(context.Context, *ComponentHealthyRequest) (*ComponentHealthyReply, error)
	// IsNodeHealthy Check if node is healthy
	IsNodeHealthy(context.Context, *NodeHealthyRequest) (*NodeHealthyReply, error)
	// CreateDryRunTask Creates dry run task for specified component
	CreateDryRunTask(context.Context, *CreateDryRunTaskRequest) (*CreateDryRunTaskReply, error)
	// GetDryRunTaskResult get the result of dry run task
	GetDryRunTaskResult(context.Context, *GetDryRunTaskRequest) (*GetDryRunTaskReply, error)
	// CreateTask Creates task, such as precheck, aftercheck, etc. for specified component
	CreateTask(context.Context, *CreateTaskRequest) (*CreateTaskReply, error)
	// ReportTaskResult report the result of task
	ReportTaskResult(context.Context, *ReportTaskResultRequest) (*ReportTaskResultReply, error)
	// DescribeTask get the result of task
	DescribeTask(context.Context, *DescribeTaskRequest) (*DescribeTaskReply, error)
	// CancelTask cancel task
	CancelTask(context.Context, *CancelTaskRequest) (*CancelTaskReply, error)
	// RollbackTask rollback task
	RollbackTask(context.Context, *RollbackTaskRequest) (*RollbackTaskReply, error)
	// TPReportTaskResult report the result of task
	TPReportTaskResult(context.Context, *TPReportTaskResultRequest) (*TPReportTaskResultReply, error)
	mustEmbedUnimplementedHealthCheckerServer()
}

// UnimplementedHealthCheckerServer must be embedded to have forward compatible implementations.
type UnimplementedHealthCheckerServer struct {
}

func (UnimplementedHealthCheckerServer) IsComponentHealthy(context.Context, *ComponentHealthyRequest) (*ComponentHealthyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsComponentHealthy not implemented")
}
func (UnimplementedHealthCheckerServer) IsNodeHealthy(context.Context, *NodeHealthyRequest) (*NodeHealthyReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method IsNodeHealthy not implemented")
}
func (UnimplementedHealthCheckerServer) CreateDryRunTask(context.Context, *CreateDryRunTaskRequest) (*CreateDryRunTaskReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDryRunTask not implemented")
}
func (UnimplementedHealthCheckerServer) GetDryRunTaskResult(context.Context, *GetDryRunTaskRequest) (*GetDryRunTaskReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDryRunTaskResult not implemented")
}
func (UnimplementedHealthCheckerServer) CreateTask(context.Context, *CreateTaskRequest) (*CreateTaskReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTask not implemented")
}
func (UnimplementedHealthCheckerServer) ReportTaskResult(context.Context, *ReportTaskResultRequest) (*ReportTaskResultReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReportTaskResult not implemented")
}
func (UnimplementedHealthCheckerServer) DescribeTask(context.Context, *DescribeTaskRequest) (*DescribeTaskReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DescribeTask not implemented")
}
func (UnimplementedHealthCheckerServer) CancelTask(context.Context, *CancelTaskRequest) (*CancelTaskReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CancelTask not implemented")
}
func (UnimplementedHealthCheckerServer) RollbackTask(context.Context, *RollbackTaskRequest) (*RollbackTaskReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RollbackTask not implemented")
}
func (UnimplementedHealthCheckerServer) TPReportTaskResult(context.Context, *TPReportTaskResultRequest) (*TPReportTaskResultReply, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TPReportTaskResult not implemented")
}
func (UnimplementedHealthCheckerServer) mustEmbedUnimplementedHealthCheckerServer() {}

// UnsafeHealthCheckerServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to HealthCheckerServer will
// result in compilation errors.
type UnsafeHealthCheckerServer interface {
	mustEmbedUnimplementedHealthCheckerServer()
}

func RegisterHealthCheckerServer(s grpc.ServiceRegistrar, srv HealthCheckerServer) {
	s.RegisterService(&HealthChecker_ServiceDesc, srv)
}

func _HealthChecker_IsComponentHealthy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ComponentHealthyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HealthCheckerServer).IsComponentHealthy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.HealthChecker/IsComponentHealthy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HealthCheckerServer).IsComponentHealthy(ctx, req.(*ComponentHealthyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HealthChecker_IsNodeHealthy_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(NodeHealthyRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HealthCheckerServer).IsNodeHealthy(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.HealthChecker/IsNodeHealthy",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HealthCheckerServer).IsNodeHealthy(ctx, req.(*NodeHealthyRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HealthChecker_CreateDryRunTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDryRunTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HealthCheckerServer).CreateDryRunTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.HealthChecker/CreateDryRunTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HealthCheckerServer).CreateDryRunTask(ctx, req.(*CreateDryRunTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HealthChecker_GetDryRunTaskResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDryRunTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HealthCheckerServer).GetDryRunTaskResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.HealthChecker/GetDryRunTaskResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HealthCheckerServer).GetDryRunTaskResult(ctx, req.(*GetDryRunTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HealthChecker_CreateTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HealthCheckerServer).CreateTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.HealthChecker/CreateTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HealthCheckerServer).CreateTask(ctx, req.(*CreateTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HealthChecker_ReportTaskResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReportTaskResultRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HealthCheckerServer).ReportTaskResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.HealthChecker/ReportTaskResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HealthCheckerServer).ReportTaskResult(ctx, req.(*ReportTaskResultRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HealthChecker_DescribeTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DescribeTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HealthCheckerServer).DescribeTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.HealthChecker/DescribeTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HealthCheckerServer).DescribeTask(ctx, req.(*DescribeTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HealthChecker_CancelTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CancelTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HealthCheckerServer).CancelTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.HealthChecker/CancelTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HealthCheckerServer).CancelTask(ctx, req.(*CancelTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HealthChecker_RollbackTask_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RollbackTaskRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HealthCheckerServer).RollbackTask(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.HealthChecker/RollbackTask",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HealthCheckerServer).RollbackTask(ctx, req.(*RollbackTaskRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _HealthChecker_TPReportTaskResult_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TPReportTaskResultRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(HealthCheckerServer).TPReportTaskResult(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/pb.HealthChecker/TPReportTaskResult",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(HealthCheckerServer).TPReportTaskResult(ctx, req.(*TPReportTaskResultRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// HealthChecker_ServiceDesc is the grpc.ServiceDesc for HealthChecker service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var HealthChecker_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "pb.HealthChecker",
	HandlerType: (*HealthCheckerServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "IsComponentHealthy",
			Handler:    _HealthChecker_IsComponentHealthy_Handler,
		},
		{
			MethodName: "IsNodeHealthy",
			Handler:    _HealthChecker_IsNodeHealthy_Handler,
		},
		{
			MethodName: "CreateDryRunTask",
			Handler:    _HealthChecker_CreateDryRunTask_Handler,
		},
		{
			MethodName: "GetDryRunTaskResult",
			Handler:    _HealthChecker_GetDryRunTaskResult_Handler,
		},
		{
			MethodName: "CreateTask",
			Handler:    _HealthChecker_CreateTask_Handler,
		},
		{
			MethodName: "ReportTaskResult",
			Handler:    _HealthChecker_ReportTaskResult_Handler,
		},
		{
			MethodName: "DescribeTask",
			Handler:    _HealthChecker_DescribeTask_Handler,
		},
		{
			MethodName: "CancelTask",
			Handler:    _HealthChecker_CancelTask_Handler,
		},
		{
			MethodName: "RollbackTask",
			Handler:    _HealthChecker_RollbackTask_Handler,
		},
		{
			MethodName: "TPReportTaskResult",
			Handler:    _HealthChecker_TPReportTaskResult_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "pb/starship.proto",
}
