// Copyright 2015 gRPC authors.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

syntax = "proto3";

option go_package = "git.woa.com/kmetis/starship/pb";
option java_multiple_files = true;
option java_package = "io.grpc.starship.pb";
option java_outer_classname = "PbProto";

package pb;

///////////////////////////////////////////////////////////////
///////////////////// 第三方 grpc 服务定义 //////////////////////

// TP是thirdParty的缩写

// 访问凭证
message TPToken {
  string username = 1;    // 用户名
  string token = 2;       // token
  string host = 3;        // 访问地址
  string type = 4;        // token类型
}

// 创建任务请求
message TPCreateTaskRequest {
  string region = 1;          // 地域信息
  string cluster_id = 2;      // 集群id
  string workload = 3;        // 工作负载
  string image_tag = 4;       // 镜像tag
  TPToken token = 5;          // 访问凭证: ianvs 获取其他
  string action = 6;          // 任务类型：precheck/upgrade/postcheck
  string extender = 7;        // 额外数据
  string client_token = 8;    // 幂等token: 同一个任务请求保持这里的字符串一致
  int64  report_id = 9;       // 用于主动上报task结果时的参数id
}

// 创建任务返回体
message TPCreateTaskReply {
  string  task_id = 1;     // 任务id
  int32  code = 2;        // 返回码
  string reason = 3;      // 如果返回码不为0表示失败，这里填充失败原因
}

// 获取任务执行情况请求
message TPDescribeTaskRequest{
  string region = 1;      // 地域信息
  string task_id = 2;
}

// 风险信息
message TPRisk {
  string app_name = 1;           // 组件名称
  string name = 2;               // 隐患名称
  string resrouce = 3;           // 【新增】资源信息  namespace + name + action
  string code = 4;               // 隐患错误码：PASS、FAILED、ERROR，对应检查通过、失败、错误
  string detail = 5;             // 隐患详情
  string level = 6;              // 隐患级别
  string solution = 7;           // 解决方案
}

// 获取任务执行情况返回体
message TPDescribeTaskReply {
  string  task_id = 1;      // 任务id
  string status = 2;
  int32  code = 3;          // 返回码
  string reason = 4;        // 如果返回码不为0表示失败，这里填充失败原因
  repeated TPRisk risks = 5;  // 风险信息：对于预检/后检需要暴露
}


message TPCancelTaskRequest {
  string region =1;
  string task_id = 2;
}

message TPCancelTaskReply {
  string task_id = 1;       // 任务id
  int32  code = 2;          // 返回码
  string reason = 3;        // 如果返回码不为0表示失败，这里填充失败原因
}

// 上报任务执行结果请求
message TPReportTaskResultRequest {
  int64    report_id = 1;     // 上报id
  string   task_id = 2;       // tp server的任务id
  string   status = 3;        // 任务状态
  int32    code = 4;          // 返回码
  string   reason = 5;        // 失败原因
  repeated TPRisk risks = 6;  // 风险信息
}

// 上报任务执行结果返回体
message TPReportTaskResultReply {
  int32  code = 1;       // 返回码
  string reason = 2;     // 失败原因
}

// The TPTaskEngine service definition.
service TPTaskEngine {
  // CreateTask Creates task, such as precheck, aftercheck, etc. for specified component
  rpc CreateTask(TPCreateTaskRequest) returns (TPCreateTaskReply) {}

  // DescribeTask get the result of task
  rpc DescribeTask(TPDescribeTaskRequest) returns (TPDescribeTaskReply) {}

  // CancelTask cancel task
  rpc CancelTask(TPCancelTaskRequest) returns (TPCancelTaskReply) {}

  // TPReportTaskResult report the result of task
  rpc TPReportTaskResult(TPReportTaskResultRequest) returns (TPReportTaskResultReply) {}

}

///////////////////////////////////////////////////////////////
