package platform

import (
	"fmt"
	"os"

	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/tools/clientcmd/api"
	"k8s.io/klog/v2"
	"tkestack.io/tke/api/client/clientset/versioned"

	configpkg "git.woa.com/kmetis/starship/pkg/config"
)

var (
	clientSet *ClientSet
)

const (
	contextName = "tke-starship"
)

// 用于对接platform-api查询集群信息
type ClientSet struct {
	Client    *versioned.Clientset
	EksClient *versioned.Clientset
}

func GetClientSet() *ClientSet {
	return clientSet
}

func InitClientSet() error {
	clientConfig := configpkg.GetClientConfig()
	client, err := NewTkeClient(clientConfig)
	if err != nil {
		return err
	}
	eksClient, err := NewEksClient(clientConfig)
	if err != nil {
		return err
	}
	clientSet = &ClientSet{
		Client:    client,
		EksClient: eksClient,
	}
	return nil
}

func NewTkeClient(cfg configpkg.ClientConfig) (*versioned.Clientset, error) {
	config := api.NewConfig()
	config.CurrentContext = contextName
	config.Clusters[contextName] = &api.Cluster{
		Server:                cfg.TkeBackendServer,
		InsecureSkipTLSVerify: true,
	}
	config.AuthInfos[contextName] = &api.AuthInfo{
		ClientCertificate: cfg.TkePlatformClientCAFile,
		ClientKey:         cfg.TkePlatformClientKeyFile,
	}
	config.Contexts[contextName] = &api.Context{
		Cluster:  contextName,
		AuthInfo: contextName,
	}

	clientConfig := clientcmd.NewNonInteractiveClientConfig(*config, contextName, &clientcmd.ConfigOverrides{Timeout: "5s"}, nil)
	restConfig, err := clientConfig.ClientConfig()
	if err != nil {
		return nil, fmt.Errorf("build versioned TKE client config error:%v", err)
	}
	restConfig.QPS = 50
	restConfig.Burst = 100
	klog.Infof("NewTKE platform restConfig qps = %d, burst = %d ", restConfig.QPS, restConfig.Burst)
	client, err := versioned.NewForConfig(restConfig)
	if err != nil {
		return nil, fmt.Errorf("build versioned TKE client error:%v", err)
	}
	return client, nil
}

func NewEksClient(cfg configpkg.ClientConfig) (*versioned.Clientset, error) {
	config := api.NewConfig()
	config.CurrentContext = contextName
	config.Clusters[contextName] = &api.Cluster{
		Server:                cfg.EksBackendServer,
		InsecureSkipTLSVerify: true,
	}
	region := os.Getenv("LONG_REGION")
	if region == "" {
		return nil, fmt.Errorf("LONG_REGION from env is empty")
	}
	if region == "ap-chongqing" {
		if cfg.Token == "" {
			return nil, fmt.Errorf("client token is empty")
		}
		config.AuthInfos[contextName] = &api.AuthInfo{
			Token: cfg.Token,
		}
	} else {
		config.AuthInfos[contextName] = &api.AuthInfo{
			ClientCertificate: cfg.EksClientCAFile,
			ClientKey:         cfg.EksClientKeyFile,
		}
	}

	config.Contexts[contextName] = &api.Context{
		Cluster:  contextName,
		AuthInfo: contextName,
	}

	clientConfig := clientcmd.NewNonInteractiveClientConfig(*config, contextName, &clientcmd.ConfigOverrides{Timeout: "5s"}, nil)
	restConfig, err := clientConfig.ClientConfig()
	if err != nil {
		return nil, fmt.Errorf("build versioned EKS client config error:%v", err)
	}
	restConfig.QPS = 50
	restConfig.Burst = 100
	klog.Infof("NewEKS restConfig qps = %d, burst = %d ", restConfig.QPS, restConfig.Burst)
	client, err := versioned.NewForConfig(restConfig)
	if err != nil {
		return nil, fmt.Errorf("build versioned EKS client error:%v", err)
	}
	return client, nil
}
