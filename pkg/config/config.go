package config

import (
	"fmt"

	"github.com/spf13/viper"
	corev1 "k8s.io/api/core/v1"

	"git.woa.com/kmetis/starship/pkg/util"
)

const (
	DBHost     = "mysql-164603"
	DBPort     = "3306"
	DBUser     = "root"
	DBPasswd   = "852456"
	DBDatabase = "starship"

	RainbowUrl     = "http://api.rainbow.woa.com:8080" // oa要换成woa, 否则测试环境访问七彩石不通
	RainbowAppId   = "4d127961-5bc8-4015-b0b7-55c194936ef2"
	RainbowUserId  = "4a01b76e41b243e2b334f1fb7f5ef985"
	RainbowUserKey = "4e0daa93eed146c149534032fe6486209e84"
	RainbowGroup   = "components"
	RainbowEnv     = "Default"
	RainbowRegion  = "qy"

	ComponentApiserver         = "apiserver"
	ComponentScheduler         = "scheduler"
	ComponentControllerManager = "controller-manager"

	KubeApiMaxRequestsInflight         = "max-requests-inflight"
	KubeApiMaxMutatingRequestsInflight = "max-mutating-requests-inflight"
	KubeApiQps                         = "kube-api-qps"
	KubeApiBurst                       = "kube-api-burst"
	DefaultMaxRetryNum                 = 2
	DefaultClusterCreationSecond       = 600
)

type ComponentResourceConfig struct {
	Min corev1.ResourceRequirements `yaml:"min"`
	Max corev1.ResourceRequirements `yaml:"max"`
}

type MasterConfig struct {
	Apiserver                     ComponentResourceConfig           `yaml:"apiserver"`
	ControllerManager             ComponentResourceConfig           `yaml:"controllerManager"`
	Scheduler                     ComponentResourceConfig           `yaml:"scheduler"`
	EnableScaleDown               bool                              `yaml:"enableScaleDown"`
	EnableScaleUp                 bool                              `yaml:"enableScaleUp"`
	EnableEventEtcd               bool                              `yaml:"enableEventEtcd"`
	TotalMillCpuAmount            int64                             `yaml:"totalMillCpuAmount"`
	TotalMemoryBytesAmount        int64                             `yaml:"totalMemoryBytesAmount"`
	MaxTotalMillCpuAmount         int64                             `yaml:"maxTotalMillCpuAmount"`
	MaxTotalMemoryBytesAmount     int64                             `yaml:"maxTotalMemoryBytesAmount"`
	MaxNode                       int                               `yaml:"maxNode"`
	Affinity                      corev1.Affinity                   `yaml:"affinity,omitempty"`
	Tolerations                   []corev1.Toleration               `yaml:"tolerations,omitempty"`
	TopologySpreadConstraints     []corev1.TopologySpreadConstraint `yaml:"topologySpreadConstraints,omitempty"`
	MaxMutatingRequestsInflight   int                               `yaml:"maxMutatingRequestsInflight,omitempty"`
	MaxRequestsInflight           int                               `yaml:"maxRequestsInflight,omitempty"`
	SchedulerKubeApiQps           int                               `yaml:"schedulerKubeApiQps,omitempty"`
	SchedulerKubeApiBurst         int                               `yaml:"schedulerKubeApiBurst,omitempty"`
	ControllerManagerKubeApiQps   int                               `yaml:"controllerManagerKubeApiQps,omitempty"`
	ControllerManagerKubeApiBurst int                               `yaml:"controllerManagerKubeApiBurst,omitempty"`
	EnableScaleDownReplicas       bool                              `yaml:"enableScaleDownReplicas,omitempty"`
	TerminatedPodGCThreshold      int                               `yaml:"terminatedPodGCThreshold,omitempty"`
	GOGCValue                     int                               `yaml:"gogcValue,omitempty"`
}

type Ianvs struct {
	Addr       string
	SecretUser string
	SecretID   string
	SecretKey  string
}

type Rainbow struct {
	Url     string
	AppId   string
	UserId  string
	UserKey string
	Group   string
	Env     string
	Region  string
}

type Database struct {
	DbHost     string
	DbPort     string
	DbUser     string
	DbPasswd   string
	DbDatabase string
}

type ClsEventConfig struct {
	Region    string
	Topic     string
	SecretId  string
	SecretKey string
}

func (c *ClsEventConfig) validate() error {
	if c.Region == "" {
		return fmt.Errorf("cls event config region is empty")
	}
	if c.Topic == "" {
		return fmt.Errorf("cls event config topicId is empty")
	}
	if c.SecretId == "" {
		return fmt.Errorf("cls event config secretId is empty")
	}
	if c.SecretKey == "" {
		return fmt.Errorf("cls event config secretKey is empty")
	}
	var err error
	if c.SecretKey, err = util.Decrypt(c.SecretKey); err != nil {
		return fmt.Errorf("decrypt cls event config secretKey failed: %v", err)
	}
	return nil
}

type ClientConfig struct {
	//tke-platform CA文件
	TkePlatformClientCAFile string
	// tke-platform key文件
	TkePlatformClientKeyFile string
	// tke-platform 后端服务地址
	TkeBackendServer string
	// eks-platform CA文件
	EksClientCAFile string
	// eks-platform key文件
	EksClientKeyFile string
	// eks-platform 后端服务地址
	EksBackendServer string
	// apiserver 访问token，用于成都地区的apiserver访问
	Token string
	// dashboard server 地址
	DashboardServer string
	// tke-cloud-gw server 地址
	TkeCloudGWServer string
	// tke-application server地址
	TkeApplicationServer string
	// tke-application CA文件
	TkeApplicationClientCAFile string
	// tke-application key文件
	TkeApplicationClientKeyFile string
	// eks-application server地址
	EksApplicationServer string
	// eks-application CA文件
	EksApplicationClientCAFile string
	// eks-application key文件
	EksApplicationClientKeyFile string
}

func (c *ClientConfig) validate() error {
	if c.TkePlatformClientCAFile == "" {
		return fmt.Errorf("tke platform client ca file is empty")
	}
	if c.TkePlatformClientKeyFile == "" {
		return fmt.Errorf("tke platform client key file is empty")
	}
	if c.TkeBackendServer == "" {
		return fmt.Errorf("tke platform backend server is empty")
	}
	if c.EksClientCAFile == "" {
		return fmt.Errorf("eks client ca file is empty")
	}
	if c.EksClientKeyFile == "" {
		return fmt.Errorf("eks client key file is empty")
	}
	if c.EksBackendServer == "" {
		return fmt.Errorf("eks backend server is empty")
	}
	if c.DashboardServer == "" {
		return fmt.Errorf("dashboard server is empty")
	}
	if c.TkeCloudGWServer == "" {
		return fmt.Errorf("tke cloud gw server is empty")
	}
	if c.TkeApplicationServer == "" {
		return fmt.Errorf("tke application server is empty")
	}
	if c.TkeApplicationClientCAFile == "" {
		return fmt.Errorf("tke application client ca file is empty")
	}
	if c.TkeApplicationClientKeyFile == "" {
		return fmt.Errorf("tke application client key file is empty")
	}
	if c.EksApplicationServer == "" {
		return fmt.Errorf("eks application server is empty")
	}
	if c.EksApplicationClientCAFile == "" {
		return fmt.Errorf("eks application client ca file is empty")
	}
	if c.EksApplicationClientKeyFile == "" {
		return fmt.Errorf("eks application client key file is empty")
	}
	return nil
}

type DatasourceMappings map[string]string

type Config struct {
	DB                 Database
	Rainbow            Rainbow
	ClsEventConfig     ClsEventConfig
	Client             ClientConfig
	DatasourceMappings DatasourceMappings
}

var c *Config

func InitConfig(config string) error {
	viper.SetConfigFile(config)
	err := viper.ReadInConfig()
	if err != nil {
		return err
	}

	var conf Config
	err = viper.Unmarshal(&conf)
	if err != nil {
		return err
	}
	c = &conf

	if err = c.ClsEventConfig.validate(); err != nil {
		return err
	}
	if err = c.Client.validate(); err != nil {
		return err
	}
	return nil
}

func GetDatabaseConfig() Database {
	return c.DB
}

func GetRainbowConfig() Rainbow {
	return c.Rainbow
}

func GetClsEventConfig() ClsEventConfig {
	return c.ClsEventConfig
}

func GetClientConfig() ClientConfig {
	return c.Client
}
