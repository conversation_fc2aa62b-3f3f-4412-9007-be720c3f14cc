package precheck

import (
	"context"
	"fmt"
	"sync"

	"golang.org/x/sync/errgroup"
	"k8s.io/client-go/kubernetes"
	"k8s.io/klog/v2"

	"git.woa.com/kmetis/starship/infra"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/healthcheckrule"
	"git.woa.com/kmetis/starship/pkg/healthprovider"
	"git.woa.com/kmetis/starship/pkg/model"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	pkgutil "git.woa.com/kmetis/starship/pkg/util"
)

const (
	ProviderName = pkgutil.GenericStrategyPrefix + pkgutil.TaskActionPreCheck
)

func init() {
	strategyprovider.RegisterReleaseStrategyFactory(
		ProviderName,
		func(ctx *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
			return NewGenericPreCheckStrategy(ctx)
		},
	)
}

type GenericPreCheckStrategy struct {
	metaKubeclientset kubernetes.Interface
}

// NewGenericPreCheckStrategy returns new instance of a GenericPreCheckStrategy
func NewGenericPreCheckStrategy(ctx *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
	// TODO: 获取当前地域的metacluster id集群列表，并进行初始化
	return &GenericPreCheckStrategy{}, nil
}

func (d *GenericPreCheckStrategy) Validate(ctx context.Context, request *strategyprovider.TaskStrategyRequest) error {
	return pkgutil.ValidateTaskStrategyRequest(request)
}

func (d *GenericPreCheckStrategy) Exec(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	baseLog := fmt.Sprintf("traceId:%s", request.TraceId)
	if err := d.Validate(ctx, request); err != nil {
		klog.Errorf("validate failed, err is %s, %s", err, baseLog)
		return nil, fmt.Errorf("validate failed:%v", err)
	}

	if request.Action != pkgutil.TaskActionPreCheck {
		klog.Errorf("action %s ignore, %s", request.Action, baseLog)
		return nil, nil
	}
	rule, err := healthcheckrule.GetMergedComponentHealthCheckRules(request.Component)
	if err != nil {
		klog.Errorf("get component health check rules failed, err is %v, %s", err, baseLog)
		return nil, fmt.Errorf("get component health check rules failed")
	}

	// 如果是回滚任务，移除不支持的预检/后检
	if request.IsRollback {
		if rule, err = healthcheckrule.ExcludeSomeRuleForRollback(rule); err != nil {
			klog.Errorf("exclude some rule for rollback failed, err is %v, %s", err, baseLog)
			return nil, fmt.Errorf("exclude some rule for rollback failed")
		}
	}

	// 追加request.HealthCheckRuleMap
	if request.HealthCheckRules == nil {
		request.HealthCheckRules = make([]model.HealthCheckRule, 0)
	}
	request.HealthCheckRules = append(request.HealthCheckRules, rule.PreCheckRules...)
	klog.Infof("pre-check rules:%v, %s", request.HealthCheckRules, baseLog)

	// risk上报
	var replyRisks []*strategyprovider.TaskStrategyReply
	// defer上报风险，更新子任务状态
	defer func() {
		// 将HealthCheckResult转成DB risk
		risks := make([]*infra.StarshipRisks, 0)
		// 批量上报risk
		for _, v := range replyRisks {
			// build TaskStrategyReply
			if v == nil {
				klog.Errorf("health check result is nil, %s", baseLog)
				continue
			}
			if v.Code == "" || v.Level == "" {
				klog.Errorf("health check result is invalid, %s", baseLog)
				continue
			}
			risks = append(risks, &infra.StarshipRisks{
				SubTaskId:       request.SubTaskId,
				AppName:         request.Component,
				HealthCheckName: v.HealthCheckName,
				Name:            v.RiskName,
				Code:            v.Code,
				Detail:          v.Detail,
				Solution:        v.Solution,
				Level:           v.Level,
			})
		}
		// 上报risks
		err = infra.CreateRisk(risks)
		if err != nil {
			klog.Errorf("create risk failed: %v", err)
			return
		}
	}()

	// 并发调用任务
	mu := &sync.Mutex{}
	g, ctx := errgroup.WithContext(ctx)
	for _, rule := range rule.PreCheckRules {
		rule := rule
		g.Go(func() error {
			klog.Infof("start health check %s, %s", rule.Name, baseLog)
			checker, err := healthprovider.GetComponentHealthProvider(rule.Name, nil)
			if err != nil {
				klog.Errorf("failed to get health provider %s, err is %v, %s", rule.Name, err, baseLog)
				return err
			}
			rsps, err := checker.IsReady(ctx, request)
			if err != nil {
				mu.Lock()
				replyRisks = append(replyRisks, &strategyprovider.TaskStrategyReply{
					Code:   healthcheck.HEALTHCHECK_CODE_ERROR,
					Detail: err.Error(),
				})
				mu.Unlock()
				klog.Errorf("failed to do health check %s, err is %v, %s", rule.Name, err, baseLog)
				return nil
			}
			mu.Lock()
			replyRisks = append(replyRisks, rsps...)
			mu.Unlock()
			klog.Infof("finish health check %s, %s", rule.Name, baseLog)
			return nil
		})
	}
	if err := g.Wait(); err != nil {
		klog.Errorf("pre-check err:%v", err)
		return nil, err
	}

	if pkgutil.IsAsyncStrategy(request.PreCheckStrategy) {
		klog.Infof("start to do plugin health precheck, %s", baseLog)
		releaseConfig := pkgutil.BuildReleaseConfig(request)
		strategy, err := strategyprovider.GetReleaseStrategyProvider(request.PreCheckStrategy, releaseConfig)
		if err != nil {
			klog.Errorf("failed get %s release strategy provider failed: %v", request.Component, request.PreCheckStrategy)
			return nil, fmt.Errorf("failed get %s release strategy provider failed: %v", request.Component, request.PreCheckStrategy)
		}
		rsp, err := strategy.Exec(context.TODO(), request)
		if err != nil {
			replyRisks = append(replyRisks, &strategyprovider.TaskStrategyReply{
				Code:   healthcheck.HEALTHCHECK_CODE_ERROR,
				Detail: err.Error(),
			})
			klog.Errorf("failed to do %s plugin precheck: %v", request.Component, err)
			return nil, err
		}
		replyRisks = append(replyRisks, rsp)
		klog.Infof("finish to do plugin health check, %s", baseLog)
	}
	return nil, nil
}

func (d *GenericPreCheckStrategy) Rollback(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	return d.Exec(ctx, request)
}
