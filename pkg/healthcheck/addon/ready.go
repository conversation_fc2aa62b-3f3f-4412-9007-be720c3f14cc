package addon

import (
	"context"
	"fmt"
	"sync"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog/v2"
	applicationv1 "tkestack.io/tke/api/application/v1"
	"tkestack.io/tke/api/client/clientset/versioned"

	healthpb "git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/clientset/application"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/healthprovider"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
)

const (
	ProviderName = "addon"
	RiskName     = "addon健康检查"

	// 默认配置值
	defaultMaxRetries    = 20
	defaultRetryInterval = 30 * time.Second
)

// AddonHealthCheckConfig addon健康检查配置
type AddonHealthCheckConfig struct {
	// MaxRetries 最大重试次数
	MaxRetries int
	// RetryInterval 重试间隔时间
	RetryInterval time.Duration
}

var (
	once    sync.Once
	checker *AddonChecker
)

func init() {
	healthprovider.RegisterComponentHealthFactory(
		ProviderName,
		func(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
			return initAddonChecker(ctx)
		},
	)
}

func initAddonChecker(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
	var err error
	once.Do(func() {
		checker = &AddonChecker{name: ProviderName, risk: RiskName, level: healthcheck.RISK_LEVEL_FATAL}
		err = checker.init()
	})
	return checker, err
}

type AddonChecker struct {
	name  string
	risk  string
	level string
}

func (c *AddonChecker) init() error {
	klog.Infof("init AddonChecker success")
	return nil
}

func (c *AddonChecker) IsHealthy(ctx context.Context, request *healthpb.ComponentHealthyRequest) (*healthpb.ComponentHealthyReply, error) {
	klog.Infof("request info is %v", request)
	return &healthpb.ComponentHealthyReply{}, nil
}

func (c *AddonChecker) IsReady(ctx context.Context, request *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error) {
	baseLog := fmt.Sprintf("traceId:%s", request.TraceId)
	klog.V(5).Infof("run addon check, %s", baseLog)

	rsp := &strategyprovider.TaskStrategyReply{
		HealthCheckName: c.name,
		RiskName:        c.risk,
		Level:           c.level,
	}

	if request.PluginVersion == "" {
		rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
		return util.WrapSingleResult(rsp, nil)
	}

	var appclient *versioned.Clientset
	if request.ProductName == "tke" {
		appclient = application.GetClientSet().Client
	} else if request.ProductName == "eks" {
		appclient = application.GetClientSet().EksClient
	} else {
		return util.WrapSingleResult(util.HandleError(rsp, "productName is invalid", nil, baseLog))
	}

	// 从rule.Value中解析配置参数
	config := c.parseHealthCheckConfig(request, baseLog)

	// 使用配置驱动的重试逻辑
	return c.checkAddonWithRetry(ctx, appclient, request, rsp, config, baseLog)
}

// parseHealthCheckConfig 从rule.Value中解析健康检查配置
func (c *AddonChecker) parseHealthCheckConfig(request *strategyprovider.TaskStrategyRequest, baseLog string) *AddonHealthCheckConfig {
	// 默认配置
	config := &AddonHealthCheckConfig{
		MaxRetries:    defaultMaxRetries,
		RetryInterval: defaultRetryInterval,
	}

	// 获取健康检查规则
	rule := request.GetHealthCheckRule(ProviderName)
	if rule == nil {
		klog.V(5).Infof("addon health check rule not found, using default config, %s", baseLog)
		return config
	}

	// 解析检测次数
	if maxRetries, err := rule.GetValue("maxRetries"); err == nil {
		if retries, ok := maxRetries.(float64); ok && retries > 0 {
			config.MaxRetries = int(retries)
		} else if retries, ok := maxRetries.(int); ok && retries > 0 {
			config.MaxRetries = retries
		}
	}

	// 解析检测间隔时长（秒）
	if retryIntervalSeconds, err := rule.GetValue("retryIntervalSeconds"); err == nil {
		if interval, ok := retryIntervalSeconds.(float64); ok && interval > 0 {
			config.RetryInterval = time.Duration(interval) * time.Second
		} else if interval, ok := retryIntervalSeconds.(int); ok && interval > 0 {
			config.RetryInterval = time.Duration(interval) * time.Second
		}
	}

	klog.V(5).Infof("addon health check config: maxRetries=%d, retryInterval=%v, %s",
		config.MaxRetries, config.RetryInterval, baseLog)

	return config
}

// checkAddonWithRetry 使用配置驱动的重试逻辑检查addon状态
func (c *AddonChecker) checkAddonWithRetry(ctx context.Context, appclient *versioned.Clientset, request *strategyprovider.TaskStrategyRequest, rsp *strategyprovider.TaskStrategyReply, config *AddonHealthCheckConfig, baseLog string) ([]*strategyprovider.TaskStrategyReply, error) {
	for attempt := 1; attempt <= config.MaxRetries; attempt++ {
		klog.V(5).Infof("addon check attempt %d/%d, %s", attempt, config.MaxRetries, baseLog)

		curApp, err := appclient.ApplicationV1().Apps(request.ClusterId).Get(ctx, request.Component, metav1.GetOptions{})
		if err != nil {
			// 其他错误，如果不是最后一次尝试，则继续重试
			if attempt < config.MaxRetries {
				klog.Warningf("addon check failed on attempt %d/%d, will retry after %v: %v, %s",
					attempt, config.MaxRetries, config.RetryInterval, err, baseLog)
				time.Sleep(config.RetryInterval)
				continue
			}

			// 最后一次尝试失败
			return util.WrapSingleResult(util.HandleError(rsp, "get app failed", err, baseLog))
		}

		// 检查app状态
		if curApp.Status.Phase != applicationv1.AppPhaseSucceeded {
			statusErr := fmt.Errorf("status %s reason %s", curApp.Status.Phase, curApp.Status.Reason)

			// 如果不是最后一次尝试，则继续重试
			if attempt < config.MaxRetries {
				klog.Warningf("addon status check failed on attempt %d/%d, will retry after %v: %v, %s",
					attempt, config.MaxRetries, config.RetryInterval, statusErr, baseLog)
				time.Sleep(config.RetryInterval)
				continue
			}

			// 最后一次尝试失败
			return util.WrapSingleResult(util.HandleError(rsp, "app status check failed", statusErr, baseLog))
		}

		// 检查成功
		klog.V(5).Infof("addon check pass on attempt %d, %s", attempt, baseLog)
		rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
		return util.WrapSingleResult(rsp, nil)
	}

	// 理论上不会到达这里
	return util.WrapSingleResult(util.HandleError(rsp, "unexpected end of retry loop", nil, baseLog))
}
