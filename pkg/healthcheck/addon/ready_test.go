package addon

import (
	"context"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/model"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
)

func TestParseHealthCheckConfig_DefaultValues(t *testing.T) {
	checker := &AddonChecker{
		name:  ProviderName,
		risk:  RiskName,
		level: healthcheck.RISK_LEVEL_FATAL,
	}

	// 测试没有规则时的默认配置
	request := &strategyprovider.TaskStrategyRequest{
		TraceId: "test-trace",
	}

	config := checker.parseHealthCheckConfig(request, "test-log")

	assert.Equal(t, defaultMaxRetries, config.MaxRetries)
	assert.Equal(t, defaultRetryInterval, config.RetryInterval)
}

func TestParseHealthCheckConfig_WithRuleValues(t *testing.T) {
	checker := &AddonChecker{
		name:  ProviderName,
		risk:  RiskName,
		level: healthcheck.RISK_LEVEL_FATAL,
	}

	testCases := []struct {
		name                    string
		ruleValue               map[string]interface{}
		expectedMaxRetries      int
		expectedRetryInterval   time.Duration
	}{
		{
			name: "valid config with int values",
			ruleValue: map[string]interface{}{
				"maxRetries":           5,
				"retryIntervalSeconds": 15,
			},
			expectedMaxRetries:    5,
			expectedRetryInterval: 15 * time.Second,
		},
		{
			name: "valid config with float64 values",
			ruleValue: map[string]interface{}{
				"maxRetries":           float64(7),
				"retryIntervalSeconds": float64(20),
			},
			expectedMaxRetries:    7,
			expectedRetryInterval: 20 * time.Second,
		},
		{
			name: "partial config",
			ruleValue: map[string]interface{}{
				"maxRetries": 4,
				// retryIntervalSeconds 缺失，应使用默认值
			},
			expectedMaxRetries:    4,
			expectedRetryInterval: defaultRetryInterval,
		},
		{
			name: "invalid values should use defaults",
			ruleValue: map[string]interface{}{
				"maxRetries":           -1,
				"retryIntervalSeconds": 0,
			},
			expectedMaxRetries:    defaultMaxRetries, // 负值被忽略，使用默认值
			expectedRetryInterval: defaultRetryInterval, // 0值被忽略，使用默认值
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建带有健康检查规则的请求
			request := &strategyprovider.TaskStrategyRequest{
				TraceId: "test-trace",
				HealthCheckRules: &model.HealthCheckRules{
					PreCheckRules: []model.HealthCheckRule{
						{
							Name:  ProviderName,
							Value: tc.ruleValue,
						},
					},
				},
			}

			config := checker.parseHealthCheckConfig(request, "test-log")

			assert.Equal(t, tc.expectedMaxRetries, config.MaxRetries)
			assert.Equal(t, tc.expectedRetryInterval, config.RetryInterval)
		})
	}
}

func TestNotFoundErrorHandling(t *testing.T) {
	// 测试NotFound错误的处理逻辑（现在固定返回WARNING）
	checker := &AddonChecker{
		name:  ProviderName,
		risk:  RiskName,
		level: healthcheck.RISK_LEVEL_FATAL,
	}

	// 模拟NotFound错误的处理
	rsp := &strategyprovider.TaskStrategyReply{
		HealthCheckName: checker.name,
		RiskName:        checker.risk,
		Level:           checker.level,
	}

	config := &AddonHealthCheckConfig{
		MaxRetries:    3,
		RetryInterval: 10 * time.Second,
	}

	// 模拟NotFound错误处理逻辑
	rsp.Level = healthcheck.RISK_LEVEL_WARNING
	rsp.Code = healthcheck.HEALTHCHECK_CODE_FAILED
	rsp.Detail = "addon not found (attempt 1/3)"
	rsp.Solution = "请检查addon是否已正确安装，或确认是否为非addon组件变更"

	// 验证结果
	assert.Equal(t, healthcheck.RISK_LEVEL_WARNING, rsp.Level)
	assert.Equal(t, healthcheck.HEALTHCHECK_CODE_FAILED, rsp.Code)
	assert.NotEmpty(t, rsp.Detail)
	assert.NotEmpty(t, rsp.Solution)
}

func TestIsReady_EmptyPluginVersion(t *testing.T) {
	checker := &AddonChecker{
		name:  ProviderName,
		risk:  RiskName,
		level: healthcheck.RISK_LEVEL_FATAL,
		config: &AddonHealthCheckConfig{
			MaxRetries:           3,
			RetryIntervalSeconds: 10,
			TimeoutSeconds:       300,
			NotFoundBehavior:     "warning",
		},
	}

	request := &strategyprovider.TaskStrategyRequest{
		TraceId:       "test-trace",
		PluginVersion: "", // 空版本
		ProductName:   "tke",
		ClusterId:     "test-cluster",
		Component:     "test-component",
	}

	results, err := checker.IsReady(context.Background(), request)

	assert.NoError(t, err)
	assert.Len(t, results, 1)
	assert.Equal(t, healthcheck.HEALTHCHECK_CODE_PASS, results[0].Code)
}

func TestIsReady_InvalidProductName(t *testing.T) {
	checker := &AddonChecker{
		name:  ProviderName,
		risk:  RiskName,
		level: healthcheck.RISK_LEVEL_FATAL,
		config: &AddonHealthCheckConfig{
			MaxRetries:           3,
			RetryIntervalSeconds: 10,
			TimeoutSeconds:       300,
			NotFoundBehavior:     "warning",
		},
	}

	request := &strategyprovider.TaskStrategyRequest{
		TraceId:       "test-trace",
		PluginVersion: "v1.0.0",
		ProductName:   "invalid", // 无效产品名
		ClusterId:     "test-cluster",
		Component:     "test-component",
	}

	results, err := checker.IsReady(context.Background(), request)

	assert.NoError(t, err)
	assert.Len(t, results, 1)
	assert.Equal(t, healthcheck.HEALTHCHECK_CODE_ERROR, results[0].Code)
	assert.Contains(t, results[0].Detail, "productName is invalid")
}
