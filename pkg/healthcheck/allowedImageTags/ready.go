/*
升级前预检:镜像是否为问题版本

检查实现逻辑：
1. 检查镜像tag是否为问题版本
*/
package allowedimagetags

import (
	"context"
	"fmt"
	"sync"

	"k8s.io/klog/v2"

	healthpb "git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/healthprovider"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/kmetis/starship/pkg/util/kubeclient"
)

const (
	ProviderName = "allowedImageTags"
	RiskName     = "镜像tag版本检查"
)

var (
	once    sync.Once
	checker *TKEImageTagChecker
)

func init() {
	healthprovider.RegisterComponentHealthFactory(
		ProviderName,
		func(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
			return initTKEImageTagChecker(ctx)
		},
	)
}

func initTKEImageTagChecker(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
	var err error
	once.Do(func() {
		checker = &TKEImageTagChecker{name: ProviderName, risk: RiskName, level: healthcheck.RISK_LEVEL_FATAL}
		err = checker.init()
	})
	return checker, err
}

type TKEImageTagChecker struct {
	name  string
	risk  string
	level string
}

func (t *TKEImageTagChecker) init() error {
	klog.Infof("init TKEImageTagChecker success")
	return nil
}

func (t *TKEImageTagChecker) IsHealthy(ctx context.Context, request *healthpb.ComponentHealthyRequest) (*healthpb.ComponentHealthyReply, error) {
	klog.Infof("request info is %v", request)
	return &healthpb.ComponentHealthyReply{}, nil
}

func (t *TKEImageTagChecker) IsReady(ctx context.Context, req *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error) {
	baseLog := fmt.Sprintf("traceId:%s, clusterId:%s, taskId:%d", req.TraceId, req.ClusterId, req.TaskId)
	klog.V(5).Infof("run image tag check, %s", baseLog)

	rsp := &strategyprovider.TaskStrategyReply{
		HealthCheckName: t.name,
		RiskName:        t.risk,
		Level:           t.level,
	}
	client, err := kubeclient.GetTargetK8sClient(req)
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get k8s client", err, baseLog))
	}
	clusterId := req.ClusterId
	namespace := util.GetComponentNamespace(clusterId, req.Namespace)
	workloadName := util.GetWorkloadName(clusterId, req.WorkloadName)
	// 获取workload的镜像信息
	imageList, err := util.GetWorkloadImage(client, req.WorkloadType, namespace, workloadName, req.Extend)
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get workload image", err, baseLog))
	}

	// 从七彩石策略读取问题tag版本，并进行匹配
	// NOTICE: 如果指定了要进行镜像tag版本检查，七彩石必须配置策略，否则会报错
	if req.HealthCheckRules == nil {
		return util.WrapSingleResult(util.HandleError(rsp, "health check rule not exist", nil, baseLog))
	}

	allowedImageTagsRule := req.GetHealthCheckRule(ProviderName)

	// tag信息如下：
	// allowedImageTags:
	//	- v1.28.1
	//	- v1.29.9
	if allowedImageTagsRule == nil {
		return util.WrapSingleResult(util.HandleError(rsp, "image tag check rule is empty", nil, baseLog))
	}

	allowedImageTags, err := allowedImageTagsRule.GetStringArray("allowedImageTags")
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get allowedImageTags", err, baseLog))
	}

	for _, imageStr := range imageList {
		klog.Infof("workload image:%s, problematic image tags:%v, %s", imageStr, allowedImageTags, baseLog)
		// 检查进行镜像tag版本检查
		currentImageTag, err := util.GetImageTag(imageStr)
		if err != nil {
			return util.WrapSingleResult(util.HandleError(rsp, "failed to get image tag", err, baseLog))
		}
		match := false
		for _, tag := range allowedImageTags {
			if currentImageTag == tag {
				match = true
				break
			}
		}

		if !match {
			return util.WrapSingleResult(util.HandleFailed(rsp,
				fmt.Sprintf("image tag is not specified version, current image:%s", imageStr),
				"please check image tag", baseLog))
		}
	}

	klog.V(5).Infof("image tag check pass, %s", baseLog)
	rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
	return util.WrapSingleResult(rsp, nil)
}
