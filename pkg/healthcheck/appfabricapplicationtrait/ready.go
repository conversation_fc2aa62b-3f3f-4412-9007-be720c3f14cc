package appfabricapplicationtrait

import (
	"context"
	"encoding/json"
	"fmt"
	"sync"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog/v2"

	"git.woa.com/kmetis/starship/infra"
	healthpb "git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/healthprovider"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
)

const (
	ProviderName = "appFabricApplicationTrait"
	RiskName     = "应用Trait数量检查"
)

type TraitCountDetail struct {
	TraitCount int `json:"trait_count"`
}

var (
	once    sync.Once
	checker *TraitCountChecker
)

func init() {
	healthprovider.RegisterComponentHealthFactory(
		ProviderName,
		func(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
			return initTraitCountChecker(ctx)
		},
	)
}

func initTraitCountChecker(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
	var err error
	once.Do(func() {
		checker = &TraitCountChecker{
			name:  ProviderName,
			risk:  RiskName,
			level: healthcheck.RISK_LEVEL_FATAL,
		}
		err = checker.init()
	})
	return checker, err
}

type TraitCountChecker struct {
	name  string
	risk  string
	level string
}

func (t *TraitCountChecker) init() error {
	klog.Infof("init TraitCountChecker success")
	return nil
}

func (t *TraitCountChecker) IsHealthy(ctx context.Context, request *healthpb.ComponentHealthyRequest) (*healthpb.ComponentHealthyReply, error) {
	klog.Infof("request info is %v", request)
	return &healthpb.ComponentHealthyReply{}, nil
}

func (t *TraitCountChecker) IsReady(ctx context.Context, req *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error) {
	baseLog := fmt.Sprintf("traceId:%s, clusterId:%s, taskId:%d", req.TraceId, req.ClusterId, req.TaskId)
	klog.V(5).Infof("run trait count check, %s", baseLog)

	rsp := &strategyprovider.TaskStrategyReply{
		HealthCheckName: t.name,
		RiskName:        t.risk,
		Level:           t.level,
	}

	// 获取TAD客户端
	client, err := util.GetTargetTadClient(req)
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get tad client", err, baseLog))
	}

	// 获取Application对象
	app, err := client.CoreV1().Applications(req.Extend.Namespace).Get(ctx, req.Extend.Name, metav1.GetOptions{})
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get Application", err, baseLog))
	}

	traitsNum := len(app.Spec.Components[0].Traits)

	// 如果是预检，则记录trait数量
	if req.Action == util.TaskActionPreCheck {
		traitDetail := TraitCountDetail{
			TraitCount: traitsNum,
		}
		detailBytes, err := json.Marshal(traitDetail)
		if err != nil {
			return util.WrapSingleResult(util.HandleError(rsp, "failed to marshal trait count detail", err, baseLog))
		}

		klog.Infof("saved trait count %d for task %d, %s", traitsNum, req.TaskId, baseLog)
		rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
		rsp.Detail = string(detailBytes)
		return util.WrapSingleResult(rsp, nil)
	}

	// 后检，先获取当前任务的预检记录
	preCheck, err := infra.GetSubTaskByParent(req.TaskId, util.TaskActionPreCheck)
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get pre-check trait count", err, baseLog))
	}

	// 找不到预检记录，直接返回
	if len(preCheck) == 0 {
		return util.WrapSingleResult(util.HandleError(rsp, "no pre-check record found", nil, baseLog))
	}

	// 通过预检找到appFabricApplicationTrait risk记录
	risk, err := infra.GetAllRisksBySubTask(preCheck[0].ID, ProviderName)
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get risks by subtask", err, baseLog))
	}

	if len(risk) == 0 {
		return util.WrapSingleResult(util.HandleError(rsp, "no risk record found for pre-check", nil, baseLog))
	}

	var preCheckTraitDetail TraitCountDetail
	if err := json.Unmarshal([]byte(risk[0].Detail), &preCheckTraitDetail); err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to unmarshal pre-check detail", err, baseLog))
	}

	// 比较trait数量
	if preCheckTraitDetail.TraitCount != traitsNum {
		return util.WrapSingleResult(util.HandleFailed(rsp,
			fmt.Sprintf("trait count mismatch: precheck=%d, postcheck=%d", preCheckTraitDetail.TraitCount, traitsNum),
			"please check trait num", baseLog))
	}

	klog.Infof("trait count check passed: count=%d, %s", traitsNum, baseLog)
	rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
	return util.WrapSingleResult(rsp, nil)
}
