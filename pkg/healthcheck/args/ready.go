/**
组件启动参数检查
*/

package args

import (
	"context"
	"fmt"
	"sync"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog/v2"

	healthpb "git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/healthprovider"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/kmetis/starship/pkg/util/kubeclient"
)

const (
	ProviderName = "containerStartArgs"
	RiskName     = "组件启动参数检查"
)

type TKEContainerStartArgsChecker struct {
	name  string
	risk  string
	level string
}

func (t *TKEContainerStartArgsChecker) IsHealthy(ctx context.Context, request *healthpb.ComponentHealthyRequest) (*healthpb.ComponentHealthyReply, error) {
	klog.Infof("request info is %v", request)
	return &healthpb.ComponentHealthyReply{}, nil
}

func (t *TKEContainerStartArgsChecker) IsReady(ctx context.Context, req *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error) {
	// 获取k8s client
	baseLog := fmt.Sprintf("traceId:%s", req.TraceId)
	klog.V(5).Infof("run deployment start args check, %s", baseLog)
	rsp := &strategyprovider.TaskStrategyReply{
		HealthCheckName: t.name,
		RiskName:        t.risk,
		Level:           t.level,
	}
	client, err := kubeclient.GetTargetK8sClient(req)

	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get k8s client", err, baseLog))
	}
	clusterId := req.ClusterId
	namespace := util.GetComponentNamespace(clusterId, req.Namespace)
	workloadName := util.GetWorkloadName(clusterId, req.WorkloadName)

	// 获取deployment信息
	deployment, err := client.AppsV1().Deployments(namespace).Get(ctx, workloadName, metav1.GetOptions{})
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get deployment", err, baseLog))
	}
	rule := req.GetHealthCheckRule(ProviderName)
	if rule == nil {
		return util.WrapSingleResult(util.HandleError(rsp, fmt.Sprintf("rule %s is empty", ProviderName), nil, baseLog))
	}

	// 遍历当前deployment的container，对照七彩石中的规则检查是否匹配
	for index, container := range deployment.Spec.Template.Spec.Containers {
		if ok := rule.Exist(container.Name); ok {
			// 判断当前container的启动参数是否匹配
			klog.V(5).Infof("container name: %s, args: %v", container.Name, container.Args)
			required, err := rule.GetStringArray(fmt.Sprintf("%s.required", container.Name))
			if err != nil {
				return util.WrapSingleResult(util.HandleError(rsp, "failed to get required args", err, baseLog))
			}

			forbidden, err := rule.GetStringArray(fmt.Sprintf("%s.forbidden", container.Name))
			if err != nil {
				return util.WrapSingleResult(util.HandleError(rsp, "failed to get forbidden args", err, baseLog))
			}

			for _, arg := range deployment.Spec.Template.Spec.Containers[index].Args {
				if ContainsString(forbidden, arg) {
					// 如果包含Forbidden的参数，则不匹配
					errMsg := fmt.Sprintf("The startup parameter of container %s contain the prohibited parameter %s", container.Name, arg)
					return util.WrapSingleResult(util.HandleError(rsp, errMsg, nil, baseLog))
				}
			}

			if !IncludeStrings(required, deployment.Spec.Template.Spec.Containers[index].Args) {
				// 如果不包含required的参数，则不匹配
				errMsg := fmt.Sprintf("The startup parameter of container %s don't contain the required parameter", container.Name)
				return util.WrapSingleResult(util.HandleError(rsp, errMsg, nil, baseLog))
			}
		}
	}
	klog.V(5).Infof("container start parameters check pass, %s", baseLog)
	rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
	return util.WrapSingleResult(rsp, nil)
}

var (
	once    sync.Once
	checker *TKEContainerStartArgsChecker
)

func init() {
	healthprovider.RegisterComponentHealthFactory(
		ProviderName,
		func(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
			return initTKEContainerStartArgsChecker(ctx)
		},
	)
}

func IncludeStrings(needs []string, all []string) bool {
	for _, need := range needs {
		found := false
		for _, item := range all {
			if item == need {
				found = true
				break
			}
		}
		if !found {
			return false
		}
	}
	return true
}

func ContainsString(slice []string, str string) bool {
	for _, s := range slice {
		if s == str {
			return true
		}
	}
	return false
}

func initTKEContainerStartArgsChecker(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
	var err error
	once.Do(func() {
		checker = &TKEContainerStartArgsChecker{name: ProviderName, risk: RiskName, level: healthcheck.RISK_LEVEL_FATAL}
		err = checker.init()
	})
	return checker, err
}

func (t *TKEContainerStartArgsChecker) init() error {
	klog.Infof("init TKEContainerStartArgsChecker success")
	return nil
}
