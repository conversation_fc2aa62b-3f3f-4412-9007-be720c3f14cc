package args_test

import (
	"testing"

	"git.woa.com/kmetis/starship/pkg/healthcheck/args"
)

func TestContainsString(t *testing.T) {
	tests := []struct {
		slice    []string
		str      string
		expected bool
	}{
		{[]string{"apple", "banana", "cherry"}, "banana", true},
		{[]string{"apple", "banana", "cherry"}, "date", false},
		{[]string{}, "anything", false},
		{[]string{"onlyone"}, "onlyone", true},
		{[]string{"one", "two", "three"}, "", false},
	}

	for _, test := range tests {
		result := args.ContainsString(test.slice, test.str)
		if result != test.expected {
			t.Errorf("ContainsString(%v, %q) = %v; want %v", test.slice, test.str, result, test.expected)
		}
	}
}
