/*
组件是否允许升级检查

功能：
1. 检查appid是否在黑名单中
2. 检查集群id是否在黑名单中
*/
package blacklist

import (
	"context"
	"fmt"
	"strings"
	"sync"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog/v2"

	healthpb "git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/clientset/platform"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/healthprovider"
	"git.woa.com/kmetis/starship/pkg/metrics"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	platformapiv1 "tkestack.io/tke/api/platform/v1"
)

const (
	ProviderName = "blackList"
	RiskName     = "升级黑名单检查"
)

var (
	once    sync.Once
	checker *BlackListChecker
)

func init() {
	healthprovider.RegisterComponentHealthFactory(
		ProviderName,
		func(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
			return initBlackListChecker(ctx)
		},
	)
}

func initBlackListChecker(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
	var err error
	once.Do(func() {
		checker = &BlackListChecker{name: ProviderName, risk: RiskName, level: healthcheck.RISK_LEVEL_FATAL}
		err = checker.init()
	})
	return checker, err
}

type BlackListChecker struct {
	name  string
	risk  string
	level string
}

func (t *BlackListChecker) init() error {
	klog.Infof("init UpgradeBlacklistChecker success")
	return nil
}

func (t *BlackListChecker) IsHealthy(ctx context.Context, request *healthpb.ComponentHealthyRequest) (*healthpb.ComponentHealthyReply, error) {
	klog.Infof("request info is %v", request)
	return &healthpb.ComponentHealthyReply{}, nil
}

func (t *BlackListChecker) IsReady(ctx context.Context, request *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error) {
	baseLog := fmt.Sprintf("traceId:%s", request.TraceId)
	klog.V(5).Infof("run upgrade blacklist check, %s", baseLog)

	rsp := &strategyprovider.TaskStrategyReply{
		HealthCheckName: t.name,
		RiskName:        t.risk,
		Level:           t.level,
	}

	rule := request.GetHealthCheckRule(ProviderName)
	if rule == nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get upgrade blacklist check rule", nil, baseLog))
	}

	blockedClusters, err := rule.GetStringArray("clusterIdList")
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get clusterIds from blacklist", err, baseLog))
	} else if len(blockedClusters) > 0 {
		for _, id := range blockedClusters {
			if strings.TrimSpace(id) == request.ClusterId {
				errMsg := fmt.Sprintf("cluster %s is in blacklist", request.ClusterId)
				return util.WrapSingleResult(util.HandleFailed(rsp, errMsg, "please check appId", baseLog))
			}
		}
	}
	blockedAppids, err := rule.GetStringArray("appidList")
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get appids from blacklist", err, baseLog))
	} else if len(blockedAppids) > 0 {
		appid, err := getAppIdFromCluster(request.ClusterId, request.ProductName)
		if err != nil {
			return util.WrapSingleResult(util.HandleError(rsp, "failed to get cluster appid", err, baseLog))
		}
		if appid == "" {
			return util.WrapSingleResult(util.HandleError(rsp, "get cluster appid failed, tenantID is empty", err, baseLog))
		}
		for _, id := range blockedAppids {
			if appid == strings.TrimSpace(id) {
				errMsg := fmt.Sprintf("appid %s is in blacklist", appid)
				return util.WrapSingleResult(util.HandleFailed(rsp, errMsg, "", baseLog))
			}
		}
	}

	klog.V(5).Infof("upgrade blacklist check pass, %s", baseLog)
	rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
	return util.WrapSingleResult(rsp, nil)
}

func getAppIdFromCluster(clusterId, productName string) (string, error) {
	var cluster *platformapiv1.Cluster
	var err error
	if productName == "tke" {
		cluster, err = platform.GetClientSet().Client.PlatformV1().Clusters().Get(context.TODO(), clusterId, metav1.GetOptions{})
	} else if productName == "eks" {
		cluster, err = platform.GetClientSet().EksClient.PlatformV1().Clusters().Get(context.TODO(), clusterId, metav1.GetOptions{})
	} else {
		return "", fmt.Errorf("productName is invalid: %s", productName)
	}
	if err != nil {
		metrics.ClusterInfoFetchFailedTotal.WithLabelValues().Inc()
		return "", fmt.Errorf("failed to get cluster %s for product %s: %v", clusterId, productName, err)
	}
	return cluster.Spec.TenantID, nil
}
