package configmapcheck

/*
升级前预检:configmap是否能匹配指定客更新对象
*/

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"k8s.io/klog/v2"

	healthpb "git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/healthprovider"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
)

const (
	ProviderName = "configmapcheck"
	RiskName     = "configmap检查"
)

var (
	once    sync.Once
	checker *ConfigmapChecker
)

func init() {
	healthprovider.RegisterComponentHealthFactory(
		ProviderName,
		func(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
			return initConfigmapChecker(ctx)
		},
	)
}

func initConfigmapChecker(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
	var err error
	once.Do(func() {
		checker = &ConfigmapChecker{name: ProviderName, risk: RiskName, level: healthcheck.RISK_LEVEL_FATAL}
		err = checker.init()
	})
	return checker, err
}

type ConfigmapChecker struct {
	name  string
	risk  string
	level string
}

func (t *ConfigmapChecker) init() error {
	klog.Infof("init ConfigmapChecker success")
	return nil
}

func (t *ConfigmapChecker) IsHealthy(ctx context.Context, request *healthpb.ComponentHealthyRequest) (*healthpb.ComponentHealthyReply, error) {
	klog.Infof("request info is %v", request)
	return &healthpb.ComponentHealthyReply{}, nil
}

func (t *ConfigmapChecker) IsReady(ctx context.Context, req *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error) {
	baseLog := fmt.Sprintf("traceId:%s, clusterId:%s, taskId:%d", req.TraceId, req.ClusterId, req.TaskId)
	klog.V(5).Infof("run configmap check, %s", baseLog)

	rsp := &strategyprovider.TaskStrategyReply{
		HealthCheckName: t.name,
		RiskName:        t.risk,
		Level:           t.level,
	}
	clusterId := req.ClusterId
	namespace := util.GetComponentNamespace(clusterId, req.Namespace)
	configmapName := util.GetWorkloadName(clusterId, req.WorkloadName)

	// 从七彩石策略读取指定可更新configmap，并进行匹配
	// NOTICE: 如果指定了要进行configmap检查，七彩石必须配置策略，否则会报错
	// configmap check 信息如下
	// configmapcheck:
	// - "kube-system/tke-request-match-config"
	// - "$clusterid/$clusterid-test"

	if req.HealthCheckRules == nil {
		return util.WrapSingleResult(util.HandleError(rsp, "health check rule is nil", nil, baseLog))
	}

	allowedConfigmap := req.GetHealthCheckRule(ProviderName)
	if allowedConfigmap == nil {
		return util.WrapSingleResult(util.HandleError(rsp, "health check rule not exist", nil, baseLog))
	}
	configmaps, err := allowedConfigmap.GetStringArray(ProviderName)
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "get configmap list failed", nil, baseLog))
	}
	find := false
	for _, configmap := range configmaps {
		arrs := strings.Split(configmap, "/")
		if len(arrs) != 2 {
			return util.WrapSingleResult(util.HandleError(rsp, "invalid configmap format", nil, baseLog))
		}
		availableNamespace := util.GetComponentNamespace(clusterId, arrs[0])
		availableConfigmap := util.GetWorkloadName(clusterId, arrs[1])

		if availableNamespace == namespace && availableConfigmap == configmapName {
			find = true
			break
		}
	}
	if !find {
		return util.WrapSingleResult(util.HandleFailed(rsp,
			fmt.Sprintf("configmap not specified, current configmap:%s", configmapName),
			"please check configmap", baseLog))
	}

	klog.V(5).Infof("configmap check pass, %s", baseLog)
	rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
	return util.WrapSingleResult(rsp, nil)
}
