package daemonsetsimulation

import (
	"context"
	"fmt"
	"sync"
	"time"

	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog/v2"

	healthpb "git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/healthprovider"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/kmetis/starship/pkg/util/kubeclient"
)

const (
	ProviderName = "daemonsetSimulation"
	RiskName     = "DaemonSet模拟启动"
)

var (
	once    sync.Once
	checker *DaemonsetSimulationChecker
)

func init() {
	healthprovider.RegisterComponentHealthFactory(
		ProviderName,
		func(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
			return initDaemonsetSimulationChecker(ctx)
		},
	)
}

func initDaemonsetSimulationChecker(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
	var err error
	once.Do(func() {
		checker = &DaemonsetSimulationChecker{name: ProviderName, risk: RiskName, level: healthcheck.RISK_LEVEL_FATAL}
		err = checker.init()
	})
	return checker, err
}

type DaemonsetSimulationChecker struct {
	name  string
	risk  string
	level string
}

func (t *DaemonsetSimulationChecker) init() error {
	klog.Infof("init DaemonsetSimulation success")
	return nil
}

func (t *DaemonsetSimulationChecker) IsHealthy(ctx context.Context, request *healthpb.ComponentHealthyRequest) (*healthpb.ComponentHealthyReply, error) {
	klog.Infof("request info is %v", request)
	return &healthpb.ComponentHealthyReply{}, nil
}

func (t *DaemonsetSimulationChecker) IsReady(ctx context.Context, request *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error) {
	baseLog := fmt.Sprintf("traceId:%s", request.TraceId)
	klog.V(5).Infof("run daemonset simulation check, %s", baseLog)

	rsp := &strategyprovider.TaskStrategyReply{
		HealthCheckName: t.name,
		RiskName:        t.risk,
		Level:           t.level,
	}

	client, err := kubeclient.GetTargetK8sClient(request)
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get k8s client", err, baseLog))
	}
	clusterId := request.ClusterId
	namespace := util.GetComponentNamespace(clusterId, request.Namespace)
	workloadName := util.GetWorkloadName(clusterId, request.WorkloadName)

	daemonSet, err := client.AppsV1().DaemonSets(namespace).Get(ctx, workloadName, metav1.GetOptions{})
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get ds", err, baseLog))
	}

	// 构造simulation daemonset
	sDs, err := getSimulationDaemonset(daemonSet, request.ImageTag)
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get simulation ds", err, baseLog))
	}

	util.DumpResource(sDs)

	// 创建simulation daemonset
	_, err = client.AppsV1().DaemonSets(namespace).Create(ctx, sDs, metav1.CreateOptions{})
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to create simulation ds", err, baseLog))
	}

	// 等待simulation daemonset ready
	errCreate := util.RetryUntilTimeout(ctx, time.Second*30, time.Minute*5, func() error {
		ds, err := client.AppsV1().DaemonSets(namespace).Get(ctx, sDs.Name, metav1.GetOptions{})
		if err != nil {
			return err
		}
		if daemonSet.Status.ObservedGeneration == daemonSet.Generation && ds.Status.NumberReady == ds.Status.DesiredNumberScheduled {
			return nil
		}
		// 重试
		return util.RetryAbleErr
	})

	// 30s后删除simulation daemonset
	time.Sleep(30 * time.Second)
	gracePeriodSeconds := int64(0)
	client.AppsV1().DaemonSets(namespace).Delete(ctx, sDs.Name, metav1.DeleteOptions{
		GracePeriodSeconds: &gracePeriodSeconds,
	})
	errDelete := util.RetryUntilTimeout(ctx, time.Second*30, time.Minute*5, func() error {
		if _, err := client.AppsV1().DaemonSets(namespace).Get(ctx, sDs.Name, metav1.GetOptions{}); err != nil {
			if errors.IsNotFound(err) {
				return nil
			}
			return err
		}
		// 重试
		return util.RetryAbleErr
	})

	if errCreate != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to create simulation ds", errCreate, baseLog))
	}

	if errDelete != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to delete simulation ds", errDelete, baseLog))
	}

	klog.V(5).Infof("daemonset simulation check pass, %s", baseLog)
	rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
	return util.WrapSingleResult(rsp, nil)
}

func getSimulationDaemonset(ds *appsv1.DaemonSet, tag string) (*appsv1.DaemonSet, error) {
	newDs := appsv1.DaemonSet{
		TypeMeta: ds.TypeMeta,
		ObjectMeta: metav1.ObjectMeta{
			Name:      ds.Name + "-precheck",
			Namespace: ds.Namespace,
		},
		Spec: ds.Spec,
	}

	if len(newDs.Spec.Template.Spec.Containers) > 1 {
		return nil, fmt.Errorf("daemonset %s/%s has more than one container", ds.Namespace, ds.Name)
	}

	// 保持镜像，避免大集群对tcr产生压力
	//image, _ := util.GetTargetImage(ds.Spec.Template.Spec.Containers[0].Image, tag, nil)
	//newDs.Spec.Template.Spec.Containers[0].Image = image
	// req/limit去掉，避免对节点资源造成干扰
	newDs.Spec.Template.Spec.Containers[0].Resources.Requests = nil
	newDs.Spec.Template.Spec.Containers[0].Resources.Limits = nil
	// 更新优先级为空，避免调度时驱逐了业务pod
	newDs.Spec.Template.Spec.PriorityClassName = ""
	newDs.Spec.Template.Spec.Priority = nil
	// remove scheduler.alpha.kubernetes.io/critical-pod: ""
	newDs.Spec.Template.Annotations = nil
	// 更新命令行参数，用于空跑
	newDs.Spec.Template.Spec.Containers[0].Command = []string{"tail", "-f", "/dev/null"}
	newDs.Spec.Template.Spec.Containers[0].Args = nil
	// securityContext
	newDs.Spec.Template.Spec.Containers[0].SecurityContext = nil
	newDs.Spec.Template.Spec.SecurityContext = nil
	// 移除volume的挂载，避免挂载干扰，但要保留timeZone的挂载
	newDs.Spec.Template.Spec.Volumes = keepTimeZoneVolume(newDs.Spec.Template.Spec.Volumes)
	newDs.Spec.Template.Spec.Containers[0].VolumeMounts = keepTimeZoneVolumeMount(newDs.Spec.Template.Spec.Containers[0].VolumeMounts)
	// 移除initcontainer，避免有其他注入影响
	newDs.Spec.Template.Spec.InitContainers = nil
	// 更新pod的label, k8s-app: kube-proxy-precheck
	newDs.Spec.Selector.MatchLabels = map[string]string{"k8s-app": ds.Name + "-precheck"}
	newDs.Spec.Template.Labels = map[string]string{"k8s-app": ds.Name + "-precheck"}

	return &newDs, nil
}

func keepTimeZoneVolume(items []corev1.Volume) []corev1.Volume {
	for _, item := range items {
		if item.Name == "tz-config" {
			return []corev1.Volume{item}
		}
	}
	return nil
}

func keepTimeZoneVolumeMount(items []corev1.VolumeMount) []corev1.VolumeMount {
	for _, item := range items {
		if item.Name == "tz-config" {
			return []corev1.VolumeMount{item}
		}
	}
	return nil
}
