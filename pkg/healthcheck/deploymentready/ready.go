/*
专门针对deployment的检查

功能：
1. 检查deployment的副本数是否符合预期
*/
package deploymentready

import (
	"context"
	"fmt"
	"sync"

	"k8s.io/klog/v2"

	healthpb "git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/healthprovider"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/kmetis/starship/pkg/util/kubeclient"
)

const (
	ProviderName = "deploymentReady"
	RiskName     = "deployment就绪检查"
)

var (
	once    sync.Once
	checker *TKEDeploymentChecker
)

func init() {
	healthprovider.RegisterComponentHealthFactory(
		ProviderName,
		func(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
			return initTKEDeploymentChecker(ctx)
		},
	)
}

func initTKEDeploymentChecker(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
	var err error
	once.Do(func() {
		checker = &TKEDeploymentChecker{name: ProviderName, risk: RiskName, level: healthcheck.RISK_LEVEL_FATAL}
		err = checker.init()
	})
	return checker, err
}

type TKEDeploymentChecker struct {
	name  string
	risk  string
	level string
}

func (t *TKEDeploymentChecker) init() error {
	klog.Infof("init TKEDeploymentChecker success")
	return nil
}

func (t *TKEDeploymentChecker) IsHealthy(ctx context.Context, request *healthpb.ComponentHealthyRequest) (*healthpb.ComponentHealthyReply, error) {
	klog.Infof("request info is %v", request)
	return &healthpb.ComponentHealthyReply{}, nil
}

func (t *TKEDeploymentChecker) IsReady(ctx context.Context, request *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error) {
	baseLog := fmt.Sprintf("traceId:%s", request.TraceId)
	klog.Infof("run deployment ready check, %s", baseLog)

	client, err := kubeclient.GetTargetK8sClient(request)
	if err != nil {
		errMsg := fmt.Sprintf("failed to get k8s client, err:%v", err)
		klog.Errorf("%s, %s", errMsg, baseLog)
		return util.WrapSingleResult(&strategyprovider.TaskStrategyReply{
			HealthCheckName: t.name,
			RiskName:        t.risk,
			Level:           t.level,
			Code:            healthcheck.HEALTHCHECK_CODE_ERROR,
			Detail:          errMsg,
		}, nil)
	}
	clusterId := request.ClusterId
	namespace := util.GetComponentNamespace(clusterId, request.Namespace)
	workloadName := util.GetWorkloadName(clusterId, request.WorkloadName)

	return util.WrapSingleResult(util.CheckDeploymentReadyWithRetry(ctx, client, namespace, workloadName, request.TraceId, 10, t.name, t.risk, t.level))
}
