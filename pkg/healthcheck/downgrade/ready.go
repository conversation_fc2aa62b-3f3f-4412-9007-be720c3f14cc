/*
镜像升级检查

功能：
1. 拦截主版本升级
2. 拦截次版本升级

该升级策略默认开启，可以通过在七彩石配置healthchecks.rules禁用
*/
package downgrade

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"k8s.io/apimachinery/pkg/util/json"
	"k8s.io/klog/v2"

	healthpb "git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/healthprovider"
	"git.woa.com/kmetis/starship/pkg/model"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/kmetis/starship/pkg/util/kubeclient"
)

const (
	ProviderName = "crossVersionDowngrade"
	RiskName     = "禁止镜像跨版本降级"

	// 镜像降级特性开关
	SWITCH_ENABLE                  = "enable"
	FORBID_MAJOR_VERSION_DOWNGRADE = "ForbidMajorVersionDowngrade"
	FORBID_MINOR_VERSION_DOWNGRADE = "ForbidMinorVersionDowngrade"
	FORBID_PATCH_VERSION_DOWNGRADE = "ForbidPatchVersionDowngrade"
	FORBID_TKE_VERSION_DOWNGRADE   = "ForbidTKEVersionDowngrade"
)

var (
	once    sync.Once
	checker *TKEDowngradeChecker
)

func init() {
	healthprovider.RegisterComponentHealthFactory(
		ProviderName,
		func(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
			return initTKEDowngradeChecker(ctx)
		},
	)
}

func initTKEDowngradeChecker(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
	var err error
	once.Do(func() {
		checker = &TKEDowngradeChecker{name: ProviderName, risk: RiskName, level: healthcheck.RISK_LEVEL_FATAL}
		err = checker.init()
	})
	return checker, err
}

type TKEDowngradeChecker struct {
	name  string
	risk  string
	level string
}

func (t *TKEDowngradeChecker) init() error {
	klog.Infof("init TKEDowngradeChecker success")
	return nil
}

func (t *TKEDowngradeChecker) IsHealthy(ctx context.Context, request *healthpb.ComponentHealthyRequest) (*healthpb.ComponentHealthyReply, error) {
	klog.Infof("request info is %v", request)
	return &healthpb.ComponentHealthyReply{}, nil
}

func (t *TKEDowngradeChecker) IsReady(ctx context.Context, request *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error) {
	baseLog := fmt.Sprintf("traceId:%s", request.TraceId)
	klog.V(5).Infof("run cross-version downgrade check, %s", baseLog)
	rsp := &strategyprovider.TaskStrategyReply{
		HealthCheckName: t.name,
		RiskName:        t.risk,
		Level:           t.level,
	}
	client, err := kubeclient.GetTargetK8sClient(request)
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get k8s client", err, baseLog))
	}
	clusterId := request.ClusterId
	namespace := util.GetComponentNamespace(clusterId, request.Namespace)
	workloadName := util.GetWorkloadName(clusterId, request.WorkloadName)

	imageList, err := util.GetWorkloadImage(client, request.WorkloadType, namespace, workloadName, request.Extend)
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "get workload image err", err, baseLog))
	}

	// NOTICE: 如果指定了要进行版本降级检查，七彩石必须配置策略，否则会报错
	if request.HealthCheckRules == nil {
		return util.WrapSingleResult(util.HandleError(rsp, "health check rule not exist", nil, baseLog))
	}

	downgradeRule := request.GetHealthCheckRule(ProviderName)

	// downgrade信息如下：
	// ForbidMajorVersionDowngrade: enable
	// ForbidMinorVersionDowngrade: enable
	// ForbidPatchVersionDowngrade: enable
	// ForbidTKEVersionDowngrade: enable
	if downgradeRule == nil {
		return util.WrapSingleResult(util.HandleError(rsp, "cross-version downgrade rule is empty", nil, baseLog))
	}
	downgradeRuleJson, err := json.Marshal(downgradeRule)
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to marshal downgrade rule", err, baseLog))
	}

	newImageTag := request.ImageTag
	if newImageTag == "" {
		rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
		return util.WrapSingleResult(rsp, nil)
	}
	for _, imageStr := range imageList {
		currentImageTag, err := util.GetImageTag(imageStr)
		if err != nil {
			return util.WrapSingleResult(util.HandleError(rsp, "failed to get image tag", err, baseLog))
		}

		klog.Infof("current image tag:%s, new image tag:%s, downgrade rule:%s, %s", currentImageTag, newImageTag, downgradeRuleJson, baseLog)
		// 检查镜像版本
		allow, err := allowUpgrade(downgradeRule, currentImageTag, newImageTag)
		if err != nil {
			return util.WrapSingleResult(util.HandleError(rsp, "failed to check tag version", err, baseLog))
		}

		if !allow {
			return util.WrapSingleResult(util.HandleFailed(rsp,
				fmt.Sprintf("cross-version downgrade is not allowed, current image tag:%s", currentImageTag),
				fmt.Sprintf("image downgrade check rule:%s", downgradeRuleJson), baseLog))
		}
	}

	klog.V(5).Infof("cross-version downgrade check pass, %s", baseLog)
	rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
	return util.WrapSingleResult(rsp, nil)
}

// 通过镜像Tag判断是否允许升级
func allowUpgrade(rule *model.HealthCheckRule, oldImageTag, newImageTag string) (bool, error) {
	oldTagVersion, err := util.GetVersionFromImageTag(oldImageTag)
	if err != nil {
		klog.Errorf("failed to get version from image tag, err:%v", err)
		return false, err
	}
	newTagVersion, err := util.GetVersionFromImageTag(newImageTag)
	if err != nil {
		klog.Errorf("failed to get version from image tag, err:%v", err)
		return false, err
	}
	// 检查主版本是否有降低
	downgradeMajorSwitch, err := rule.GetString(FORBID_MAJOR_VERSION_DOWNGRADE)
	if err != nil {
		return false, err
	}
	if downgradeMajorSwitch == SWITCH_ENABLE {
		klog.V(5).Infof("major check, old major:%d, new major:%d", oldTagVersion.MajorVersion, newTagVersion.MajorVersion)
		if oldTagVersion.MajorVersion > newTagVersion.MajorVersion {
			return false, nil
		}
	}

	// 检查次版本是否有降低
	downgradeMinorSwitch, err := rule.GetString(FORBID_MINOR_VERSION_DOWNGRADE)
	if err != nil {
		return false, err
	}
	if strings.TrimSpace(downgradeMinorSwitch) == SWITCH_ENABLE {
		klog.V(5).Infof("minor check, old minor:%d, new minor:%d", oldTagVersion.MinorVersion, newTagVersion.MinorVersion)
		if oldTagVersion.MajorVersion == newTagVersion.MajorVersion && oldTagVersion.MinorVersion > newTagVersion.MinorVersion {
			return false, nil
		}
	}

	// 检查次版本是否有降低
	downgradePatchSwitch, err := rule.GetString(FORBID_PATCH_VERSION_DOWNGRADE)
	if err != nil {
		return false, err
	}
	if downgradePatchSwitch == SWITCH_ENABLE {
		klog.V(5).Infof("patch check, old patch:%d, new patch:%d", oldTagVersion.PatchVersion, newTagVersion.PatchVersion)
		if oldTagVersion.MajorVersion == newTagVersion.MajorVersion && oldTagVersion.MinorVersion == newTagVersion.MinorVersion &&
			oldTagVersion.PatchVersion > newTagVersion.PatchVersion {
			return false, nil
		}
	}

	// 检查TKE版本是否有降低
	downgradeTKEVersionSwitch, err := rule.GetString(FORBID_TKE_VERSION_DOWNGRADE)
	if err != nil {
		return false, err
	}
	if downgradeTKEVersionSwitch == SWITCH_ENABLE {
		// 没有tke.xx的versing不做校验
		if oldTagVersion.TkeVersion != 0 && newTagVersion.TkeVersion != 0 {
			klog.V(5).Infof("tke version check, old tke:%d, new tke:%d", oldTagVersion.TkeVersion, newTagVersion.TkeVersion)
			if oldTagVersion.MajorVersion == newTagVersion.MajorVersion && oldTagVersion.MinorVersion == newTagVersion.MinorVersion &&
				oldTagVersion.PatchVersion == newTagVersion.PatchVersion && oldTagVersion.TkeVersion > newTagVersion.TkeVersion {
				return false, nil
			}
		}
	}

	return true, nil
}
