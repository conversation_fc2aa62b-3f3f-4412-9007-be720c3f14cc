package downgrade

import (
	"testing"

	"github.com/stretchr/testify/assert"

	"git.woa.com/kmetis/starship/pkg/model"
)

func TestAllowUpgrade(t *testing.T) {

	tests := []struct {
		name           string
		rule           *model.HealthCheckRule
		oldImageTag    string
		newImageTag    string
		expectedResult bool
		expectedError  error
	}{
		{
			name: "正常升级",
			rule: &model.HealthCheckRule{
				Name: ProviderName,
				Value: map[string]interface{}{
					FORBID_MAJOR_VERSION_DOWNGRADE: "disable",
					FORBID_MINOR_VERSION_DOWNGRADE: "disable",
					FORBID_PATCH_VERSION_DOWNGRADE: "disable",
					FORBID_TKE_VERSION_DOWNGRADE:   "disable",
				},
			},
			oldImageTag:    "v1.2.3-tke.4",
			newImageTag:    "v1.2.4-tke.5",
			expectedResult: true,
			expectedError:  nil,
		},

		{
			name: "禁止主版本降级",
			rule: &model.HealthCheckRule{
				Name: ProviderName,
				Value: map[string]interface{}{
					FORBID_MAJOR_VERSION_DOWNGRADE: SWITCH_ENABLE,
					FORBID_MINOR_VERSION_DOWNGRADE: "disable",
					FORBID_PATCH_VERSION_DOWNGRADE: "disable",
					FORBID_TKE_VERSION_DOWNGRADE:   "disable",
				},
			},
			oldImageTag:    "v2.2.3-tke.4",
			newImageTag:    "v1.2.3-tke.4",
			expectedResult: false,
			expectedError:  nil,
		},
		{
			name: "禁止次版本降级",
			rule: &model.HealthCheckRule{
				Name: ProviderName,
				Value: map[string]interface{}{
					FORBID_MAJOR_VERSION_DOWNGRADE: "disable",
					FORBID_MINOR_VERSION_DOWNGRADE: SWITCH_ENABLE,
					FORBID_PATCH_VERSION_DOWNGRADE: "disable",
					FORBID_TKE_VERSION_DOWNGRADE:   "disable",
				},
			},
			oldImageTag:    "v1.3.3-tke.4",
			newImageTag:    "v1.2.3-tke.4",
			expectedResult: false,
			expectedError:  nil,
		},
		{
			name: "禁止补丁版本降级",
			rule: &model.HealthCheckRule{
				Name: ProviderName,
				Value: map[string]interface{}{
					FORBID_MAJOR_VERSION_DOWNGRADE: "disable",
					FORBID_MINOR_VERSION_DOWNGRADE: "disable",
					FORBID_PATCH_VERSION_DOWNGRADE: SWITCH_ENABLE,
					FORBID_TKE_VERSION_DOWNGRADE:   "disable",
				},
			},
			oldImageTag:    "v1.2.5-tke.4",
			newImageTag:    "v1.2.3-tke.4",
			expectedResult: false,
			expectedError:  nil,
		},
		{
			name: "禁止TKE版本降级",
			rule: &model.HealthCheckRule{
				Name: ProviderName,
				Value: map[string]interface{}{
					FORBID_MAJOR_VERSION_DOWNGRADE: "disable",
					FORBID_MINOR_VERSION_DOWNGRADE: "disable",
					FORBID_PATCH_VERSION_DOWNGRADE: "disable",
					FORBID_TKE_VERSION_DOWNGRADE:   SWITCH_ENABLE,
				},
			},
			oldImageTag:    "v1.2.3-tke.5",
			newImageTag:    "v1.2.3-tke.4",
			expectedResult: false,
			expectedError:  nil,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := allowUpgrade(tt.rule, tt.oldImageTag, tt.newImageTag)
			assert.Equal(t, tt.expectedResult, result)
			assert.Equal(t, tt.expectedError, err)
		})
	}
}
