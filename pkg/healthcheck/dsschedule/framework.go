package dsschedule

import (
	"context"
	"fmt"
	"sync"

	v1 "k8s.io/api/core/v1"
	v12 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/client-go/informers"
	"k8s.io/client-go/kubernetes"
	"k8s.io/klog/v2"
	schedulerconfig "k8s.io/kubernetes/pkg/scheduler/apis/config/latest"
	"k8s.io/kubernetes/pkg/scheduler/framework"
	"k8s.io/kubernetes/pkg/scheduler/framework/plugins"
	"k8s.io/kubernetes/pkg/scheduler/framework/runtime"
)

type SchedulerFramework struct {
	f      framework.Framework
	stopCh chan struct{}
}

func NewSchedulerFramework(ctx context.Context, clientset kubernetes.Interface, snapshot framework.SharedLister) (*SchedulerFramework, error) {
	cfg, err := schedulerconfig.Default()
	if len(cfg.Profiles) < 1 {
		klog.Warningf("KubeSchedulerCOnfiguration profiles is less than 1")
		return nil, fmt.Errorf("KubeSchedulerCOnfiguration profiles is less than 1")
	}

	cfg.Profiles[0].Plugins.Filter = mergeFilterPluginSets(&cfg.Profiles[0].Plugins.Filter)
	cfg.Profiles[0].Plugins.PreFilter = mergeFilterPluginSets(&cfg.Profiles[0].Plugins.PreFilter)
	cfg.Profiles[0].Plugins.MultiPoint = mergeFilterPluginSets(&cfg.Profiles[0].Plugins.MultiPoint)

	registry := plugins.NewInTreeRegistry()
	mergeRegistry(&registry)

	informerFactory := informers.NewSharedInformerFactory(clientset, 0)
	frame, err := runtime.NewFramework(ctx, registry,
		&cfg.Profiles[0],
		runtime.WithInformerFactory(informerFactory),
		runtime.WithSnapshotSharedLister(snapshot))
	if err != nil {
		return nil, err
	}

	return &SchedulerFramework{
		f:      frame,
		stopCh: nil,
	}, nil
}

type Snapshot struct {
	clientset    kubernetes.Interface
	nodeInfoList []*framework.NodeInfo
	nodeInfoMap  map[string]*framework.NodeInfo
	mu           sync.RWMutex
}

func NewSnapshot(clientset kubernetes.Interface) *Snapshot {
	return &Snapshot{
		clientset:    clientset,
		nodeInfoList: make([]*framework.NodeInfo, 0),
		nodeInfoMap:  make(map[string]*framework.NodeInfo),
		mu:           sync.RWMutex{},
	}
}

func (s *Snapshot) AddNode(no *v1.Node) error {
	if no == nil {
		return fmt.Errorf("node object is nil")
	}
	s.mu.Lock()
	defer s.mu.Unlock()

	selector := fields.ParseSelectorOrDie("spec.nodeName=" + no.Name)
	podList, err := s.clientset.CoreV1().Pods("").List(context.TODO(), v12.ListOptions{
		FieldSelector:   selector.String(),
		ResourceVersion: "0",
	})
	if err != nil {
		return err
	}

	var nodePods = make([]*v1.Pod, len(podList.Items))
	for index, p := range podList.Items {
		deepCopyPod := p.DeepCopy()
		nodePods[index] = deepCopyPod
	}

	nodeInfo := framework.NewNodeInfo(nodePods...)
	nodeInfo.SetNode(no)

	s.nodeInfoList = append(s.nodeInfoList, nodeInfo)
	s.nodeInfoMap[no.Name] = nodeInfo

	return nil
}

func (s *Snapshot) AddAllNodes(nodes *v1.NodeList) error {
	if nodes == nil {
		return fmt.Errorf("nodes is nil")
	}
	s.mu.Lock()
	defer s.mu.Unlock()

	podList, err := s.clientset.CoreV1().Pods("").List(context.TODO(), v12.ListOptions{
		ResourceVersion: "0",
	})
	if err != nil {
		return err
	}

	var nodePodsMap = make(map[string][]*v1.Pod)
	for _, p := range podList.Items {
		deepCopyPod := p.DeepCopy()
		nodeName := deepCopyPod.Spec.NodeName
		if nodeName == "" {
			continue
		}
		if _, ok := nodePodsMap[nodeName]; !ok {
			nodePodsMap[nodeName] = make([]*v1.Pod, 0)
		}
		nodePodsMap[nodeName] = append(nodePodsMap[nodeName], deepCopyPod)
	}

	for _, no := range nodes.Items {
		var nodePods []*v1.Pod
		if pods, ok := nodePodsMap[no.Name]; ok {
			nodePods = pods
		} else {
			nodePods = make([]*v1.Pod, 0)
		}
		nodeInfo := framework.NewNodeInfo(nodePods...)
		nodeInfo.SetNode(&no)
		s.nodeInfoList = append(s.nodeInfoList, nodeInfo)
		s.nodeInfoMap[no.Name] = nodeInfo
	}

	return nil
}

func (s *Snapshot) List() ([]*framework.NodeInfo, error) {
	return s.nodeInfoList, nil
}

func (s *Snapshot) HavePodsWithAffinityList() ([]*framework.NodeInfo, error) {
	var nodeList []*framework.NodeInfo

	for _, ni := range s.nodeInfoList {
		if len(ni.PodsWithAffinity) > 0 {
			nodeList = append(nodeList, ni)
		}
	}

	return nodeList, nil
}

func (s *Snapshot) HavePodsWithRequiredAntiAffinityList() ([]*framework.NodeInfo, error) {
	var nodeList []*framework.NodeInfo
	for _, ni := range s.nodeInfoList {
		if len(ni.PodsWithRequiredAntiAffinity) > 0 {
			nodeList = append(nodeList, ni)
		}
	}
	return nodeList, nil
}

func (s *Snapshot) Get(nodeName string) (*framework.NodeInfo, error) {
	ni, ok := s.nodeInfoMap[nodeName]
	if !ok {
		return nil, fmt.Errorf("node info not found for name %s", nodeName)
	}

	return ni, nil
}

func (s *Snapshot) NodeInfos() framework.NodeInfoLister {
	return s
}

func (s *Snapshot) StorageInfos() framework.StorageInfoLister {
	return nil
}
