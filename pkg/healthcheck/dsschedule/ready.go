/*
daemonset模拟调度

功能：检查集群节点是否可调度

该升级策略默认关闭，可以通过在七彩石配置healthchecks.rules开启
*/
package dsschedule

import (
	"context"
	"fmt"
	"sync"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/sets"
	"k8s.io/klog"
	"k8s.io/kubernetes/pkg/controller/daemon"
	scheduler_apis_config "k8s.io/kubernetes/pkg/scheduler/apis/config"
	"k8s.io/kubernetes/pkg/scheduler/framework"
	"k8s.io/kubernetes/pkg/scheduler/framework/runtime"

	healthpb "git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/healthprovider"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/kmetis/starship/pkg/util/kubeclient"
)

const (
	ProviderName = "dsSchedule"
	RiskName     = "ds模拟调度"
	// 大集群节点数阈值
	largeClusterNodeThreshold = 500
)

var (
	once    sync.Once
	checker *DSScheduleChecker
	// 默认全局禁用的插件
	disabledPlugins = sets.NewString(
		"ComputeResource", "FitIPResource", "EvictHistory", "LocalReplicas", "CheckEkletStaticIP", "RetainPod", "SuperNode",
	)
)

func init() {
	healthprovider.RegisterComponentHealthFactory(
		ProviderName,
		func(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
			return initDSScheduleChecker(ctx)
		},
	)
}

func initDSScheduleChecker(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
	var err error
	once.Do(func() {
		checker = &DSScheduleChecker{name: ProviderName, risk: RiskName, level: healthcheck.RISK_LEVEL_FATAL}
		err = checker.init()
	})
	return checker, err
}

type DSScheduleChecker struct {
	name  string
	risk  string
	level string
}

func (t *DSScheduleChecker) init() error {
	klog.Infof("init DSScheduleChecker success")
	return nil
}

func (t *DSScheduleChecker) IsHealthy(ctx context.Context, request *healthpb.ComponentHealthyRequest) (*healthpb.ComponentHealthyReply, error) {
	klog.Infof("request info is %v", request)
	return &healthpb.ComponentHealthyReply{}, nil
}

func (t *DSScheduleChecker) IsReady(ctx context.Context, request *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error) {
	baseLog := fmt.Sprintf("clusterId:%s, taskId:%d, traceId:%s,", request.ClusterId, request.TaskId, request.TraceId)
	klog.V(5).Infof("run ds schedule check, %s", baseLog)

	rsp := &strategyprovider.TaskStrategyReply{
		HealthCheckName: t.name,
		RiskName:        t.risk,
		Level:           t.level,
	}
	//获取 k8s client
	client, err := kubeclient.GetTargetK8sClient(request)
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get k8s client", err, baseLog))
	}

	clusterId := request.ClusterId
	namespace := util.GetComponentNamespace(clusterId, request.Namespace)
	workloadName := util.GetWorkloadName(clusterId, request.WorkloadName)
	if request.WorkloadType != "daemonset" {
		errMsg := fmt.Sprintf("invalid workloadType:%v", request.WorkloadType)
		return util.WrapSingleResult(util.HandleError(rsp, errMsg, nil, baseLog))
	}

	ds, err := client.AppsV1().DaemonSets(namespace).Get(ctx, workloadName, metav1.GetOptions{})
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "get daemonset failed", err, baseLog))
	}

	rule := request.GetHealthCheckRule(ProviderName)
	if rule == nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get ds schedule check rule", nil, baseLog))
	}

	excludeNodeNames := make([]string, 0)
	if excludeNodeNames, err = rule.GetStringArray("excludeNodeNames"); err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get excludeNodeNames from ds schedule check rule", err, baseLog))
	}
	excludeNodeSet := sets.NewString(excludeNodeNames...)

	// 获取所有nodes
	nodes, err := client.CoreV1().Nodes().List(ctx, metav1.ListOptions{ResourceVersion: "0"})
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "get nodes failed", err, baseLog))
	}

	// 构造模拟调度过程中依赖的snapshot
	snapshot := NewSnapshot(client)
	if len(nodes.Items) < largeClusterNodeThreshold {
		// 小集群，直接获取所有pod
		if err = snapshot.AddAllNodes(nodes); err != nil {
			return util.WrapSingleResult(util.HandleError(rsp, "add all nodes to snapshot failed", err, baseLog))
		}
	} else {
		// 大集群，按照node分批获取指定node上的pod
		for _, no := range nodes.Items {
			// 检查是否需要排除该节点
			if excludeNodeSet.Has(no.Name) {
				continue
			}
			nodeDeepcopy := no.DeepCopy()
			err = snapshot.AddNode(nodeDeepcopy)
			if err != nil {
				return util.WrapSingleResult(util.HandleError(rsp, fmt.Sprintf("add node %s to snapshot failed", no.Name), err, baseLog))
			}
		}
	}

	// 创建scheduler framework，默认包含以下插件：
	// 1、NodeUnschedulable：检查pod是否容忍节点不可调度的污点（node.kubernetes.io/unschedulable=true）
	// 2、NodeName：检查节点名称是否满足pod调度要求
	// 3、TaintToleration：检查节点污点是否满足调度要求
	// 4、NodeAffinity：检查节点是否满足node亲和性的要求
	// 5、NodeResourcesFit：检查节点资源是否满足pod调度要求
	// 6、NodePorts：检查节点端口是否满足pod调度要求
	// 7、VolumeRestrictions：检查pod的volume是否和节点存在的volume冲突
	// 8、EBSLimits：检查pod的EBS卷是否超出节点限制
	// 9、GCEPDLimits：检查pod的GCE资源是否超出节点限制
	// 10、NodeVolumeLimits：检查节点是否有足够的volume可被pod使用
	// 11、AzureDiskLimits：检查pod的AzureDisk是否超出节点限制
	// 12、VolumeBinding：检查PVC和PV是否满足绑定要求
	// 13、VolumeZone：检查PV是否在调度节点所在的zone
	// 14、PodTopologySpread：检查pod是否满足拓扑调度要求
	// 15、InterPodAffinity：检查pod是否满足pod亲和性的调度要求
	// 任何插件返回非success状态，则模拟调度失败
	ctxWithCancel, cancel := context.WithCancel(ctx)
	defer cancel()
	sf, err := NewSchedulerFramework(ctxWithCancel, client, snapshot)
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "NewSchedulerFramework failed", err, baseLog))
	}

	// 对每个node进行模拟调度
	for _, node := range nodes.Items {
		// 检查是否需要排除该节点
		if excludeNodeSet.Has(node.Name) {
			continue
		}
		nodeDeepcopy := node.DeepCopy()
		// 1> 判断节点上是否需要运行DaemonSet pod
		shouldRun, _ := daemon.NodeShouldRunDaemonPod(nodeDeepcopy, ds)
		if !shouldRun {
			klog.Warningf("node %s should not run daemonSet pod, %s", node.Name, baseLog)
			continue
		}

		// 2> 判断节点是否可以调度DaemonSet pod
		pod := daemon.NewPod(ds, node.Name)
		state := framework.NewCycleState()
		// 2.1> 运行PreFilterPlugins
		_, status := sf.f.RunPreFilterPlugins(ctx, state, pod)
		if !status.IsSuccess() {
			errMsg := fmt.Sprintf("node %s can not schedule pod,code:%v, message:%v, error:%v", node.Name, status.Code(), status.Message(), status.AsError())
			return util.WrapSingleResult(util.HandleFailed(rsp, errMsg, "check scheduler framework", baseLog))
		}
		// 2.2> 运行FilterPlugins
		ni, err := snapshot.Get(node.Name)
		if err != nil {
			return util.WrapSingleResult(util.HandleError(rsp, fmt.Sprintf("get node %s from snapshot failed", node.Name), err, baseLog))
		}

		status = sf.f.RunFilterPlugins(ctx, state, pod, ni)
		if !status.IsSuccess() {
			errMsg := fmt.Sprintf("node %s can not schedule pod, code:%v, message:%v, error:%v", node.Name, status.Code(), status.Message(), status.AsError())
			return util.WrapSingleResult(util.HandleFailed(rsp, errMsg, "check scheduler framework", baseLog))
		}
	}
	klog.V(5).Infof("ds schedule check pass, %s", baseLog)
	rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
	return util.WrapSingleResult(rsp, nil)
}

func mergeRegistry(registry *runtime.Registry) {
	removeKeys := make([]string, 0)
	for name, _ := range *registry {
		if disabledPlugins.Has(name) {
			removeKeys = append(removeKeys, name)
		}
	}
	for _, name := range removeKeys {
		delete(*registry, name)
	}
}

func mergeFilterPluginSets(defaultPluginSet *scheduler_apis_config.PluginSet) scheduler_apis_config.PluginSet {
	enabledPlugins := []scheduler_apis_config.Plugin{}
	if defaultPluginSet == nil {
		return scheduler_apis_config.PluginSet{Enabled: enabledPlugins}
	}
	for _, defaultEnabledPlugin := range defaultPluginSet.Enabled {
		if disabledPlugins.Has(defaultEnabledPlugin.Name) {
			continue
		}
		enabledPlugins = append(enabledPlugins, defaultEnabledPlugin)
	}
	return scheduler_apis_config.PluginSet{Enabled: enabledPlugins}
}
