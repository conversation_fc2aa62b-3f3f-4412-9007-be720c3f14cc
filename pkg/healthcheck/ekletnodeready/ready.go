package eksEkletReady

import (
	"context"
	"fmt"
	"sync"
	"time"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/klog/v2"

	healthpb "git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/healthprovider"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/kmetis/starship/pkg/util/kubeclient"
)

var (
	once    sync.Once
	checker *EksEkletReadyChecker
)

func init() {
	healthprovider.RegisterComponentHealthFactory(
		ProviderName,
		func(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
			return initEksEkletReadyChecker(ctx)
		},
	)
}

func initEksEkletReadyChecker(_ *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
	var err error
	once.Do(func() {
		checker = &EksEkletReadyChecker{name: ProviderName, risk: RiskName, level: healthcheck.RISK_LEVEL_FATAL}
		err = checker.init()
	})
	return checker, err
}

func (t *EksEkletReadyChecker) init() error {
	klog.Infof("init EksEkletReadyChecker success")
	return nil
}

const (
	ProviderName = "eksEkletReady"
	RiskName     = "eklet节点状态检查"
)

type EksEkletReadyChecker struct {
	name  string
	risk  string
	level string
}

func (t *EksEkletReadyChecker) IsHealthy(ctx context.Context, request *healthpb.ComponentHealthyRequest) (*healthpb.ComponentHealthyReply, error) {
	klog.Infof("request info is %v", request)
	return &healthpb.ComponentHealthyReply{}, nil
}

func (t *EksEkletReadyChecker) IsReady(ctx context.Context, req *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error) {
	baseLog := fmt.Sprintf("traceId:%s", req.TraceId)
	klog.Infof("running node health check for cluster %s, %s", req.ClusterId, baseLog)
	rsp := &strategyprovider.TaskStrategyReply{
		HealthCheckName: t.name,
		RiskName:        t.risk,
		Level:           t.level,
	}

	metaClient, err := kubeclient.GetTargetK8sClient(req)
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get k8s client", err, baseLog))
	}

	clsClient, err := kubeclient.GetK8sClient(req.ClusterId, req.IanvsToken, req.User, req.ProductName)
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get k8s client", err, baseLog))
	}

	switch req.Action {
	case util.TaskActionPreCheck:
		err = preCheckEklet(ctx, clsClient, metaClient, req)
		if err != nil {
			return util.WrapSingleResult(util.HandleError(rsp, "eksEkletReadyChecker pre check failed", err, baseLog))
		}
		klog.V(5).Infof("eksEkletReadyChecker pre check pass, %s", baseLog)
		rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
		return util.WrapSingleResult(rsp, nil)
	case util.TaskActionPostCheck:
		// 针对eksEkletReadyChecker 的post check 如果失败，重试6次，每次间隔20s
		maxRetries := 6
		for i := 1; i <= maxRetries; i++ {
			retryInterval := time.Duration(i*i) * 5 * time.Second
			if retryInterval > 60*time.Second {
				retryInterval = 60 * time.Second
			}
			time.Sleep(retryInterval)
			err = postCheckEklet(ctx, clsClient, req.ImageTag)
			if err == nil {
				// 如果检查成功，跳出循环
				klog.V(5).Infof("eksEkletReadyChecker post check pass, %s", baseLog)
				rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
				rsp.Detail = ""
				return util.WrapSingleResult(rsp, nil)
			}
		}
		// 如果所有重试都失败了，返回最后一次的错误信息
		return util.WrapSingleResult(util.HandleError(rsp, fmt.Sprintf("eksEkletReadyChecker post check failed on attempt %d", maxRetries), err, baseLog))
	default:
		return util.WrapSingleResult(util.HandleError(rsp, "eksEkletReadyChecker check failed, unknown action", nil, baseLog))
	}
}

// 预检：检查超级节点是否ready
func preCheckEklet(ctx context.Context, clsClient, metaClient kubernetes.Interface, req *strategyprovider.TaskStrategyRequest) error {
	nodes, err := clsClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{
		LabelSelector:   "node.kubernetes.io/instance-type=eklet",
		ResourceVersion: "0",
	})
	if err != nil {
		return fmt.Errorf("get node list failed: %v", err)
	}

	for _, node := range nodes.Items {
		if !isNodeReady(&node) {
			return fmt.Errorf("cls %s node %s/%s is not ready: %v", req.ClusterId, node.Namespace, node.Name, node.Status.NodeInfo)
		}
	}

	return nil
}

func isNodeReady(node *corev1.Node) bool {
	for _, condition := range node.Status.Conditions {
		if condition.Type == "Ready" && condition.Status == "True" {
			return true
		}
	}
	return false
}

// 2、check 超级节点是否ok
func postCheckEklet(ctx context.Context, clsClient kubernetes.Interface, targetVersion string) error {
	nodes, err := clsClient.CoreV1().Nodes().List(ctx, metav1.ListOptions{
		LabelSelector: "node.kubernetes.io/instance-type=eklet",
	})
	if err != nil {
		return fmt.Errorf("fail to get nodes: %v", err)
	}

	if len(nodes.Items) == 0 {
		return fmt.Errorf("eks-controllers is not ready")
	}

	for _, node := range nodes.Items {
		if !isNodeReady(&node) {
			return fmt.Errorf("node %s is not ready", node.Name)
		}
		if node.Status.NodeInfo.KubeletVersion != targetVersion {
			return fmt.Errorf("node %s is not upgrade", node.Name)
		}
	}
	return nil
}
