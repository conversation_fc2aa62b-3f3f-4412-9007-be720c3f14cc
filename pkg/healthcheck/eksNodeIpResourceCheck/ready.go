/*
升级前预检:eks集群节点ip资源是充足

检查实现逻辑：
1. 检查eks集群节点ip资源是否充足
*/

package eksNodeIpResourceCheck

import (
	"context"
	"fmt"
	"strconv"
	"sync"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog/v2"

	healthpb "git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/healthprovider"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/kmetis/starship/pkg/util/kubeclient"
)

const (
	ProviderName                   = "eksNodeIpResourceCheck"
	RiskName                       = "eks集群ip资源检查"
	AvailableIPCountKey            = "eks.tke.cloud.tencent.com/available-ip-count"
	DefaultAvailableIpAddressCount = 5
)

var (
	once    sync.Once
	checker *IpResourceChecker
)

func init() {
	healthprovider.RegisterComponentHealthFactory(
		ProviderName,
		func(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
			return initIpResourceChecker(ctx)
		},
	)
}

func initIpResourceChecker(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
	var err error
	once.Do(func() {
		checker = &IpResourceChecker{name: ProviderName, risk: RiskName, level: healthcheck.RISK_LEVEL_FATAL}
		err = checker.init()
	})
	return checker, err
}

type IpResourceChecker struct {
	name  string
	risk  string
	level string
}

func (t *IpResourceChecker) init() error {
	klog.Infof("init IpResourceChecker success")
	return nil
}

func (t *IpResourceChecker) IsHealthy(ctx context.Context, request *healthpb.ComponentHealthyRequest) (*healthpb.ComponentHealthyReply, error) {
	klog.Infof("request info is %v", request)
	return &healthpb.ComponentHealthyReply{}, nil
}

func (t *IpResourceChecker) IsReady(ctx context.Context, req *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error) {
	baseLog := fmt.Sprintf("traceId:%s, clusterId:%s, taskId:%d", req.TraceId, req.ClusterId, req.TaskId)
	klog.V(5).Infof("run ip resource check, %s", baseLog)

	rsp := &strategyprovider.TaskStrategyReply{
		HealthCheckName: t.name,
		RiskName:        t.risk,
		Level:           t.level,
	}
	client, err := kubeclient.GetTargetK8sClient(req)
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get k8s client", err, baseLog))
	}
	clusterId := req.ClusterId
	namespace := util.GetComponentNamespace(clusterId, req.Namespace)
	workloadName := util.GetWorkloadName(clusterId, req.WorkloadName)
	deployment, err := client.AppsV1().Deployments(namespace).Get(ctx, workloadName, metav1.GetOptions{})
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get deployment", err, baseLog))
	}
	maxSurge := util.MaxSurge(*deployment)
	nodes, err := client.CoreV1().Nodes().List(ctx, metav1.ListOptions{
		LabelSelector: "node.kubernetes.io/instance-type=eklet",
	})
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get nodes", err, baseLog))
	}
	if len(nodes.Items) == 0 {
		return util.WrapSingleResult(util.HandleError(rsp, "no eklet node", nil, baseLog))
	}
	ipCount := 0
	for _, node := range nodes.Items {
		count, err := strconv.Atoi(node.Labels[AvailableIPCountKey])
		if err != nil {
			return util.WrapSingleResult(util.HandleError(rsp, fmt.Sprintf("node : %s no availableIPCountKey", node.Name), err, baseLog))
		}
		ipCount = ipCount + count
	}

	if ipCount < DefaultAvailableIpAddressCount+int(maxSurge) {
		errMsg := fmt.Sprintf("available ip count is less than %d", DefaultAvailableIpAddressCount+int(maxSurge))
		return util.WrapSingleResult(util.HandleError(rsp, errMsg, nil, baseLog))
	}

	klog.V(5).Infof("ip resource check pass, %s", baseLog)
	rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
	return util.WrapSingleResult(rsp, nil)
}
