/*
镜像检查

检查实现逻辑：
1. 检查镜像repo是否合法，镜像repo支持正则。
*/
package image

import (
	"context"
	"fmt"
	"regexp"
	"sync"

	"k8s.io/klog/v2"

	healthpb "git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/healthprovider"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/kmetis/starship/pkg/util/kubeclient"
)

const (
	ProviderName = "allowedImageRepo"
	RiskName     = "镜像来源检查"
)

var (
	once    sync.Once
	checker *TKEImageChecker
)

func init() {
	healthprovider.RegisterComponentHealthFactory(
		ProviderName,
		func(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
			return initTKEImageChecker(ctx)
		},
	)
}

func initTKEImageChecker(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
	var err error
	once.Do(func() {
		checker = &TKEImageChecker{name: ProviderName, risk: RiskName, level: healthcheck.RISK_LEVEL_FATAL}
		err = checker.init()
	})
	return checker, err
}

type TKEImageChecker struct {
	name  string
	risk  string
	level string
}

func (t *TKEImageChecker) init() error {
	klog.Infof("init TKEImageChecker success")
	return nil
}

func (t *TKEImageChecker) IsHealthy(ctx context.Context, request *healthpb.ComponentHealthyRequest) (*healthpb.ComponentHealthyReply, error) {
	klog.Infof("request info is %v", request)
	return &healthpb.ComponentHealthyReply{}, nil
}

func (t *TKEImageChecker) IsReady(ctx context.Context, req *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error) {
	baseLog := fmt.Sprintf("traceId:%s", req.TraceId)
	klog.V(5).Infof("run image check, %s", baseLog)

	rsp := &strategyprovider.TaskStrategyReply{
		HealthCheckName: t.name,
		RiskName:        t.risk,
		Level:           t.level,
	}
	client, err := kubeclient.GetTargetK8sClient(req)
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get k8s client", err, baseLog))
	}
	clusterId := req.ClusterId
	namespace := util.GetComponentNamespace(clusterId, req.Namespace)
	workloadName := util.GetWorkloadName(clusterId, req.WorkloadName)

	// 获取workload的镜像信息
	imageList, err := util.GetWorkloadImage(client, req.WorkloadType, namespace, workloadName, req.Extend)
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get workload image", err, baseLog))
	}

	// 从七彩石策略读取repo白名单，并进行匹配
	// NOTICE: 如果指定了要进行镜像检查，七彩石必须配置策略，否则会报错
	if req.HealthCheckRules == nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get health check rule", nil, baseLog))
	}

	allowedImageRule := req.GetHealthCheckRule(ProviderName)

	// repo信息如下：
	// registry: ccr.ccs.tencentyun.com/tkeimages/eklet-amd64
	if allowedImageRule == nil {
		return util.WrapSingleResult(util.HandleError(rsp, "image check rule is empty", nil, baseLog))
	}

	allowedRepo, err := allowedImageRule.GetString("registry")
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get image repo", err, baseLog))
	}

	for _, imageStr := range imageList {
		klog.Infof("workload image:%s, allowed repo:%s, %s", imageStr, allowedRepo, baseLog)

		// 检查进行repo合法
		match, err := regexp.MatchString(allowedRepo, imageStr)
		if err != nil {
			return util.WrapSingleResult(util.HandleError(rsp, "failed to match image repo", err, baseLog))
		}
		if !match {
			return util.WrapSingleResult(util.HandleFailed(rsp,
				fmt.Sprintf("image repo is not allowed, current image:%s", imageStr),
				fmt.Sprintf("please use allowed image repo: %v", allowedRepo),
				baseLog))
		}
	}

	klog.V(5).Infof("image check pass, %s", baseLog)
	rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
	return util.WrapSingleResult(rsp, nil)
}
