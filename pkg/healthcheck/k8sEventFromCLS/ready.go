package k8sevent

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	cls "github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/cls/v20201016"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common"
	"github.com/tencentcloud/tencentcloud-sdk-go/tencentcloud/common/profile"
	"k8s.io/klog/v2"

	healthpb "git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/config"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/healthprovider"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
)

const (
	ProviderName = "k8sEventFromCLS"
	RiskName     = "集群中是否存在异常事件"

	eventTimeInterval = 10 * time.Minute
	// 内部域名，避免云api权限校验失败
	endpoint = "cls.internal.tencentcloudapi.com"

	// k8s event list: https://github.com/kubernetes/kubernetes/blob/master/pkg/kubelet/events/event.go
)

var (
	once    sync.Once
	checker *TKEK8sEventFromCLSChecker
)

func init() {
	healthprovider.RegisterComponentHealthFactory(
		ProviderName,
		func(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
			return initTKEK8sEventFromCLSChecker(ctx)
		},
	)
}

func initTKEK8sEventFromCLSChecker(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
	var err error
	once.Do(func() {
		clsEventConfig := config.GetClsEventConfig()
		credential := common.NewCredential(
			clsEventConfig.SecretId,
			clsEventConfig.SecretKey,
		)
		cpf := profile.NewClientProfile()
		cpf.HttpProfile.Endpoint = endpoint

		var client *cls.Client
		if client, err = cls.NewClient(credential, clsEventConfig.Region, cpf); err != nil {
			err = fmt.Errorf("init cls client failed: %v", err)
			return
		}
		checker = &TKEK8sEventFromCLSChecker{name: ProviderName, risk: RiskName, level: healthcheck.RISK_LEVEL_FATAL, clsClient: client, topicId: clsEventConfig.Topic}
		err = checker.init()
	})
	return checker, err
}

type TKEK8sEventFromCLSChecker struct {
	name      string
	risk      string
	level     string
	clsClient *cls.Client
	topicId   string
}

func (t *TKEK8sEventFromCLSChecker) init() error {
	klog.Infof("init TKEK8sEventFromCLSChecker success")
	return nil
}

func (t *TKEK8sEventFromCLSChecker) IsHealthy(ctx context.Context, request *healthpb.ComponentHealthyRequest) (*healthpb.ComponentHealthyReply, error) {
	klog.Infof("request info is %v", request)
	return &healthpb.ComponentHealthyReply{}, nil
}

func (t *TKEK8sEventFromCLSChecker) IsReady(ctx context.Context, request *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error) {
	baseLog := fmt.Sprintf("traceId:%s", request.TraceId)
	klog.V(5).Infof("run k8sEventFromCLS check, %s", baseLog)

	rsp := &strategyprovider.TaskStrategyReply{
		HealthCheckName: t.name,
		RiskName:        t.risk,
		Level:           t.level,
	}

	rule := request.GetHealthCheckRule(ProviderName)
	if rule == nil {
		klog.Infof("failed to get k8s event check rule, baseLog: %s", baseLog)
		rsp.Code = healthcheck.HEALTHCHECK_CODE_FAILED
		return util.WrapSingleResult(rsp, nil)
	}

	checkEvents, err := rule.GetStringArray("keyword")
	if err != nil {
		klog.Errorf("failed to parse k8s event check rule, baseLog: %s, err:%v", baseLog, err)
		return nil, err
	}
	if len(checkEvents) == 0 {
		klog.Infof("k8s events is empty, baseLog: %s", baseLog)
		rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
		return util.WrapSingleResult(rsp, nil)
	}

	now := time.Now()
	ctxWithTimeout, cancel := context.WithTimeout(ctx, 5*time.Second)
	defer cancel()

	logReq := cls.NewSearchLogRequest()
	logReq.SetContext(ctxWithTimeout)
	logReq.TopicId = common.StringPtr(t.topicId)
	logReq.SyntaxRule = common.Uint64Ptr(1)
	// 查询时间范围：近15分钟
	logReq.From = common.Int64Ptr(now.Add(-10 * time.Minute).UnixMilli())
	logReq.To = common.Int64Ptr(now.UnixMilli())
	// 查询条数最多1000条
	logReq.Limit = common.Int64Ptr(1000)
	logReq.Query = common.StringPtr(buildSearchSQL(request.ClusterId, checkEvents))

	clsResp, err := t.clsClient.SearchLog(logReq)
	if err != nil {
		klog.Errorf("failed to get cls log: %s/%s, err:%v", request.ClusterId, request.Component, err)
		return nil, err
	}

	if len(clsResp.Response.AnalysisResults) == 0 {
		klog.Infof("k8sEventFromCLS check success, cluster: %s, baseLog: %s", request.ClusterId, baseLog)
		rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
		return util.WrapSingleResult(rsp, nil)
	}

	hitEvents := make(map[string]bool)
	for _, log := range clsResp.Response.AnalysisResults {
		var reason, message, involvedObject string
		for _, item := range log.Data {
			if *item.Key == "Reason" {
				reason = *item.Value
			} else if *item.Key == "InvolvedObjectObjName" {
				involvedObject = *item.Value
			} else if *item.Key == "Message" {
				message = *item.Value
			}
		}
		var eventType string
		for _, event := range checkEvents {
			if reason == event || strings.Contains(message, event) {
				eventType = event
				break
			}
		}
		if eventType != "" {
			if _, ok := hitEvents[eventType]; !ok {
				hitEvents[eventType] = true
			}
			klog.Infof("cluster %s k8sEventFromCLS check is warning, reason: %s, involvedObject: %s", request.ClusterId, eventType, involvedObject)
		}
	}

	rsp.Code = healthcheck.HEALTHCHECK_CODE_FAILED
	var events string
	for k, _ := range hitEvents {
		events += fmt.Sprintf("%s,", k)
	}
	events = events[:len(events)-1]
	rsp.Detail = fmt.Sprintf("k8s event check is warning, find events: %v", events)
	rsp.Solution = fmt.Sprintf("please check k8s event, and fix the warning event, events: %v", events)
	return util.WrapSingleResult(rsp, nil)
}

// CLS查询语句，根据集群ID和事件类型过滤并事件类型和触发对象聚合
func buildSearchSQL(clusterId string, checkEvents []string) string {
	var filter string
	var builder strings.Builder
	builder.WriteString(fmt.Sprintf("ClusterID:\"%s\"", clusterId))
	if len(checkEvents) > 0 {
		builder.WriteString(" AND (")
		for _, checkEvent := range checkEvents {
			builder.WriteString(fmt.Sprintf("Reason:\"%s\" OR Message:\"%s\" OR ", checkEvent, checkEvent))
		}
		filter = builder.String()[:builder.Len()-3] + ")"
	} else {
		filter = builder.String()
	}
	return filter + " | select InvolvedObjectObjName, Reason, Message group by InvolvedObjectObjName, Reason, Message"
}
