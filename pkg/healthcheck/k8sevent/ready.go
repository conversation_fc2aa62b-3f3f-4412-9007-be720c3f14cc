package k8sevent

import (
	"context"
	"fmt"
	"strings"
	"sync"
	"time"

	"git.woa.com/kmetis/starship/pkg/util"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog/v2"

	healthpb "git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/healthprovider"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util/kubeclient"
)

const (
	ProviderName = "k8sEvent"
	RiskName     = "集群中是否存在异常事件"

	eventTimeInterval = 10 * time.Minute

	// k8s event list: https://github.com/kubernetes/kubernetes/blob/master/pkg/kubelet/events/event.go
)

var (
	once    sync.Once
	checker *TKEK8sEventChecker
)

func init() {
	healthprovider.RegisterComponentHealthFactory(
		ProviderName,
		func(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
			return initTKEK8sEventChecker(ctx)
		},
	)
}

func initTKEK8sEventChecker(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
	var err error
	once.Do(func() {
		checker = &TKEK8sEventChecker{name: ProviderName, risk: RiskName, level: healthcheck.RISK_LEVEL_FATAL}
		err = checker.init()
	})
	return checker, err
}

type TKEK8sEventChecker struct {
	name  string
	risk  string
	level string
}

func (t *TKEK8sEventChecker) init() error {
	klog.Infof("init TKEK8sEventChecker success")
	return nil
}

func (t *TKEK8sEventChecker) IsHealthy(ctx context.Context, request *healthpb.ComponentHealthyRequest) (*healthpb.ComponentHealthyReply, error) {
	klog.Infof("request info is %v", request)
	return &healthpb.ComponentHealthyReply{}, nil
}

func (t *TKEK8sEventChecker) IsReady(ctx context.Context, request *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error) {
	baseLog := fmt.Sprintf("traceId:%s", request.TraceId)
	klog.V(5).Infof("run k8s event check, %s", baseLog)
	rsp := &strategyprovider.TaskStrategyReply{
		HealthCheckName: t.name,
		RiskName:        t.risk,
		Level:           t.level,
	}

	rule := request.GetHealthCheckRule(ProviderName)
	if rule == nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get k8s event check rule", nil, baseLog))
	}

	checkEvents, err := rule.GetStringArray("keyword")
	if err != nil {
		klog.Errorf("failed to parse k8s event check rule, baseLog: %s, err:%v", baseLog, err)
		return nil, err
	}
	if len(checkEvents) == 0 {
		klog.Infof("k8s events is empty, baseLog: %s", baseLog)
		rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
		return util.WrapSingleResult(rsp, nil)
	}

	client, err := kubeclient.GetTargetK8sClient(request)
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get k8s client", err, baseLog))
	}

	namespace := util.GetComponentNamespace(request.ClusterId, request.Namespace)
	// 只获取指定集群下的events
	events, err := client.CoreV1().Events(namespace).List(ctx, metav1.ListOptions{ResourceVersion: "0"})
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get k8s event", err, baseLog))
	}

	now := time.Now()
	hitEvents := make(map[string]bool)
	for _, event := range events.Items {
		if now.Sub(event.LastTimestamp.Time) <= eventTimeInterval {
			for _, checkEvent := range checkEvents {
				if event.Reason == checkEvent || strings.Contains(event.Message, checkEvent) {
					hitEvents[checkEvent] = true
					klog.Infof("cluster %s k8sEvent check is warning, reason: %s, involvedObject: %s", request.ClusterId, checkEvent, event.InvolvedObject.Name)
				}
			}
		}
	}

	if len(hitEvents) == 0 {
		klog.Infof("k8s event check success, baseLog: %s", baseLog)
		rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
		return util.WrapSingleResult(rsp, nil)
	}

	var eventStr string
	for k, _ := range hitEvents {
		eventStr += fmt.Sprintf("%s,", k)
	}
	eventStr = eventStr[:len(eventStr)-1]
	rsp.Code = healthcheck.HEALTHCHECK_CODE_FAILED
	rsp.Detail = fmt.Sprintf("k8s event check is warning, find events: %v", eventStr)
	rsp.Solution = fmt.Sprintf("please check k8s event, and fix the warning event, events: %v", eventStr)
	return util.WrapSingleResult(rsp, nil)
}
