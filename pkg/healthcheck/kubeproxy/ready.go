package kubeproxy

import (
	"context"
	"fmt"
	"regexp"
	"strings"
	"sync"

	healthpb "git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/healthprovider"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/kmetis/starship/pkg/util/kubeclient"

	appv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/klog/v2"
)

const (
	ProviderName = "kubeProxyArgs"
	RiskName     = "kube-proxy预检校验"

	KUBEPROXY_CONTAINER_NAME              = "kube-proxy"
	KUBEPROXY_DAEMONSET_NAME              = "kube-proxy"
	KUBEPROXY_SHARENS_INIT_CONTAINER_NAME = "kube-proxy-sharens-init"

	KubeProxyControllerImageRepo    = "/tkeimages/hyperkube"
	KubeProxyControllerImageOldRepo = "/ccs-dev/hyperkube"
)

var (
	once    sync.Once
	checker *KubeProxyArgsChecker
)

func init() {
	healthprovider.RegisterComponentHealthFactory(
		ProviderName,
		func(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
			return initKubeProxyArgsChecker(ctx)
		},
	)
}

// initKubeProxyArgsChecker 初始化检查器
func initKubeProxyArgsChecker(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
	var err error
	once.Do(func() {
		checker = &KubeProxyArgsChecker{
			name:  ProviderName,
			risk:  RiskName,
			level: healthcheck.RISK_LEVEL_FATAL,
		}
		err = checker.init()
	})
	return checker, err
}

type KubeProxyArgsChecker struct {
	name  string
	risk  string
	level string
}

func (k *KubeProxyArgsChecker) init() error {
	klog.Infof("init KubeProxyArgsChecker success")
	return nil
}

// IsHealthy 实现健康检查接口
func (k *KubeProxyArgsChecker) IsHealthy(ctx context.Context, request *healthpb.ComponentHealthyRequest) (*healthpb.ComponentHealthyReply, error) {
	klog.Infof("request info is %v", request)
	return &healthpb.ComponentHealthyReply{}, nil
}

// IsReady 实现就绪检查接口，检查kube-proxy的启动参数
func (k *KubeProxyArgsChecker) IsReady(ctx context.Context, req *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error) {
	baseLog := fmt.Sprintf("traceId:%s", req.TraceId)
	klog.V(5).Infof("run kube-proxy args check, %s", baseLog)

	rsp := &strategyprovider.TaskStrategyReply{
		HealthCheckName: k.name,
		RiskName:        k.risk,
		Level:           k.level,
	}

	client, err := kubeclient.GetTargetK8sClient(req)
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get k8s client", err, baseLog))
	}

	clusterId := req.ClusterId
	namespace := util.GetComponentNamespace(clusterId, req.Namespace)
	workloadName := util.GetWorkloadName(clusterId, req.WorkloadName)

	// 获取daemonset信息
	daemonSet, err := client.AppsV1().DaemonSets(namespace).Get(ctx, workloadName, metav1.GetOptions{})
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get daemonset", err, baseLog))
	}

	// 检查kube-proxy pod状态
	if err := checkKubeProxyPodStatus(client, rsp, baseLog); err != nil {
		return util.WrapSingleResult(util.HandleFailed(rsp, err.Error(), "", baseLog))
	}

	// 检查kube-proxy启动参数
	if err := checkKubeProxyArgs(daemonSet, clusterId); err != nil {
		return util.WrapSingleResult(util.HandleFailed(rsp, err.Error(), "", baseLog))
	}

	// 检测镜像repo
	if err := checkKubeProxyImageRepo(daemonSet, clusterId); err != nil {
		return util.WrapSingleResult(util.HandleFailed(rsp, err.Error(), "", baseLog))
	}

	// 校验tag
	if err := checkKubeProxyTag(req.ImageTag); err != nil {
		return util.WrapSingleResult(util.HandleFailed(rsp, err.Error(), "", baseLog))
	}

	klog.V(5).Infof("kube-proxy args check pass, %s", baseLog)
	rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
	return util.WrapSingleResult(rsp, nil)
}

func checkKubeProxyPodStatus(client kubernetes.Interface, rsp *strategyprovider.TaskStrategyReply, baseLog string) error {
	pods, err := client.CoreV1().Pods(metav1.NamespaceSystem).List(context.Background(), metav1.ListOptions{
		LabelSelector:   fmt.Sprintf("k8s-app=%s", KUBEPROXY_DAEMONSET_NAME),
		ResourceVersion: "0",
	})
	if err != nil {
		return fmt.Errorf("failed to list pods: %v", err)
	}

	var notRunningPods []string
	for _, pod := range pods.Items {
		if corev1.PodRunning != pod.Status.Phase {
			notRunningPods = append(notRunningPods, fmt.Sprintf("%s(%s)", pod.Name, pod.Status.Phase))
		}
	}

	if len(notRunningPods) > 0 {
		return fmt.Errorf("some kube-proxy pods are not Running: %s", strings.Join(notRunningPods, ", "))
	}

	return nil
}

// CheckKubeProxyArgs 检查kube-proxy的启动参数
func checkKubeProxyArgs(daemonSet *appv1.DaemonSet, clusterId string) error {
	initContainer := getKubeProxyShareNSInitContainerFromDaemonSet(daemonSet)
	if initContainer != nil {
		for _, arg := range initContainer.Args {
			if strings.Contains(arg, "set ip_vs sharens") {
				return fmt.Errorf("kube proxy Sharens Mode. %s", clusterId)
			}
		}
	}

	daemonContainer := getKubeProxyContainerFromDaemonSet(daemonSet)
	if daemonContainer == nil {
		return fmt.Errorf("kube proxy Container Not Found. %s", clusterId)
	}

	for _, arg := range daemonContainer.Args {
		if strings.Contains(arg, "bpf-snat-ip") {
			return fmt.Errorf("kube-proxy kube-proxy-bpf Mode. %s", clusterId)
		}
	}

	return nil
}

func getKubeProxyShareNSInitContainerFromDaemonSet(daemonSet *appv1.DaemonSet) *corev1.Container {
	for index, container := range daemonSet.Spec.Template.Spec.InitContainers {
		if container.Name == KUBEPROXY_SHARENS_INIT_CONTAINER_NAME {
			return &daemonSet.Spec.Template.Spec.InitContainers[index]
		}
	}
	return nil
}

func getKubeProxyContainerFromDaemonSet(daemonSet *appv1.DaemonSet) *corev1.Container {
	for index, container := range daemonSet.Spec.Template.Spec.Containers {
		if container.Name == KUBEPROXY_CONTAINER_NAME {
			return &daemonSet.Spec.Template.Spec.Containers[index]
		}
	}
	return nil
}

func checkKubeProxyImageRepo(daemonSet *appv1.DaemonSet, clusterId string) error {
	daemonContainer := getKubeProxyContainerFromDaemonSet(daemonSet)
	if daemonContainer == nil {
		return fmt.Errorf("kube proxy Container Not Found. %s", clusterId)
	}

	repo, _ := util.GetImageRepoAndTag(daemonContainer.Image)
	repoName := repo
	if index := strings.Index(repo, "/"); index != -1 {
		repoName = repo[index:]
	}

	if repoName != KubeProxyControllerImageRepo && repoName != KubeProxyControllerImageOldRepo {
		return fmt.Errorf("kube proxy Image Not Standard repo. %s", repo)
	}
	return nil
}

func checkKubeProxyTag(imageTag string) error {
	if imageTag == "" {
		return nil
	}

	// Match image tag format like v1.18.4-tke.49-rc1
	matched, err := regexp.MatchString(`^v\d+(\.\d+)*-tke(\.[0-9]+)*(-[a-z0-9]+)*$`, imageTag)
	if err != nil {
		return fmt.Errorf("regex match failed: %v", err)
	}
	if !matched {
		return fmt.Errorf("invalid image tag format, expected v*-tke* pattern, got: %s", imageTag)
	}
	return nil
}
