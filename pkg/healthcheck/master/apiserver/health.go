package apiserver

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	healthpb "git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/healthprovider"
	"git.woa.com/kmetis/starship/pkg/metrics"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/tke/tke-monitor/pkg/pb"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"k8s.io/klog/v2"
)

const (
	ProviderName         = "apiserver"
	TKEMonitorServerAddr = "TKE_MONITOR_GRPC_SERVER_ADDR"
	RiskName             = "apiserver连通性拨测" // apiserver failed

	// 探测apiserver的超时时间和探测次数配置
	DAIL_TOTAL_COUNT          = 5    // 探测次数
	DAIL_TIMEOUT_MILLSECONDS  = 5000 // 超时时间
	DAIL_INTERVAL_MILLSECONDS = 1000 // 探测间隔
)

var (
	once    sync.Once
	checker *TKEMonitorHealthChecker
)

func init() {
	healthprovider.RegisterComponentHealthFactory(
		ProviderName,
		func(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
			return initTKEMonitorHealthChecker(ctx)
		},
	)
}

func initTKEMonitorHealthChecker(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
	var err error
	var addr string
	once.Do(func() {
		addr, _ = util.GetConfigValue(TKEMonitorServerAddr)
		checker = &TKEMonitorHealthChecker{name: ProviderName, risk: RiskName, level: healthcheck.RISK_LEVEL_FATAL}
		err = checker.init(addr)
	})
	return checker, err
}

type TKEMonitorHealthChecker struct {
	name   string
	client pb.MonitorClient
	risk   string
	level  string
}

func (t *TKEMonitorHealthChecker) init(addr string) error {
	if addr == "" {
		return fmt.Errorf("tke-monitor-server addr is invalid")
	}
	// Set up a connection to the server.
	conn, err := grpc.NewClient(addr, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Fatalf("did not connect: %v", err)
		return err
	}
	t.client = pb.NewMonitorClient(conn)
	return nil
}

// Contact the server and print out its response.
func (t *TKEMonitorHealthChecker) IsHealthy(ctx context.Context, req *healthpb.ComponentHealthyRequest) (*healthpb.ComponentHealthyReply, error) {
	baseLog := fmt.Sprintf("clusterId:%s, appName:%s", req.ClusterId, req.ComponentName)
	klog.Infof("run apiserver dial test, %s", baseLog)
	ctxNew, cancel := context.WithTimeout(ctx, time.Minute)
	defer cancel()

	rsp := &healthpb.ComponentHealthyReply{
		HealthCheckName: t.name,
		RiskName:        t.risk,
		Level:           t.level,
	}
	dailResult, err := t.DialTestApiserver(ctxNew, req.ClusterId, req.Product)
	if err != nil {
		errMsg := fmt.Sprintf("failed to dial test for apiserver, err info is %v", err)
		klog.Errorf("%s, %s", errMsg, baseLog)
		metrics.ComponentDialTestErrorTotal.WithLabelValues(req.ClusterId, req.Product, req.ComponentName).Add(1)
		rsp.Code = healthcheck.HEALTHCHECK_CODE_ERROR
		rsp.Detail = errMsg
		rsp.ErrMessage = errMsg
		return rsp, nil
	}

	if dailResult.FailedCount > 0 {
		errMsg := fmt.Sprintf("failed to connect apiserver, failed count:%d, %s", dailResult.FailedCount, req.ClusterId)
		klog.Errorf(errMsg)
		metrics.ComponentDialTestFailedTotal.WithLabelValues(req.ClusterId, req.Product, req.ComponentName).Add(1)
		rsp.Code = healthcheck.HEALTHCHECK_CODE_FAILED
		rsp.Detail = errMsg
		rsp.Solution = fmt.Sprintf("please check the network rules or apiserver status")
		return rsp, nil
	}
	klog.Infof("apiserver dial test success, %s", baseLog)

	rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
	return rsp, nil
}

func (t *TKEMonitorHealthChecker) IsReady(ctx context.Context, req *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error) {
	baseLog := fmt.Sprintf("traceId:%s", req.TraceId)
	klog.V(5).Infof("run apiserver dial test, %s", baseLog)
	ctxNew, cancel := context.WithTimeout(ctx, time.Minute)

	defer cancel()

	rsp := &strategyprovider.TaskStrategyReply{
		HealthCheckName: t.name,
		RiskName:        t.risk,
		Level:           t.level,
	}
	dialResult, err := t.DialTestApiserver(ctxNew, req.ClusterId, req.ProductName)
	if err != nil {
		metrics.ComponentDialTestErrorTotal.WithLabelValues(req.ClusterId, req.ProductName, req.Component).Add(1)
		return util.WrapSingleResult(util.HandleError(rsp, "failed to dial test for apiserver", err, baseLog))
	}

	if dialResult.FailedCount > 0 {
		metrics.ComponentDialTestFailedTotal.WithLabelValues(req.ClusterId, req.ProductName, req.Component).Add(1)

		errMsg := fmt.Sprintf("failed to connect apiserver, failed count:%d, %s", dialResult.FailedCount, baseLog)
		solution := fmt.Sprintf("please check the network rules or apiserver status")
		return util.WrapSingleResult(util.HandleFailed(rsp, errMsg, solution, baseLog))
	}
	klog.V(5).Infof("apiserver dial test success, %s", baseLog)

	rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
	return util.WrapSingleResult(rsp, nil)
}

// 连接apiserver测试
func (t *TKEMonitorHealthChecker) DialTestApiserver(ctx context.Context, ClusterId, Product string) (*pb.DialTestResponse, error) {
	request := &pb.DialTestRequest{
		ClusterId:     ClusterId,
		TotalCount:    DAIL_TOTAL_COUNT,
		Interval:      DAIL_INTERVAL_MILLSECONDS,
		SingleTimeout: DAIL_TIMEOUT_MILLSECONDS,
		Object:        pb.Object_apiserver,
		Product:       pb.Product_tke,
	}
	if Product == util.ProductEKS {
		request.Product = pb.Product_eks
	}

	return t.client.DialTest(ctx, request)
}
