/*
masterCRD 检查

功能：
检查master是否ready
*/
package mastercrdready

import (
	"context"
	"fmt"
	"sync"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog/v2"

	healthpb "git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/healthprovider"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/kmetis/starship/pkg/util/kubeclient"
)

const (
	ProviderName = "mastercrdready"
	RiskName     = "master is not ready"
)

var (
	once    sync.Once
	checker *TKEMasterCRDChecker
)

func init() {
	healthprovider.RegisterComponentHealthFactory(
		ProviderName,
		func(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
			return initTKEMasterCRDChecker(ctx)
		},
	)
}

func initTKEMasterCRDChecker(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
	var err error
	once.Do(func() {
		checker = &TKEMasterCRDChecker{name: ProviderName, risk: RiskName, level: healthcheck.RISK_LEVEL_FATAL}
		err = checker.init()
	})
	return checker, err
}

type TKEMasterCRDChecker struct {
	name  string
	risk  string
	level string
}

func (t *TKEMasterCRDChecker) init() error {
	klog.Infof("init TKEMasterCRDChecker success")
	return nil
}

func (t *TKEMasterCRDChecker) IsHealthy(ctx context.Context, request *healthpb.ComponentHealthyRequest) (*healthpb.ComponentHealthyReply, error) {
	klog.Infof("request info is %v", request)
	return &healthpb.ComponentHealthyReply{}, nil
}

func (t *TKEMasterCRDChecker) IsReady(ctx context.Context, request *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error) {
	baseLog := fmt.Sprintf("traceId:%s", request.TraceId)
	klog.V(5).Infof("run mastercrd ready check, %s", baseLog)
	clusterId := request.ClusterId
	rsp := &strategyprovider.TaskStrategyReply{
		HealthCheckName: t.name,
		RiskName:        t.risk,
		Level:           t.level,
	}

	// 获取master的client
	masterClient, err := kubeclient.GetTargetMetaClient(request)
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get k8s client", err, baseLog))
	}

	// 判断master是否ready
	for count := 1; count <= 10; count++ {
		master, err := masterClient.MasterV1alpha1().Masters(clusterId).Get(ctx, clusterId, metav1.GetOptions{})
		if err != nil {
			klog.Errorf("failed to get master %s in cluster, err:%s, %s", clusterId, err, baseLog)
			return nil, err
		}
		if master.Status.Phase == "running" {
			klog.Infof("mastercrd ready check pass, %s", baseLog)
			rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS // 在上报时，如果出错，则上报为ERROR
			return util.WrapSingleResult(rsp, nil)
		}
		klog.V(5).Infof("mastercrd not ready, current round:%d, %s", count, baseLog)

		backoff := time.Duration(count*count) * 6 * time.Second
		if backoff > 75*time.Second {
			backoff = 75 * time.Second
		}
		time.Sleep(backoff)
	}
	solution := fmt.Sprintf("please check if the master status is running")
	return util.WrapSingleResult(util.HandleFailed(rsp, "mastercrd ready check failed", solution, baseLog))
}
