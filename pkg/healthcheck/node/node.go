package node

import (
	"context"
	"fmt"
	"log"
	"sync"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog/v2"

	nodeservice "git.woa.com/k8s/node-service/pkg/grpc/service"
	healthpb "git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/healthprovider"
	"git.woa.com/kmetis/starship/pkg/metrics"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/kmetis/starship/pkg/util/kubeclient"
)

const (
	ProviderName            = "node"
	TKENodeHealthServerAddr = "TKE_NODE_HEALTH_GRPC_SERVER_ADDR"
	RiskName                = "found unhealthy or evicted node"
)

var (
	once    sync.Once
	checker *TKENodeHealthChecker
)

func init() {
	healthprovider.RegisterComponentHealthFactory(
		ProviderName,
		func(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
			return initNodeHealthChecker(ctx)
		},
	)
}

func initNodeHealthChecker(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
	var err error
	once.Do(func() {
		addr, _ := util.GetConfigValue(TKENodeHealthServerAddr)
		checker = &TKENodeHealthChecker{
			name:  ProviderName,
			risk:  RiskName,
			level: healthcheck.RISK_LEVEL_FATAL, // 风险级别
		}
		err = checker.init(addr)
	})
	return checker, err
}

type TKENodeHealthChecker struct {
	name   string
	client nodeservice.NodeServiceClient
	risk   string
	level  string
}

func GetNodeChecker() *TKENodeHealthChecker {
	return checker
}

func (t *TKENodeHealthChecker) init(addr string) error {
	if addr == "" {
		return fmt.Errorf("tke-node-health addr is invalid")
	}
	// Set up a connection to the server.
	conn, err := grpc.NewClient(addr, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		log.Fatalf("did not connect: %v", err)
		return err
	}
	t.client = nodeservice.NewNodeServiceClient(conn)
	return nil
}

func (t *TKENodeHealthChecker) IsNodeHealthy(ctx context.Context, req *healthpb.NodeHealthyRequest) (*healthpb.NodeHealthyReply, error) {
	ctxNew, cancel := context.WithTimeout(ctx, 15*time.Second)
	defer cancel()

	timestamp := req.GetTimestamp()
	request := &nodeservice.HealthRequest{
		ClusterId: req.GetClusterId(),
		Nodes:     req.GetNodes(),
		Timestamp: &timestamp, // 操作前的时间戳，操作完成后，等段时间发起node的健康检查

		// 下面字段后端暂未使用
		Product: req.GetProduct(),
		Region:  req.GetRegion(),
		//CheckPolicies: req.GetPolicyName(),
	}
	rsp := &healthpb.NodeHealthyReply{
		HealthCheckName: t.name,
		RiskName:        t.risk,
		Level:           t.level,
	}

	resp, err := t.client.CheckHealth(ctxNew, request)
	if err != nil {
		return t.handleHealthCheckError(request.ClusterId, req.Product, err, "", rsp)
	} else if resp.Error != "" {
		return t.handleHealthCheckError(request.ClusterId, req.Product, nil, resp.Error, rsp)
	}

	// 处理健康检查结果
	reply, hasUnhealthy := t.processHealthCheckResults(resp.Results)
	if hasUnhealthy {
		metrics.ClusterNodesCheckFailedTotal.WithLabelValues(request.ClusterId, req.Product).Add(1)
		rsp.Code = healthcheck.HEALTHCHECK_CODE_FAILED
		rsp.Detail = fmt.Sprintf("found unhealthy or evicted node, unhealthy nodes:%d, evicted nodes:%d, clusterId:%s",
			len(reply.UnhealthyNodes), len(reply.EvictionNodes), req.GetClusterId())
		rsp.Solution = fmt.Sprintf("please check the node status")
		return rsp, nil
	}
	klog.Infof("request info is %v, response is %v", request, resp)

	rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
	return rsp, nil
}

func (t *TKENodeHealthChecker) IsHealthy(ctx context.Context, req *healthpb.ComponentHealthyRequest) (*healthpb.ComponentHealthyReply, error) {
	return nil, nil
}

func (t *TKENodeHealthChecker) IsReady(ctx context.Context, request *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error) {
	baseLog := fmt.Sprintf("traceId:%s", request.TraceId)
	klog.Infof("running node health check for cluster %s, %s", request.ClusterId, baseLog)
	rsp := &strategyprovider.TaskStrategyReply{
		HealthCheckName: t.name,
		RiskName:        t.risk,
		Level:           t.level,
	}
	// 注：这里检查用户集群的node状态
	client, err := kubeclient.GetK8sClient(request.ClusterId, request.IanvsToken, request.User, request.ProductName)
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get k8s client", err, baseLog))
	}
	nodes, err := client.CoreV1().Nodes().List(ctx, metav1.ListOptions{ResourceVersion: "0"})
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to list nodes", err, baseLog))
	}
	var notReady []string
	for _, node := range nodes.Items {
		if len(node.Status.Conditions) > 0 && node.Status.Conditions[0].Status == "False" {
			notReady = append(notReady, node.Name)
		}
	}
	if len(notReady) > 0 {
		msg := fmt.Sprintf("found %d nodes are not ready, nodes:%v", len(notReady), notReady)
		solution := fmt.Sprintf("please check the node status")
		return util.WrapSingleResult(util.HandleFailed(rsp, msg, solution, baseLog))
	}
	rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
	return util.WrapSingleResult(rsp, nil)
}

func (t *TKENodeHealthChecker) handleHealthCheckError(clusterId, product string, err error, respError string, rsp *healthpb.NodeHealthyReply) (*healthpb.NodeHealthyReply, error) {
	metrics.ClusterNodesCheckErrorTotal.WithLabelValues(clusterId, product).Add(1)

	var errmsg string
	if err != nil {
		errmsg = fmt.Sprintf("failed to check %s's nodes health: [%v]", clusterId, err)
		rsp.Code = healthcheck.HEALTHCHECK_CODE_ERROR
	} else {
		errmsg = fmt.Sprintf("failed to check %s's nodes health: [%s]", clusterId, respError)
		rsp.Code = healthcheck.HEALTHCHECK_CODE_FAILED
	}

	klog.Errorf(errmsg)
	rsp.Detail = errmsg
	rsp.Solution = "please check the node status"
	return rsp, nil
}

func (t *TKENodeHealthChecker) processHealthCheckResults(results []*nodeservice.NodeResult) (healthpb.NodeHealthyReply, bool) {
	reply := healthpb.NodeHealthyReply{}
	hasUnhealthy := false

	for _, result := range results {
		if !result.Healthy {
			reply.UnhealthyNodes = append(reply.UnhealthyNodes, result.Node)
			hasUnhealthy = true
		}
		if len(result.Events) > 0 {
			reply.EvictionNodes = append(reply.EvictionNodes, result.Node)
			hasUnhealthy = true
		}
	}

	return reply, hasUnhealthy
}
