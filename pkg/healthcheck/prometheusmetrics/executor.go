package prometheusmetrics

import (
	"fmt"
	"strings"

	"k8s.io/klog/v2"

	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
)

// PrometheusQueryExecutor 通用的Prometheus查询执行器
type PrometheusQueryExecutor struct{}

// ExecuteQuery 执行单个查询并返回结果
func (e *PrometheusQueryExecutor) ExecuteQuery(queryConfig util.PrometheusQueryConfig, clusterId, region, namespace, component string, baseLog string) *strategyprovider.TaskStrategyReply {
	// 检查是否配置了datasource
	if queryConfig.Datasource == "" {
		rsp := createQueryReply(queryConfig.Name, healthcheck.RISK_LEVEL_FATAL)
		reply, _ := util.HandleError(rsp, fmt.Sprintf("query %s: datasource is required", queryConfig.Name), fmt.Errorf("datasource field is missing in configuration"), baseLog)
		return reply
	}

	// 将长域名转换为短域名
	shortRegion := util.LongToShort(region)

	// 替换查询中的变量
	// TODO：需完善更多变量替换
	processedQuery := queryConfig.Query
	processedQuery = strings.ReplaceAll(processedQuery, "${clusterId}", clusterId)
	processedQuery = strings.ReplaceAll(processedQuery, "${region}", shortRegion)
	processedQuery = strings.ReplaceAll(processedQuery, "${namespace}", namespace)
	processedQuery = strings.ReplaceAll(processedQuery, "${component}", component)

	// 如果查询中包含未替换的变量占位符，记录警告
	if strings.Contains(processedQuery, "${") && strings.Contains(processedQuery, "}") {
		klog.Warningf("query %s may contain unresolved variables: %s, %s", queryConfig.Name, processedQuery, baseLog)
	}

	// 记录最终使用的查询
	klog.Infof("using prometheus query %s: %s, %s", queryConfig.Name, processedQuery, baseLog)

	result, err := util.QueryPrometheusByDatasource(queryConfig.Datasource, processedQuery)

	if err != nil {
		rsp := createQueryReply(queryConfig.Name, healthcheck.RISK_LEVEL_FATAL)
		reply, _ := util.HandleError(rsp, fmt.Sprintf("query %s failed", queryConfig.Name), err, baseLog)
		return reply
	}

	// 检查是否有结果
	if len(result.Data.Result) == 0 {
		rsp := createQueryReply(queryConfig.Name, healthcheck.RISK_LEVEL_WARNING)
		rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
		rsp.Detail = fmt.Sprintf("no data returned from prometheus query %s", queryConfig.Name)
		klog.Warningf("no data returned from prometheus query %s, %s", queryConfig.Name, baseLog)
		return rsp
	}

	// 处理当前查询的所有结果
	return processPrometheusResults(queryConfig.Name, result.Data.Result, queryConfig.Operator, queryConfig.Threshold, baseLog)
}

// processPrometheusResults 处理Prometheus查询结果并进行阈值检查
func processPrometheusResults(queryName string, results []struct {
	Metric map[string]string `json:"metric"`
	Value  []any             `json:"value"`
}, operator string, threshold float64, baseLog string) *strategyprovider.TaskStrategyReply {

	for _, res := range results {
		// 获取值
		valueInterface := res.Value[1]
		valueStr, ok := valueInterface.(string)
		if !ok {
			klog.Warningf("failed to convert prometheus value to string for query %s result: %v, %s", queryName, valueInterface, baseLog)
			continue
		}

		// 转换为浮点数
		var value float64
		if _, err := fmt.Sscanf(valueStr, "%f", &value); err != nil {
			klog.Warningf("failed to parse prometheus value for query %s result: %v, %s", queryName, err, baseLog)
			continue
		}

		// 比较值和阈值
		checkPassed, err := util.CompareNumericValue(value, operator, threshold)
		if err != nil {
			rsp := createQueryReply(queryName, healthcheck.RISK_LEVEL_FATAL)
			result, _ := util.HandleError(rsp, fmt.Sprintf("query %s comparison failed", queryName), err, baseLog)
			return result
		}

		// 如果检查不通过，返回失败
		if !checkPassed {
			rsp := createQueryReply(queryName, healthcheck.RISK_LEVEL_FATAL)
			errMsg := fmt.Sprintf("Query %s failed threshold check: value %f %s %f", queryName, value, operator, threshold)
			solution := fmt.Sprintf("Please check if the metric values for %s are normal", queryName)
			result, _ := util.HandleFailed(rsp, errMsg, solution, baseLog)
			return result
		}

		klog.Infof("prometheus check passed for query %s result: %f %s %f, %s", queryName, value, operator, threshold, baseLog)
	}

	// 所有检查都通过
	detail := fmt.Sprintf("All %d prometheus metrics passed threshold check", len(results))
	klog.Infof("%s, %s", detail, baseLog)
	return &strategyprovider.TaskStrategyReply{
		HealthCheckName: queryName,
		RiskName:        queryName,
		Level:           healthcheck.RISK_LEVEL_FATAL,
		Code:            healthcheck.HEALTHCHECK_CODE_PASS,
		Detail:          detail,
	}
}

// createQueryReply 创建查询响应的基础结构
func createQueryReply(queryName string, level string) *strategyprovider.TaskStrategyReply {
	return &strategyprovider.TaskStrategyReply{
		HealthCheckName: queryName,
		RiskName:        queryName,
		Level:           level,
	}
}
