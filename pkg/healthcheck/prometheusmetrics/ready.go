/**
Prometheus指标健康检查
支持多查询配置，单次调用返回多个查询结果
*/

package prometheusmetrics

import (
	"context"
	"fmt"
	"sync"

	"k8s.io/klog/v2"

	healthpb "git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/healthprovider"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
)

const (
	ProviderName = "metricsCheck"
	RiskName     = "Prometheus指标检查"
)

var (
	once    sync.Once
	checker *PrometheusMetricsChecker
)

func init() {
	healthprovider.RegisterComponentHealthFactory(
		ProviderName,
		func(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
			return initPrometheusMetricsChecker(ctx)
		},
	)
}

func initPrometheusMetricsChecker(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
	var err error
	once.Do(func() {
		checker = &PrometheusMetricsChecker{name: ProviderName, risk: RiskName, level: healthcheck.RISK_LEVEL_FATAL}
		err = checker.init()
	})
	return checker, err
}

// PrometheusMetricsChecker 主检查器，执行所有Prometheus查询
type PrometheusMetricsChecker struct {
	name  string
	risk  string
	level string
}

func (t *PrometheusMetricsChecker) init() error {
	klog.Infof("init PrometheusMetricsChecker success")
	return nil
}

func (t *PrometheusMetricsChecker) IsHealthy(ctx context.Context, request *healthpb.ComponentHealthyRequest) (*healthpb.ComponentHealthyReply, error) {
	klog.Infof("request info is %v", request)
	return &healthpb.ComponentHealthyReply{}, nil
}

// IsReady 执行所有Prometheus查询并返回多个结果
func (t *PrometheusMetricsChecker) IsReady(ctx context.Context, request *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error) {
	baseLog := fmt.Sprintf("traceId:%s", request.TraceId)
	klog.Infof("run prometheus metrics check, %s", baseLog)

	// 解析查询配置
	queryConfigs, err := t.parseQueryConfigs(request)
	if err != nil {
		// 如果解析配置失败，返回单个错误结果
		errorResult := &strategyprovider.TaskStrategyReply{
			HealthCheckName: t.name,
			RiskName:        t.risk,
			Level:           healthcheck.RISK_LEVEL_FATAL,
			Code:            healthcheck.HEALTHCHECK_CODE_ERROR,
			Detail:          fmt.Sprintf("failed to parse query configs: %v", err),
		}
		return []*strategyprovider.TaskStrategyReply{errorResult}, nil
	}

	if len(queryConfigs) == 0 {
		// 如果没有查询配置，返回成功结果
		successResult := &strategyprovider.TaskStrategyReply{
			HealthCheckName: t.name,
			RiskName:        t.risk,
			Level:           healthcheck.RISK_LEVEL_WARNING,
			Code:            healthcheck.HEALTHCHECK_CODE_PASS,
			Detail:          "No prometheus queries configured",
		}
		return []*strategyprovider.TaskStrategyReply{successResult}, nil
	}

	// 执行所有查询并收集结果
	var results []*strategyprovider.TaskStrategyReply
	executor := &PrometheusQueryExecutor{}

	for _, queryConfig := range queryConfigs {
		klog.Infof("executing prometheus query: %s, %s", queryConfig.Name, baseLog)

		// 执行单个查询
		result := executor.ExecuteQuery(queryConfig, request.ClusterId, request.Region, request.Namespace, request.Component, baseLog)

		result.HealthCheckName = fmt.Sprintf("%s-%s", t.name, queryConfig.Name)
		result.RiskName = fmt.Sprintf("%s-%s", t.risk, queryConfig.Name)

		results = append(results, result)
	}

	klog.Infof("completed %d prometheus queries, %s", len(results), baseLog)
	return results, nil
}

// parseQueryConfigs 解析查询配置
func (t *PrometheusMetricsChecker) parseQueryConfigs(request *strategyprovider.TaskStrategyRequest) ([]util.PrometheusQueryConfig, error) {
	// 获取健康检查规则
	rule := request.GetHealthCheckRule(ProviderName)
	if rule == nil {
		return nil, fmt.Errorf("failed to get prometheus health check rule")
	}

	// 从规则中获取Prometheus配置
	prometheusConfig := rule.Value["prometheusConfig"]
	if prometheusConfig == nil {
		return nil, fmt.Errorf("failed to get prometheus config from rule")
	}

	return util.ParsePrometheusQueryConfigs(prometheusConfig)
}
