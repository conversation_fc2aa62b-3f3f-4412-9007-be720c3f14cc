package tcrcertcheck

import (
	"context"
	"fmt"
	"strings"
	"sync"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/kubernetes"
	restclient "k8s.io/client-go/rest"
	"k8s.io/klog/v2"

	healthpb "git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/healthprovider"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/kmetis/starship/pkg/util/kubeclient"
)

const (
	ProviderName = "tcrCertExpiredCheck"
	RiskName     = "tcr证书过期时间检查"
)

var (
	once    sync.Once
	checker *TcrCertExpiredChecker
)

func init() {
	healthprovider.RegisterComponentHealthFactory(
		ProviderName,
		func(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
			return initTcrCertExpiredChecker(ctx)
		},
	)
}

func initTcrCertExpiredChecker(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
	var err error
	once.Do(func() {
		checker = &TcrCertExpiredChecker{name: ProviderName, risk: RiskName, level: healthcheck.RISK_LEVEL_FATAL}
		err = checker.init()
	})
	return checker, err
}

type TcrCertExpiredChecker struct {
	name  string
	risk  string
	level string
}

func (t *TcrCertExpiredChecker) init() error {
	klog.Infof("init TcrCertExpiredChecker success")
	return nil
}

func (t *TcrCertExpiredChecker) IsHealthy(ctx context.Context, request *healthpb.ComponentHealthyRequest) (*healthpb.ComponentHealthyReply, error) {
	klog.Infof("request info is %v", request)
	return &healthpb.ComponentHealthyReply{}, nil
}

func (t *TcrCertExpiredChecker) IsReady(ctx context.Context, request *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error) {
	baseLog := fmt.Sprintf("traceId:%s", request.TraceId)
	klog.Infof("run tcr cert expired check, %s", baseLog)

	config, err := kubeclient.GetRestConfig(request.ClusterId, request.ProductName)
	if err != nil {
		errMsg := fmt.Sprintf("failed to get k8s rest client, err:%v", err)
		klog.Errorf("%s, %s", errMsg, baseLog)
		return util.WrapSingleResult(&strategyprovider.TaskStrategyReply{
			HealthCheckName: t.name,
			RiskName:        t.risk,
			Level:           t.level,
			Code:            healthcheck.HEALTHCHECK_CODE_ERROR,
			Detail:          errMsg,
		}, nil)
	}

	client, err := kubernetes.NewForConfig(config)
	if err != nil {
		errMsg := fmt.Sprintf("failed to get k8s client, err:%v", err)
		klog.Errorf("%s, %s", errMsg, baseLog)
		return util.WrapSingleResult(&strategyprovider.TaskStrategyReply{
			HealthCheckName: t.name,
			RiskName:        t.risk,
			Level:           t.level,
			Code:            healthcheck.HEALTHCHECK_CODE_ERROR,
			Detail:          errMsg,
		}, nil)
	}

	expired := request.TcrPrecheckExpiredTime
	if request.Action == util.TaskActionPostCheck {
		expired = request.TcrPostcheckExpiredTime
	}

	if err = checkDeployPod(config, client, request.Extend.Namespace, expired, request.TraceId); err != nil {
		errMsg := fmt.Sprintf("checkDeployPod failed err:%v", err)
		klog.Errorf("%s, %s", errMsg, baseLog)
		return util.WrapSingleResult(&strategyprovider.TaskStrategyReply{
			HealthCheckName: t.name,
			RiskName:        t.risk,
			Level:           t.level,
			Code:            healthcheck.HEALTHCHECK_CODE_FAILED,
			Detail:          errMsg,
		}, nil)
	}

	return util.WrapSingleResult(&strategyprovider.TaskStrategyReply{
		Code:            healthcheck.HEALTHCHECK_CODE_PASS,
		HealthCheckName: t.name,
		RiskName:        t.risk,
		Level:           t.level,
	}, nil)
}

func checkDeployPod(config *restclient.Config, client kubernetes.Interface, namespace, expired, traceId string) error {
	labelSet := map[string]string{
		"component":     "nginx",
		"release":       "tcr",
		"tcr-component": "tcr-nginx",
	}
	selectorStr := labels.SelectorFromSet(labelSet).String()
	pods, err := client.CoreV1().Pods(namespace).List(context.TODO(), metav1.ListOptions{
		ResourceVersion: "0",
		LabelSelector:   selectorStr,
	})
	if err != nil {
		return fmt.Errorf("failed to list pods: %v", err)
	}
	if len(pods.Items) == 0 {
		return fmt.Errorf("no pods found")
	}

	for _, pod := range pods.Items {
		// 如果pod处于terminating状态，则不检查
		if pod.DeletionTimestamp != nil {
			continue
		}
		if pod.Status.Phase != corev1.PodRunning {
			return fmt.Errorf("pod %s/%s is not running yet: %v", pod.Namespace, pod.Name, pod.Status.Phase)
		}

		if stdstring, _, err := util.ExecInPod(config, client, namespace, pod.Name, "nginx",
			"openssl s_client -connect 127.0.0.1:8443 | openssl x509 -noout -dates"); err != nil {
			return fmt.Errorf("check in pod %s/%s failed: %v", pod.Namespace, pod.Name, err)
		} else {
			if !strings.Contains(stdstring, expired) {
				return fmt.Errorf("check in pod %s/%s failed: %s", pod.Namespace, pod.Name, stdstring)
			}
		}
	}

	klog.Infof("check tcr cert expired for %d pods pass: %s", len(pods.Items), traceId)
	return nil
}
