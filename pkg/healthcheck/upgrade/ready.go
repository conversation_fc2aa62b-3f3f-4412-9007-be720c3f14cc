/*
镜像升级检查

功能：
1. 拦截主版本升级
2. 拦截次版本升级

该升级策略默认开启，可以通过在七彩石配置healthchecks.rules禁用
*/
package upgrade

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"sync"

	"k8s.io/klog/v2"

	healthpb "git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/healthprovider"
	"git.woa.com/kmetis/starship/pkg/model"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/kmetis/starship/pkg/util/kubeclient"
)

const (
	ProviderName = "crossVersionUpgrade"
	RiskName     = "禁止镜像跨版本升级"

	// 镜像升级特性开关
	SWITCH_ENABLE                = "enable"
	FORBID_MAJOR_VERSION_UPGRADE = "ForbidMajorVersionUpgrade"
	FORBID_MINOR_VERSION_UPGRADE = "ForbidMinorVersionUpgrade"
)

var (
	once    sync.Once
	checker *TKEUpgradeChecker
)

func init() {
	healthprovider.RegisterComponentHealthFactory(
		ProviderName,
		func(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
			return initTKEUpgradeChecker(ctx)
		},
	)
}

func initTKEUpgradeChecker(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
	var err error
	once.Do(func() {
		checker = &TKEUpgradeChecker{name: ProviderName, risk: RiskName, level: healthcheck.RISK_LEVEL_FATAL}
		err = checker.init()
	})
	return checker, err
}

type TKEUpgradeChecker struct {
	name  string
	risk  string
	level string
}

func (t *TKEUpgradeChecker) init() error {
	klog.Infof("init TKEUpgradeChecker success")
	return nil
}

func (t *TKEUpgradeChecker) IsHealthy(ctx context.Context, request *healthpb.ComponentHealthyRequest) (*healthpb.ComponentHealthyReply, error) {
	klog.Infof("request info is %v", request)
	return &healthpb.ComponentHealthyReply{}, nil
}

func (t *TKEUpgradeChecker) IsReady(ctx context.Context, request *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error) {
	baseLog := fmt.Sprintf("traceId:%s", request.TraceId)
	klog.V(5).Infof("run cross-version upgrade check, %s", baseLog)

	rsp := &strategyprovider.TaskStrategyReply{
		HealthCheckName: t.name,
		RiskName:        t.risk,
		Level:           t.level,
	}
	client, err := kubeclient.GetTargetK8sClient(request)
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get k8s client", err, baseLog))
	}
	clusterId := request.ClusterId
	namespace := util.GetComponentNamespace(clusterId, request.Namespace)
	workloadName := util.GetWorkloadName(clusterId, request.WorkloadName)

	// 获取workload的镜像信息
	imageList, err := util.GetWorkloadImage(client, request.WorkloadType, namespace, workloadName, request.Extend)
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to get workload imageList", err, baseLog))
	}

	// 从七彩石策略读取repo白名单，并进行匹配
	// NOTICE: 如果指定了要进行镜像检查，七彩石必须配置downgrade策略，否则会报错
	if request.HealthCheckRules == nil {
		return util.WrapSingleResult(util.HandleError(rsp, "health check rule not exist", nil, baseLog))
	}

	upgradeRule := request.GetHealthCheckRule(ProviderName)
	// upgrade信息如下：
	// ForbidMajorVersionUpgrade: enable
	// ForbidMinorVersionUpgrade: enable
	if upgradeRule == nil {
		return util.WrapSingleResult(util.HandleError(rsp, "cross-version upgrade rule is empty", nil, baseLog))
	}
	upgradeRuleJson, err := json.Marshal(upgradeRule)
	if err != nil {
		return util.WrapSingleResult(util.HandleError(rsp, "failed to marshal upgrade rule", err, baseLog))
	}

	newImageTag := request.ImageTag
	if newImageTag == "" {
		rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
		return util.WrapSingleResult(rsp, nil)
	}
	for _, imageStr := range imageList {
		currentImageTag, err := util.GetImageTag(imageStr)
		if err != nil {
			return util.WrapSingleResult(util.HandleError(rsp, "failed to get image tag", err, baseLog))
		}

		klog.Infof("current image tag:%s, new image tag:%s, upgrade rule:%v, %s", currentImageTag, newImageTag, upgradeRuleJson, baseLog)
		// 检查镜像版本
		allow, err := allowUpgrade(upgradeRule, currentImageTag, newImageTag)
		if err != nil {
			return util.WrapSingleResult(util.HandleError(rsp, "failed to check tag version", err, baseLog))
		}

		if !allow {
			return util.WrapSingleResult(util.HandleFailed(rsp,
				fmt.Sprintf("cross-version upgrade is not allowed, current image tag:%s", currentImageTag),
				fmt.Sprintf("image upgrade check rule:%s", upgradeRule), baseLog))
		}
	}

	klog.V(5).Infof("cross-version upgrade check pass, %s", baseLog)
	rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
	return util.WrapSingleResult(rsp, nil)
}

// 通过镜像Tag判断是否允许升级
func allowUpgrade(rule *model.HealthCheckRule, oldImageTag, newImageTag string) (bool, error) {
	oldTagVersion, err := util.GetVersionFromImageTag(oldImageTag)
	if err != nil {
		klog.Errorf("failed to get version from image tag, err:%v", err)
		return false, err
	}
	newTagVersion, err := util.GetVersionFromImageTag(newImageTag)
	if err != nil {
		klog.Errorf("failed to get version from image tag, err:%v", err)
		return false, err
	}

	// 检查主版本是否有升级
	upgradeMajorSwitch, err := rule.GetString(FORBID_MAJOR_VERSION_UPGRADE)
	if err != nil {
		return false, err
	}
	if strings.TrimSpace(upgradeMajorSwitch) == SWITCH_ENABLE {
		klog.V(5).Infof("major check, old major:%d, new major:%d", oldTagVersion.MajorVersion, newTagVersion.MajorVersion)
		if oldTagVersion.MajorVersion < newTagVersion.MajorVersion {
			return false, nil
		}
	}

	// 检查次版本是否有升级
	upgradeMinorSwitch, err := rule.GetString(FORBID_MINOR_VERSION_UPGRADE)
	if err != nil {
		return false, err
	}
	if strings.TrimSpace(upgradeMinorSwitch) == SWITCH_ENABLE {
		klog.V(5).Infof("minor check, old minor:%d, new minor:%d", oldTagVersion.MinorVersion, newTagVersion.MinorVersion)
		if oldTagVersion.MajorVersion == newTagVersion.MajorVersion && oldTagVersion.MinorVersion < newTagVersion.MinorVersion {
			return false, nil
		}
	}

	return true, nil
}
