package upgrade

// 给(t *TKEUpgradeChecker) IsReady 生成测试用例
import (
	"testing"

	"github.com/stretchr/testify/assert"

	"git.woa.com/kmetis/starship/pkg/model"
)

func TestAllowUpgrade(t *testing.T) {
	rule := &model.HealthCheckRule{
		Name: ProviderName,
		Value: map[string]interface{}{
			FORBID_MAJOR_VERSION_UPGRADE: "enable",
			FORBID_MINOR_VERSION_UPGRADE: "enable",
		},
	}

	tests := []struct {
		oldImageTag   string
		newImageTag   string
		expected      bool
		expectedError error
	}{
		{"v1.0.0", "v2.0.0", false, nil}, // 禁止主版本升级
		{"v1.0.0", "v1.1.0", false, nil}, // 禁止次版本升级
		{"v1.0.0", "v1.0.1", true, nil},  // 允许patch版本升级
		{"v1.0.0", "v1.0.0", true, nil},  // 版本相同
	}

	for _, tt := range tests {
		t.Run(tt.oldImageTag+"->"+tt.newImageTag, func(t *testing.T) {
			result, err := allowUpgrade(rule, tt.oldImageTag, tt.newImageTag)
			assert.Equal(t, tt.expected, result)
			assert.Equal(t, tt.expectedError, err)
		})
	}

	// rule 只禁止主版本升级
	rule = &model.HealthCheckRule{
		Name: ProviderName,
		Value: map[string]interface{}{
			FORBID_MAJOR_VERSION_UPGRADE: "enable",
			FORBID_MINOR_VERSION_UPGRADE: "disable",
		},
	}
	tests = []struct {
		oldImageTag   string
		newImageTag   string
		expected      bool
		expectedError error
	}{
		{"v1.0.0", "v1.0.1", true, nil},  // 允许升级Patch版本
		{"v1.0.0", "v1.1.0", true, nil},  // 允许次版本升级
		{"v1.0.0", "v2.0.0", false, nil}, // 禁止升级主版本
	}
	for _, tt := range tests {
		t.Run(tt.oldImageTag+"->"+tt.newImageTag, func(t *testing.T) {
			result, err := allowUpgrade(rule, tt.oldImageTag, tt.newImageTag)
			assert.Equal(t, tt.expected, result)
			assert.Equal(t, tt.expectedError, err)
		})
	}

	// ruleMap 只禁止次版本升级
	rule = &model.HealthCheckRule{
		Name: ProviderName,
		Value: map[string]interface{}{
			FORBID_MAJOR_VERSION_UPGRADE: "disable",
			FORBID_MINOR_VERSION_UPGRADE: "enable",
		},
	}

	tests = []struct {
		oldImageTag   string
		newImageTag   string
		expected      bool
		expectedError error
	}{
		{"v1.0.0", "v1.0.1", true, nil},  // 允许升级Patch版本
		{"v1.0.0", "v1.1.0", false, nil}, // 禁止次版本升级
		{"v1.0.0", "v2.0.0", true, nil},  // 升级主版本
	}
	for _, tt := range tests {
		t.Run(tt.oldImageTag+"->"+tt.newImageTag, func(t *testing.T) {
			result, err := allowUpgrade(rule, tt.oldImageTag, tt.newImageTag)
			assert.Equal(t, tt.expected, result)
			assert.Equal(t, tt.expectedError, err)
		})
	}
}
