package upgradejob

import (
	"context"
	"fmt"
	"strconv"
	"sync"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/klog/v2"
	"tkestack.io/tke/api/client/clientset/versioned"

	healthpb "git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/clientset/application"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/healthprovider"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
)

const (
	ProviderName = "upgradeJob"
	RiskName     = "upgradeJob健康检查"

	// 默认配置值
	defaultMaxRetries    = 20
	defaultRetryInterval = 30 * time.Second
)

// UpgradeJobHealthCheckConfig upgradeJob健康检查配置
type UpgradeJobHealthCheckConfig struct {
	// MaxRetries 最大重试次数
	MaxRetries int
	// RetryInterval 重试间隔时间
	RetryInterval time.Duration
}

var (
	once    sync.Once
	checker *UpgradeJobChecker
)

func init() {
	healthprovider.RegisterComponentHealthFactory(
		ProviderName,
		func(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
			return initUpgradeJobChecker(ctx)
		},
	)
}

func initUpgradeJobChecker(ctx *healthprovider.ComponentConfig) (healthprovider.HealthCheck, error) {
	var err error
	once.Do(func() {
		checker = &UpgradeJobChecker{name: ProviderName, risk: RiskName, level: healthcheck.RISK_LEVEL_FATAL}
		err = checker.init()
	})
	return checker, err
}

type UpgradeJobChecker struct {
	name  string
	risk  string
	level string
}

func (c *UpgradeJobChecker) init() error {
	klog.Infof("init UpgradeJobChecker success")
	return nil
}

func (c *UpgradeJobChecker) IsHealthy(ctx context.Context, request *healthpb.ComponentHealthyRequest) (*healthpb.ComponentHealthyReply, error) {
	klog.Infof("request info is %v", request)
	return &healthpb.ComponentHealthyReply{}, nil
}

func (c *UpgradeJobChecker) IsReady(ctx context.Context, request *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error) {
	baseLog := fmt.Sprintf("traceId:%s", request.TraceId)
	klog.V(5).Infof("run upgradeJob check, %s", baseLog)

	rsp := &strategyprovider.TaskStrategyReply{
		HealthCheckName: c.name,
		RiskName:        c.risk,
		Level:           c.level,
	}

	var appclient *versioned.Clientset
	if request.ProductName == "tke" {
		appclient = application.GetClientSet().Client
	} else if request.ProductName == "eks" {
		appclient = application.GetClientSet().EksClient
	} else {
		return util.WrapSingleResult(util.HandleError(rsp, "productName is invalid", nil, baseLog))
	}

	// 从rule.Value中解析配置参数
	config := c.parseHealthCheckConfig(request, baseLog)

	// 使用配置驱动的重试逻辑
	return c.checkUpgradeJobWithRetry(ctx, appclient, request, rsp, config, baseLog)
}

// parseHealthCheckConfig 从rule.Value中解析健康检查配置
func (c *UpgradeJobChecker) parseHealthCheckConfig(request *strategyprovider.TaskStrategyRequest, baseLog string) *UpgradeJobHealthCheckConfig {
	// 默认配置
	config := &UpgradeJobHealthCheckConfig{
		MaxRetries:    defaultMaxRetries,
		RetryInterval: defaultRetryInterval,
	}

	// 获取健康检查规则
	rule := request.GetHealthCheckRule(ProviderName)
	if rule == nil {
		klog.V(5).Infof("upgradeJob health check rule not found, using default config, %s", baseLog)
		return config
	}

	// 解析检测次数
	if maxRetries, err := rule.GetValue("maxRetries"); err == nil {
		if retries, ok := maxRetries.(float64); ok && retries > 0 {
			config.MaxRetries = int(retries)
		} else if retries, ok := maxRetries.(int); ok && retries > 0 {
			config.MaxRetries = retries
		}
	}

	// 解析检测间隔时长（秒）
	if retryIntervalSeconds, err := rule.GetValue("retryIntervalSeconds"); err == nil {
		if interval, ok := retryIntervalSeconds.(float64); ok && interval > 0 {
			config.RetryInterval = time.Duration(interval) * time.Second
		} else if interval, ok := retryIntervalSeconds.(int); ok && interval > 0 {
			config.RetryInterval = time.Duration(interval) * time.Second
		}
	}

	klog.V(5).Infof("upgradeJob health check config: maxRetries=%d, retryInterval=%v, %s",
		config.MaxRetries, config.RetryInterval, baseLog)

	return config
}

// checkUpgradeJobWithRetry 使用配置驱动的重试逻辑检查upgradeJob状态
func (c *UpgradeJobChecker) checkUpgradeJobWithRetry(ctx context.Context, appclient *versioned.Clientset, request *strategyprovider.TaskStrategyRequest, rsp *strategyprovider.TaskStrategyReply, config *UpgradeJobHealthCheckConfig, baseLog string) ([]*strategyprovider.TaskStrategyReply, error) {
	for attempt := 1; attempt <= config.MaxRetries; attempt++ {
		klog.V(5).Infof("upgradeJob check attempt %d/%d, %s", attempt, config.MaxRetries, baseLog)

		ujs, err := appclient.ApplicationV1().UpgradeJobs(request.ClusterId).List(ctx, metav1.ListOptions{
			LabelSelector: labels.SelectorFromSet(labels.Set{
				"upgradejob.starship.tkestack.io/taskid":    strconv.Itoa(int(request.TaskId)),
				"upgradejob.starship.tkestack.io/component": request.Component,
			}).String(),
		})
		if err != nil {
			// 如果不是最后一次尝试，则继续重试
			if attempt < config.MaxRetries {
				klog.Warningf("upgradeJob get failed on attempt %d/%d, will retry after %v: %v, %s",
					attempt, config.MaxRetries, config.RetryInterval, err, baseLog)
				time.Sleep(config.RetryInterval)
				continue
			}

			// 最后一次尝试失败
			return util.WrapSingleResult(util.HandleError(rsp, "get upgradeJob failed", err, baseLog))
		}

		if len(ujs.Items) != 1 {
			searchErr := fmt.Errorf("upgradeJob count %d", len(ujs.Items))

			// 如果不是最后一次尝试，则继续重试
			if attempt < config.MaxRetries {
				klog.Warningf("upgradeJob search failed on attempt %d/%d, will retry after %v: %v, %s",
					attempt, config.MaxRetries, config.RetryInterval, searchErr, baseLog)
				time.Sleep(config.RetryInterval)
				continue
			}

			// 最后一次尝试失败
			return util.WrapSingleResult(util.HandleError(rsp, "search upgradeJob failed", searchErr, baseLog))
		}

		uj := ujs.Items[0]

		// 升级失败（立即返回，不重试）
		if uj.Status.Reason != nil {
			return util.WrapSingleResult(util.HandleError(rsp, "upgradeJob failed", fmt.Errorf("%s", *uj.Status.Reason), baseLog))
		}

		// 升级完成
		if uj.Status.BatchCompleteNum > uj.Status.BatchOrder {
			klog.V(5).Infof("upgradeJob check pass on attempt %d, %s", attempt, baseLog)
			rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
			return util.WrapSingleResult(rsp, nil)
		}

		// 升级还未完成，如果不是最后一次尝试，则继续重试
		if attempt < config.MaxRetries {
			klog.V(5).Infof("upgradeJob not completed on attempt %d/%d, will retry after %v, %s",
				attempt, config.MaxRetries, config.RetryInterval, baseLog)
			time.Sleep(config.RetryInterval)
			continue
		}

		// 最后一次尝试仍未完成
		return util.WrapSingleResult(util.HandleError(rsp, "upgradeJob not completed", nil, baseLog))
	}

	// 理论上不会到达这里
	return util.WrapSingleResult(util.HandleError(rsp, "unexpected end of retry loop", nil, baseLog))
}
