package upgradejob

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/model"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
)

func TestParseHealthCheckConfig_DefaultValues(t *testing.T) {
	checker := &UpgradeJobChecker{
		name:  ProviderName,
		risk:  RiskName,
		level: healthcheck.RISK_LEVEL_FATAL,
	}

	// 测试没有规则时的默认配置
	request := &strategyprovider.TaskStrategyRequest{
		TraceId: "test-trace",
	}

	config := checker.parseHealthCheckConfig(request, "test-log")

	assert.Equal(t, defaultMaxRetries, config.MaxRetries)
	assert.Equal(t, defaultRetryInterval, config.RetryInterval)
}

func TestParseHealthCheckConfig_WithRuleValues(t *testing.T) {
	checker := &UpgradeJobChecker{
		name:  ProviderName,
		risk:  RiskName,
		level: healthcheck.RISK_LEVEL_FATAL,
	}

	testCases := []struct {
		name                  string
		ruleValue             map[string]interface{}
		expectedMaxRetries    int
		expectedRetryInterval time.Duration
	}{
		{
			name: "valid config with int values",
			ruleValue: map[string]interface{}{
				"maxRetries":           3,
				"retryIntervalSeconds": 60,
			},
			expectedMaxRetries:    3,
			expectedRetryInterval: 60 * time.Second,
		},
		{
			name: "valid config with float64 values",
			ruleValue: map[string]interface{}{
				"maxRetries":           float64(7),
				"retryIntervalSeconds": float64(45),
			},
			expectedMaxRetries:    7,
			expectedRetryInterval: 45 * time.Second,
		},
		{
			name: "partial config - only maxRetries",
			ruleValue: map[string]interface{}{
				"maxRetries": 8,
				// retryIntervalSeconds 缺失，应使用默认值
			},
			expectedMaxRetries:    8,
			expectedRetryInterval: defaultRetryInterval,
		},
		{
			name: "partial config - only retryIntervalSeconds",
			ruleValue: map[string]interface{}{
				// maxRetries 缺失，应使用默认值
				"retryIntervalSeconds": 90,
			},
			expectedMaxRetries:    defaultMaxRetries,
			expectedRetryInterval: 90 * time.Second,
		},
		{
			name: "invalid values should use defaults",
			ruleValue: map[string]interface{}{
				"maxRetries":           -1,
				"retryIntervalSeconds": 0,
			},
			expectedMaxRetries:    defaultMaxRetries,    // 负值被忽略，使用默认值
			expectedRetryInterval: defaultRetryInterval, // 0值被忽略，使用默认值
		},
		{
			name: "zero maxRetries should use default",
			ruleValue: map[string]interface{}{
				"maxRetries":           0,
				"retryIntervalSeconds": 120,
			},
			expectedMaxRetries:    defaultMaxRetries, // 0值被忽略，使用默认值
			expectedRetryInterval: 120 * time.Second,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 创建带有健康检查规则的请求
			request := &strategyprovider.TaskStrategyRequest{
				TraceId: "test-trace",
				HealthCheckRules: []model.HealthCheckRule{
					{
						Name:  ProviderName,
						Value: tc.ruleValue,
					},
				},
			}

			config := checker.parseHealthCheckConfig(request, "test-log")

			assert.Equal(t, tc.expectedMaxRetries, config.MaxRetries,
				fmt.Sprintf("Expected maxRetries %d, got %d", tc.expectedMaxRetries, config.MaxRetries))
			assert.Equal(t, tc.expectedRetryInterval, config.RetryInterval,
				fmt.Sprintf("Expected retryInterval %v, got %v", tc.expectedRetryInterval, config.RetryInterval))
		})
	}
}

func TestParseHealthCheckConfig_DifferentRuleTypes(t *testing.T) {
	checker := &UpgradeJobChecker{
		name:  ProviderName,
		risk:  RiskName,
		level: healthcheck.RISK_LEVEL_FATAL,
	}

	testCases := []struct {
		name                  string
		rules                 []model.HealthCheckRule
		expectedMaxRetries    int
		expectedRetryInterval time.Duration
	}{
		{
			name: "rule found",
			rules: []model.HealthCheckRule{
				{
					Name: ProviderName,
					Value: map[string]interface{}{
						"maxRetries":           4,
						"retryIntervalSeconds": 25,
					},
				},
			},
			expectedMaxRetries:    4,
			expectedRetryInterval: 25 * time.Second,
		},
		{
			name: "no matching rule",
			rules: []model.HealthCheckRule{
				{
					Name: "otherProvider",
					Value: map[string]interface{}{
						"maxRetries": 10,
					},
				},
			},
			expectedMaxRetries:    defaultMaxRetries,
			expectedRetryInterval: defaultRetryInterval,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			request := &strategyprovider.TaskStrategyRequest{
				TraceId:          "test-trace",
				HealthCheckRules: tc.rules,
			}

			config := checker.parseHealthCheckConfig(request, "test-log")

			assert.Equal(t, tc.expectedMaxRetries, config.MaxRetries)
			assert.Equal(t, tc.expectedRetryInterval, config.RetryInterval)
		})
	}
}

func TestUpgradeJobHealthCheckConfig_DefaultValues(t *testing.T) {
	// 测试默认配置值的合理性
	assert.Equal(t, 5, defaultMaxRetries, "默认重试次数应该为5次")
	assert.Equal(t, 30*time.Second, defaultRetryInterval, "默认重试间隔应该为30秒")

	// 验证默认值适合upgradeJob的特点
	// upgradeJob通常需要较多的重试次数，因为升级过程可能需要时间
	assert.True(t, defaultMaxRetries >= 3, "upgradeJob应该有足够的重试次数")
	assert.True(t, defaultRetryInterval >= 10*time.Second, "upgradeJob应该有合理的重试间隔")
}

func TestUpgradeJobChecker_BasicFunctionality(t *testing.T) {
	checker := &UpgradeJobChecker{
		name:  ProviderName,
		risk:  RiskName,
		level: healthcheck.RISK_LEVEL_FATAL,
	}

	// 测试基本属性
	assert.Equal(t, "upgradeJob", ProviderName)
	assert.Equal(t, "upgradeJob健康检查", RiskName)
	assert.Equal(t, ProviderName, checker.name)
	assert.Equal(t, RiskName, checker.risk)
	assert.Equal(t, healthcheck.RISK_LEVEL_FATAL, checker.level)

	// 测试初始化
	err := checker.init()
	assert.NoError(t, err)
}

func TestUpgradeJobHealthCheckConfig_StructureConsistency(t *testing.T) {
	// 验证配置结构体与addon保持一致
	config := &UpgradeJobHealthCheckConfig{
		MaxRetries:    10,
		RetryInterval: 60 * time.Second,
	}

	assert.IsType(t, 0, config.MaxRetries, "MaxRetries应该是int类型")
	assert.IsType(t, time.Duration(0), config.RetryInterval, "RetryInterval应该是time.Duration类型")

	// 验证字段名称与addon一致
	assert.Equal(t, 10, config.MaxRetries)
	assert.Equal(t, 60*time.Second, config.RetryInterval)
}
