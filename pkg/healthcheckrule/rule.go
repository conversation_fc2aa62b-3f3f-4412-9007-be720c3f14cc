package healthcheckrule

import (
	"fmt"

	"k8s.io/klog/v2"
	"sigs.k8s.io/yaml"

	"git.woa.com/kmetis/starship/pkg/model"
	"git.woa.com/kmetis/starship/pkg/task/util"
	pkgutil "git.woa.com/kmetis/starship/pkg/util"
)

type GlobalHealthCheckBaseConfig struct {
	DefaultPreCheckPlugins        []string `json:"defaultPreCheckPlugins"`
	DefaultPostCheckPlugins       []string `json:"defaultPostCheckPlugins"`
	DefaultRollbackSupportPlugins []string `json:"defaultRollbackSupportPlugins"`
}

type ComponentHealthCheckBaseConfig struct {
	AdditionalPreCheckPlugins      []string `json:"additionalPreCheckPlugins"`
	AdditionalPostCheckPlugins     []string `json:"additionalPostCheckPlugins"`
	ExcludeDefaultPreCheckPlugins  []string `json:"excludeDefaultPreCheckPlugins"`  // 不执行全局配置的预检任务
	ExcludeDefaultPostCheckPlugins []string `json:"excludeDefaultPostCheckPlugins"` // 不执行全局配置的预检任务
}

func getHealthCheckRule(component, ruleName string) (model.HealthCheckRule, error) {
	var values map[string]interface{}
	var rulesContent string
	if component != pkgutil.GlobalHealthCheckComponent {
		rulesContent = util.GetRainbowData(fmt.Sprintf("components/%s/healthchecks/rules/%s", component, ruleName))
	} else {
		rulesContent = util.GetRainbowData(fmt.Sprintf("global/healthchecks/rules/%s", ruleName))
	}
	if rulesContent != "" {
		if err := yaml.Unmarshal([]byte(rulesContent), &values); err != nil {
			klog.Errorf("unmarshal precheck rules %s,%s failed: %v", component, ruleName, err)
			return model.HealthCheckRule{}, err
		}
	}
	return model.HealthCheckRule{
		Name:  ruleName,
		Value: values,
	}, nil
}

func getHealthCheckRules(component string, ruleNames []string) ([]model.HealthCheckRule, error) {
	rules := make([]model.HealthCheckRule, 0)
	for i := 0; i < len(ruleNames); i++ {
		ruleName := ruleNames[i]
		rule, err := getHealthCheckRule(component, ruleName)
		if err != nil {
			return nil, err
		}
		rules = append(rules, rule)
	}
	return rules, nil
}

func GetGlobalHealthCheckRules() (*model.HealthCheckRules, error) {
	globalRules := &model.HealthCheckRules{}
	var err error
	config := util.GetRainbowData("global/healthchecks/config")
	var globalHealthCheckBaseConfig GlobalHealthCheckBaseConfig
	if err := yaml.Unmarshal([]byte(config), &globalHealthCheckBaseConfig); err != nil {
		klog.Errorf("unmarshal health check config from %s failed: %v", config, err)
		return nil, err
	}
	preCheckRules, err := getHealthCheckRules(pkgutil.GlobalHealthCheckComponent, globalHealthCheckBaseConfig.DefaultPreCheckPlugins)
	if err != nil {
		return nil, err
	}
	klog.V(5).Infof("get global precheck rules: %v", preCheckRules)
	postCheckRules, err := getHealthCheckRules(pkgutil.GlobalHealthCheckComponent, globalHealthCheckBaseConfig.DefaultPostCheckPlugins)
	if err != nil {
		return nil, err
	}
	klog.V(5).Infof("get global postcheck rules: %v", postCheckRules)
	globalRules.PreCheckRules = preCheckRules
	globalRules.PostCheckRules = postCheckRules
	return globalRules, nil
}

func GetComponentHealthCheckRules(component string) (*model.ComponentHealthCheckRules, error) {
	componentRules := &model.ComponentHealthCheckRules{}
	var err error
	config := util.GetRainbowData(fmt.Sprintf("components/%s/healthchecks/config", component))
	var componentHealthCheckBaseConfig ComponentHealthCheckBaseConfig
	if err := yaml.Unmarshal([]byte(config), &componentHealthCheckBaseConfig); err != nil {
		klog.Errorf("unmarshal health check config from %s failed: %v", config, err)
		return nil, err
	}
	preCheckRules, err := getHealthCheckRules(component, componentHealthCheckBaseConfig.AdditionalPreCheckPlugins)
	if err != nil {
		return nil, err
	}
	klog.V(5).Infof("get component precheck rules: %v", preCheckRules)
	postCheckRules, err := getHealthCheckRules(component, componentHealthCheckBaseConfig.AdditionalPostCheckPlugins)
	if err != nil {
		return nil, err
	}
	klog.V(5).Infof("get component postcheck rules: %v", postCheckRules)
	componentRules.PreCheckRules = preCheckRules
	componentRules.PostCheckRules = postCheckRules
	componentRules.ExcludePreCheckPlugins = componentHealthCheckBaseConfig.ExcludeDefaultPreCheckPlugins
	componentRules.ExcludePostCheckPlugins = componentHealthCheckBaseConfig.ExcludeDefaultPostCheckPlugins

	return componentRules, nil
}

func GetMergedComponentHealthCheckRules(component string) (*model.HealthCheckRules, error) {
	mergedRules := &model.HealthCheckRules{}

	globalRules, err := GetGlobalHealthCheckRules()
	if err != nil {
		return nil, err
	}
	componentRules, err := GetComponentHealthCheckRules(component)
	if err != nil {
		return nil, err
	}

	// 去重、组件的规则优先级高于全局配置
	preRuleMap := make(map[string]bool)
	for _, v := range componentRules.ExcludePreCheckPlugins {
		preRuleMap[v] = true
	}
	klog.V(5).Infof("exclude precheck plugins:%v, appName:%s", preRuleMap, component)
	// mergeRule优先使用组件自己的规则
	for _, v := range componentRules.PreCheckRules {
		if _, ok := preRuleMap[v.Name]; !ok {
			preRuleMap[v.Name] = true
			mergedRules.PreCheckRules = append(mergedRules.PreCheckRules, v)
		}
	}

	// 将global的规则添加进来，且不能重复
	for _, v := range globalRules.PreCheckRules {
		if _, ok := preRuleMap[v.Name]; !ok {
			preRuleMap[v.Name] = true
			mergedRules.PreCheckRules = append(mergedRules.PreCheckRules, v)
		}
	}

	postRuleMap := make(map[string]bool)
	for _, v := range componentRules.ExcludePostCheckPlugins {
		postRuleMap[v] = true
	}
	klog.V(5).Infof("exclude postcheck plugins:%v, appName:%s", postRuleMap, component)
	// mergeRule优先使用组件自己的规则
	for _, v := range componentRules.PostCheckRules {
		if _, ok := postRuleMap[v.Name]; !ok {
			postRuleMap[v.Name] = true
			mergedRules.PostCheckRules = append(mergedRules.PostCheckRules, v)
		}
	}
	// 将global的规则添加进来，且不能重复
	for _, v := range globalRules.PostCheckRules {
		if _, ok := postRuleMap[v.Name]; !ok {
			postRuleMap[v.Name] = true
			mergedRules.PostCheckRules = append(mergedRules.PostCheckRules, v)
		}
	}

	return mergedRules, nil
}

// rollback时，无法检查image版本、参数等信息，因为它是回滚到之前版本。因此这里对precheck/postcheck的插件进行过滤，只支持下列插件，配置在七彩石中。
/*
defaultRollbackSupportPlugins:
  - blackList
  - daemonsetReady
  - deploymentReady
  - dsSchedule
  - eksNodeIpResourceCheck
  - k8sEvent
  - k8sEventFromCLS
  - apiserver
  - etcd
  - mastercrdready
  - node
  - schedulerReady
*/
func ExcludeSomeRuleForRollback(rules *model.HealthCheckRules) (*model.HealthCheckRules, error) {
	config := util.GetRainbowData("global/healthchecks/config")
	var globalHealthCheckBaseConfig GlobalHealthCheckBaseConfig
	if err := yaml.Unmarshal([]byte(config), &globalHealthCheckBaseConfig); err != nil {
		klog.Errorf("unmarshal health check config from %s failed: %v", config, err)
		return nil, err
	}

	supportMap := make(map[string]bool)
	for _, v := range globalHealthCheckBaseConfig.DefaultRollbackSupportPlugins {
		supportMap[v] = true
	}

	rs := &model.HealthCheckRules{}
	for _, v := range rules.PreCheckRules {
		if _, ok := supportMap[v.Name]; ok {
			rs.PreCheckRules = append(rs.PreCheckRules, v)
		}
	}

	for _, v := range rules.PostCheckRules {
		if _, ok := supportMap[v.Name]; ok {
			rs.PostCheckRules = append(rs.PostCheckRules, v)
		}
	}
	return rs, nil
}
