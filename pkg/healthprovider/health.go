package healthprovider

import (
	"context"

	"git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
)

type HealthCheck interface {
	// IsHealthy 健康检查接口,主要提供给publish和application-controller使用，兼容已有场景接口
	IsHealthy(ctx context.Context, request *pb.ComponentHealthyRequest) (*pb.ComponentHealthyReply, error)

	// IsReady 检查各个健康检查规则是否满足、状态是否就绪，并且上报健康检查接口，主要提供给starship自身使用
	IsReady(ctx context.Context, request *strategyprovider.TaskStrategyRequest) ([]*strategyprovider.TaskStrategyReply, error)
}
