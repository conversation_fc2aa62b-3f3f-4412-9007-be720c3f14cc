package healthprovider

import (
	"fmt"
	"sync"

	"k8s.io/klog/v2"
)

var (
	mutex                    sync.Mutex
	ComponentHealthProviders = make(map[string]ComponentFactory)
)

type ComponentConfig struct {
}

type ComponentFactory func(cfg *ComponentConfig) (HealthCheck, error)

// RegisterComponentHealthFactory registers the specified component health provider
func RegisterComponentHealthFactory(name string, factory ComponentFactory) {
	mutex.Lock()
	defer mutex.Unlock()

	if _, found := ComponentHealthProviders[name]; found {
		klog.V(5).Infof("component health provider:%s was registered twice", name)
	}

	klog.V(5).Infof("component health provider:%s", name)
	ComponentHealthProviders[name] = factory
}

// GetComponentHealthProvider gets the specified component health provider
func GetComponentHealthProvider(name string, ctx *ComponentConfig) (HealthCheck, error) {
	mutex.Lock()
	defer mutex.Unlock()
	f, found := ComponentHealthProviders[name]

	klog.V(5).Infof("get provider name %s, status:%t", name, found)
	if !found {
		return nil, fmt.Errorf("fatal error, component health provider not found:%s", name)
	}
	checker, err := f(ctx)
	if checker == nil && err == nil {
		err = fmt.Errorf("checker is nil")
	}
	return checker, err
}

// ListComponentHealthProvider lists all component health provider
func ListComponentHealthProvider() []string {
	var healths []string
	mutex.Lock()
	defer mutex.Unlock()
	for health := range ComponentHealthProviders {
		healths = append(healths, health)
	}
	return healths
}
