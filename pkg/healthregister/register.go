package healthregister

import (
	// register apiserver health checker
	_ "git.woa.com/kmetis/starship/pkg/healthcheck/master/apiserver"
	// register etcd health checker
	_ "git.woa.com/kmetis/starship/pkg/healthcheck/master/etcd"

	// init node checker, no need to register.
	_ "git.woa.com/kmetis/starship/pkg/healthcheck/node"

	_ "git.woa.com/kmetis/starship/pkg/healthcheck/deploymentready"

	_ "git.woa.com/kmetis/starship/pkg/healthcheck/image"

	_ "git.woa.com/kmetis/starship/pkg/healthcheck/mastercrdready"

	_ "git.woa.com/kmetis/starship/pkg/healthcheck/upgrade"

	_ "git.woa.com/kmetis/starship/pkg/healthcheck/downgrade"

	_ "git.woa.com/kmetis/starship/pkg/healthcheck/k8sevent"

	_ "git.woa.com/kmetis/starship/pkg/healthcheck/allowedImageTags"

	_ "git.woa.com/kmetis/starship/pkg/healthcheck/args"

	_ "git.woa.com/kmetis/starship/pkg/healthcheck/k8sEventFromCLS"

	_ "git.woa.com/kmetis/starship/pkg/healthcheck/ekletnodeready"

	_ "git.woa.com/kmetis/starship/pkg/healthcheck/dsschedule"

	_ "git.woa.com/kmetis/starship/pkg/healthcheck/blacklist"

	_ "git.woa.com/kmetis/starship/pkg/healthcheck/eksNodeIpResourceCheck"

	_ "git.woa.com/kmetis/starship/pkg/healthcheck/appfabricapplicationtrait"

	_ "git.woa.com/kmetis/starship/pkg/healthcheck/schedulerready"

	_ "git.woa.com/kmetis/starship/pkg/healthcheck/daemonsetready"

	_ "git.woa.com/kmetis/starship/pkg/healthcheck/kubeproxy"

	_ "git.woa.com/kmetis/starship/pkg/healthcheck/tcrcertcheck"

	_ "git.woa.com/kmetis/starship/pkg/healthcheck/daemonsetsimulation"

	_ "git.woa.com/kmetis/starship/pkg/healthcheck/addon"

	_ "git.woa.com/kmetis/starship/pkg/healthcheck/prometheusmetrics"

	_ "git.woa.com/kmetis/starship/pkg/healthcheck/upgradejob"

	_ "git.woa.com/kmetis/starship/pkg/healthcheck/configmapcheck"
)
