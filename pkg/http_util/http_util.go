package http_util

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"time"
)

func PostRequest(url string, body []byte, header http.Header, timeout time.Duration) ([]byte, int, error) {
	client := &http.Client{
		Transport: &http.Transport{
			DisableKeepAlives: true,
		},
		Timeout: timeout,
	}

	req, err := http.NewRequestWithContext(context.Background(), "POST", url, bytes.NewBuffer(body))
	if err != nil {
		return nil, 0, err
	}

	if header == nil {
		header = http.Header{}
	}
	req.Header = header
	req.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return nil, 0, err
	}
	defer resp.Body.Close()

	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, 0, err
	}

	return respBody, resp.StatusCode, nil
}
