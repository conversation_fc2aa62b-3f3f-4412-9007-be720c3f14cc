package masterutil

import (
	"fmt"
	"strings"

	masterv1alpha1 "cloud.tencent.com/tke/master-operator/pkg/apis/master.cloud.tencent.com/v1alpha1"
	corev1 "k8s.io/api/core/v1"
	"k8s.io/klog/v2"

	"git.woa.com/kmetis/starship/pkg/config"
	"git.woa.com/kmetis/starship/pkg/model"
	"git.woa.com/kmetis/starship/pkg/util"
)

func CheckMasterAdvancedPara(master *masterv1alpha1.Master, component, image string, conf *config.MasterConfig) error {
	// TODO: 增加超大集群校验，增加非法镜像校验
	switch component {
	case config.ComponentApiserver:
		if master.Spec.ApiServer.Image == image {
			return fmt.Errorf("master component %s image is same", component)
		}

	case config.ComponentControllerManager:
		master.Spec.ControllerManager.Image = image
		if master.Spec.ControllerManager.Image == image {
			return fmt.Errorf("master component %s image is same", component)
		}
	case config.ComponentScheduler:
		master.Spec.Scheduler.Image = image
		if master.Spec.Scheduler.Image == image {
			return fmt.Errorf("master component %s image is same", component)
		}
	default:
		return fmt.Errorf("master does not support component %s", component)
	}
	return nil
}

func GetMasterComponentImageTag(master *masterv1alpha1.Master, component string) (string, error) {
	var image string
	switch component {
	case config.ComponentApiserver:
		image = master.Spec.ApiServer.Image
	case config.ComponentControllerManager:
		image = master.Spec.ControllerManager.Image
	case config.ComponentScheduler:
		image = master.Spec.Scheduler.Image
	}

	tag := strings.Split(image, ":")
	if len(tag) != 2 {
		return "", fmt.Errorf("master component %s image is invalid", component)
	} else if tag[1] == "" {
		return "", fmt.Errorf("master component %s image tag is empty", component)
	}

	return tag[1], nil
}

func SetMasterAdvancedPara(master *masterv1alpha1.Master, component, imageTag string, conf *config.MasterConfig, extendInfo *model.ExtendInfo) error {
	switch component {
	case config.ComponentApiserver:
		targetImage, err := util.GetTargetImage(master.Spec.ApiServer.Image, imageTag, extendInfo)
		if err != nil {
			klog.Errorf("failed to get target image,cluster %s,component %s,err is %v", master.Name, component, err)
			return err
		}
		master.Spec.ApiServer.Image = targetImage
		// TODO: 当前conf为nil,后续支持启动参数、环境变量等修改
		if conf != nil {
			if conf.MaxRequestsInflight != 0 {
				master.Spec.ApiServer.ExtraArgs = GetNewArgvValues(master.Spec.ApiServer.ExtraArgs,
					config.KubeApiMaxRequestsInflight, conf.MaxRequestsInflight)
				master.Spec.ApiServer.ExtraArgs = GetNewArgvValues(master.Spec.ApiServer.ExtraArgs,
					config.KubeApiMaxMutatingRequestsInflight, conf.MaxMutatingRequestsInflight)
			}
			if master.Spec.ApiServer.Pod.NodeSelector != nil {
				master.Spec.ApiServer.Pod.NodeSelector = nil
			}
		}

	case config.ComponentControllerManager:
		targetImage, err := util.GetTargetImage(master.Spec.ControllerManager.Image, imageTag, extendInfo)
		if err != nil {
			klog.Errorf("failed to get target image,cluster %s,component %s,err is %v", master.Name, component, err)
			return err
		}
		master.Spec.ControllerManager.Image = targetImage
		// TODO: 当前conf为nil,后续支持启动参数、环境变量等修改
		if conf != nil {
			if conf.ControllerManagerKubeApiQps != 0 {
				master.Spec.ControllerManager.ExtraArgs = GetNewArgvValues(master.Spec.ControllerManager.ExtraArgs,
					config.KubeApiQps, conf.ControllerManagerKubeApiQps)
				master.Spec.ControllerManager.ExtraArgs = GetNewArgvValues(master.Spec.ControllerManager.ExtraArgs,
					config.KubeApiBurst, conf.ControllerManagerKubeApiBurst)
			}
		}
	case config.ComponentScheduler:
		targetImage, err := util.GetTargetImage(master.Spec.Scheduler.Image, imageTag, extendInfo)
		if err != nil {
			klog.Errorf("failed to get target image,cluster %s,component %s,err is %v", master.Name, component, err)
			return err
		}
		master.Spec.Scheduler.Image = targetImage
		// TODO: 当前conf为nil,后续支持启动参数、环境变量等修改
		if conf != nil {
			if conf.SchedulerKubeApiQps != 0 {
				master.Spec.Scheduler.ExtraArgs = GetNewArgvValues(master.Spec.Scheduler.ExtraArgs,
					config.KubeApiQps, conf.SchedulerKubeApiQps)
				master.Spec.Scheduler.ExtraArgs = GetNewArgvValues(master.Spec.Scheduler.ExtraArgs,
					config.KubeApiBurst, conf.SchedulerKubeApiBurst)
			}
		}
	default:
		return fmt.Errorf("master does not support component %s", component)
	}
	return nil
}

func GetNewArgvValues(args []string, argvName string, value int) []string {
	var find bool
	newArgv := fmt.Sprintf("%s=%d", argvName, value)
	for i := 0; i < len(args); i++ {
		if strings.Contains(args[i], argvName) {
			// max-requests-inflight=500
			// max-mutating-requests-inflight=1500
			args[i] = newArgv
			find = true
			break
		}
	}
	if !find {
		args = append(args, newArgv)
	}
	return args
}

// UpdateMasterComponentArgs 更新Master组件的启动参数
func UpdateMasterComponentArgs(master *masterv1alpha1.Master, component string, args []map[string]string) error {
	if len(args) == 0 {
		return nil
	}

	var newArgs []string
	var err error

	switch component {
	case config.ComponentApiserver:
		newArgs, err = util.BuildNewArgs(master.Spec.ApiServer.ExtraArgs, args)
		if err != nil {
			return fmt.Errorf("failed to build new args for apiserver: %v", err)
		}
		master.Spec.ApiServer.ExtraArgs = newArgs

	case config.ComponentControllerManager:
		newArgs, err = util.BuildNewArgs(master.Spec.ControllerManager.ExtraArgs, args)
		if err != nil {
			return fmt.Errorf("failed to build new args for controller-manager: %v", err)
		}
		master.Spec.ControllerManager.ExtraArgs = newArgs

	case config.ComponentScheduler:
		newArgs, err = util.BuildNewArgs(master.Spec.Scheduler.ExtraArgs, args)
		if err != nil {
			return fmt.Errorf("failed to build new args for scheduler: %v", err)
		}
		master.Spec.Scheduler.ExtraArgs = newArgs

	default:
		return fmt.Errorf("master does not support component %s", component)
	}

	return nil
}

// UpdateMasterComponentEnvs 更新Master组件的环境变量
func UpdateMasterComponentEnvs(master *masterv1alpha1.Master, component string, envs []map[string]string) error {
	if len(envs) == 0 {
		return nil
	}

	var newEnvs []corev1.EnvVar
	var err error

	switch component {
	case config.ComponentApiserver:
		newEnvs, err = util.BuildNewEnvs(master.Spec.ApiServer.Env, envs)
		if err != nil {
			return fmt.Errorf("failed to build new envs for apiserver: %v", err)
		}
		master.Spec.ApiServer.Env = newEnvs

	case config.ComponentControllerManager:
		newEnvs, err = util.BuildNewEnvs(master.Spec.ControllerManager.Env, envs)
		if err != nil {
			return fmt.Errorf("failed to build new envs for controller-manager: %v", err)
		}
		master.Spec.ControllerManager.Env = newEnvs

	case config.ComponentScheduler:
		newEnvs, err = util.BuildNewEnvs(master.Spec.Scheduler.Env, envs)
		if err != nil {
			return fmt.Errorf("failed to build new envs for scheduler: %v", err)
		}
		master.Spec.Scheduler.Env = newEnvs

	default:
		return fmt.Errorf("master does not support component %s", component)
	}

	return nil
}

// GetMasterComponentArgs 获取Master组件的启动参数
func GetMasterComponentArgs(master *masterv1alpha1.Master, component string) ([]string, error) {
	switch component {
	case config.ComponentApiserver:
		return master.Spec.ApiServer.ExtraArgs, nil
	case config.ComponentControllerManager:
		return master.Spec.ControllerManager.ExtraArgs, nil
	case config.ComponentScheduler:
		return master.Spec.Scheduler.ExtraArgs, nil
	default:
		return nil, fmt.Errorf("master does not support component %s", component)
	}
}

// GetMasterComponentEnvs 获取Master组件的环境变量
func GetMasterComponentEnvs(master *masterv1alpha1.Master, component string) ([]corev1.EnvVar, error) {
	switch component {
	case config.ComponentApiserver:
		return master.Spec.ApiServer.Env, nil
	case config.ComponentControllerManager:
		return master.Spec.ControllerManager.Env, nil
	case config.ComponentScheduler:
		return master.Spec.Scheduler.Env, nil
	default:
		return nil, fmt.Errorf("master does not support component %s", component)
	}
}

// MasterComponentState 保存Master组件的完整状态
type MasterComponentState struct {
	ImageTag string          `json:"imageTag"`
	Args     []string        `json:"args"`
	Envs     []corev1.EnvVar `json:"envs"`
}

// SaveMasterComponentState 保存Master组件的完整状态
func SaveMasterComponentState(master *masterv1alpha1.Master, component string) (*MasterComponentState, error) {
	state := &MasterComponentState{}

	// 获取镜像标签
	imageTag, err := GetMasterComponentImageTag(master, component)
	if err != nil {
		return nil, fmt.Errorf("failed to get image tag: %v", err)
	}
	state.ImageTag = imageTag

	// 获取启动参数
	args, err := GetMasterComponentArgs(master, component)
	if err != nil {
		return nil, fmt.Errorf("failed to get args: %v", err)
	}
	state.Args = args

	// 获取环境变量
	envs, err := GetMasterComponentEnvs(master, component)
	if err != nil {
		return nil, fmt.Errorf("failed to get envs: %v", err)
	}
	state.Envs = envs

	return state, nil
}

// RestoreMasterComponentState 恢复Master组件的完整状态
func RestoreMasterComponentState(master *masterv1alpha1.Master, component string, state *MasterComponentState) error {
	if state == nil {
		return fmt.Errorf("state is nil")
	}

	// 恢复镜像标签
	switch component {
	case config.ComponentApiserver:
		// 从当前镜像中提取仓库部分，然后拼接新的标签
		currentImage := master.Spec.ApiServer.Image
		if idx := strings.LastIndex(currentImage, ":"); idx != -1 {
			master.Spec.ApiServer.Image = currentImage[:idx] + ":" + state.ImageTag
		} else {
			master.Spec.ApiServer.Image = currentImage + ":" + state.ImageTag
		}
	case config.ComponentControllerManager:
		currentImage := master.Spec.ControllerManager.Image
		if idx := strings.LastIndex(currentImage, ":"); idx != -1 {
			master.Spec.ControllerManager.Image = currentImage[:idx] + ":" + state.ImageTag
		} else {
			master.Spec.ControllerManager.Image = currentImage + ":" + state.ImageTag
		}
	case config.ComponentScheduler:
		currentImage := master.Spec.Scheduler.Image
		if idx := strings.LastIndex(currentImage, ":"); idx != -1 {
			master.Spec.Scheduler.Image = currentImage[:idx] + ":" + state.ImageTag
		} else {
			master.Spec.Scheduler.Image = currentImage + ":" + state.ImageTag
		}
	default:
		return fmt.Errorf("master does not support component %s", component)
	}

	// 恢复启动参数
	switch component {
	case config.ComponentApiserver:
		master.Spec.ApiServer.ExtraArgs = state.Args
	case config.ComponentControllerManager:
		master.Spec.ControllerManager.ExtraArgs = state.Args
	case config.ComponentScheduler:
		master.Spec.Scheduler.ExtraArgs = state.Args
	default:
		return fmt.Errorf("master does not support component %s", component)
	}

	// 恢复环境变量
	switch component {
	case config.ComponentApiserver:
		master.Spec.ApiServer.Env = state.Envs
	case config.ComponentControllerManager:
		master.Spec.ControllerManager.Env = state.Envs
	case config.ComponentScheduler:
		master.Spec.Scheduler.Env = state.Envs
	default:
		return fmt.Errorf("master does not support component %s", component)
	}

	return nil
}
