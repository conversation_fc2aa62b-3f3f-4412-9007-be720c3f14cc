package masterutil

import (
	"encoding/json"
	"testing"

	masterv1alpha1 "cloud.tencent.com/tke/master-operator/pkg/apis/master.cloud.tencent.com/v1alpha1"
	corev1 "k8s.io/api/core/v1"

	"git.woa.com/kmetis/starship/pkg/config"
)

func TestUpdateMasterComponentArgs(t *testing.T) {
	// 创建测试用的 Master 对象
	master := &masterv1alpha1.Master{
		Spec: masterv1alpha1.MasterSpec{
			ApiServer: masterv1alpha1.ApiServerConfig{
				ComponentExtraConfig: masterv1alpha1.ComponentExtraConfig{
					ExtraArgs: []string{"--arg1=value1", "--arg2=value2"},
				},
			},
			ControllerManager: masterv1alpha1.ControllerManagerConfig{
				ComponentExtraConfig: masterv1alpha1.ComponentExtraConfig{
					ExtraArgs: []string{"--arg3=value3"},
				},
			},
			Scheduler: masterv1alpha1.SchedulerConfig{
				ComponentExtraConfig: masterv1alpha1.ComponentExtraConfig{
					ExtraArgs: []string{"--arg4=value4"},
				},
			},
		},
	}

	tests := []struct {
		name      string
		component string
		args      []map[string]string
		wantErr   bool
	}{
		{
			name:      "更新 apiserver 参数",
			component: config.ComponentApiserver,
			args: []map[string]string{
				{"type": "update", "value": "--arg1=newvalue1"},
				{"type": "update", "value": "--arg5=value5"},
			},
			wantErr: false,
		},
		{
			name:      "删除 controller-manager 参数",
			component: config.ComponentControllerManager,
			args: []map[string]string{
				{"type": "remove", "value": "--arg3"},
			},
			wantErr: false,
		},
		{
			name:      "不支持的组件",
			component: "unsupported",
			args: []map[string]string{
				{"type": "update", "value": "--arg1=value1"},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := UpdateMasterComponentArgs(master, tt.component, tt.args)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateMasterComponentArgs() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestUpdateMasterComponentEnvs(t *testing.T) {
	// 创建测试用的 Master 对象
	master := &masterv1alpha1.Master{
		Spec: masterv1alpha1.MasterSpec{
			ApiServer: masterv1alpha1.ApiServerConfig{
				ComponentExtraConfig: masterv1alpha1.ComponentExtraConfig{
					Env: []corev1.EnvVar{
						{Name: "ENV1", Value: "value1"},
						{Name: "ENV2", Value: "value2"},
					},
				},
			},
			ControllerManager: masterv1alpha1.ControllerManagerConfig{
				ComponentExtraConfig: masterv1alpha1.ComponentExtraConfig{
					Env: []corev1.EnvVar{
						{Name: "ENV3", Value: "value3"},
					},
				},
			},
			Scheduler: masterv1alpha1.SchedulerConfig{
				ComponentExtraConfig: masterv1alpha1.ComponentExtraConfig{
					Env: []corev1.EnvVar{
						{Name: "ENV4", Value: "value4"},
					},
				},
			},
		},
	}

	tests := []struct {
		name      string
		component string
		envs      []map[string]string
		wantErr   bool
	}{
		{
			name:      "更新 apiserver 环境变量",
			component: config.ComponentApiserver,
			envs: []map[string]string{
				{"type": "add", "key": "ENV1", "value": "newvalue1"},
				{"type": "add", "key": "ENV5", "value": "value5"},
			},
			wantErr: false,
		},
		{
			name:      "删除 controller-manager 环境变量",
			component: config.ComponentControllerManager,
			envs: []map[string]string{
				{"type": "remove", "key": "ENV3"},
			},
			wantErr: false,
		},
		{
			name:      "不支持的组件",
			component: "unsupported",
			envs: []map[string]string{
				{"type": "add", "key": "ENV1", "value": "value1"},
			},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := UpdateMasterComponentEnvs(master, tt.component, tt.envs)
			if (err != nil) != tt.wantErr {
				t.Errorf("UpdateMasterComponentEnvs() error = %v, wantErr %v", err, tt.wantErr)
			}
		})
	}
}

func TestSaveAndRestoreMasterComponentState(t *testing.T) {
	// 创建测试用的 Master 对象
	master := &masterv1alpha1.Master{
		Spec: masterv1alpha1.MasterSpec{
			ApiServer: masterv1alpha1.ApiServerConfig{
				ComponentExtraConfig: masterv1alpha1.ComponentExtraConfig{
					Image:    "test-image:v1.0.0",
					ExtraArgs: []string{"--arg1=value1", "--arg2=value2"},
					Env: []corev1.EnvVar{
						{Name: "ENV1", Value: "value1"},
						{Name: "ENV2", Value: "value2"},
					},
				},
			},
		},
	}

	// 保存状态
	state, err := SaveMasterComponentState(master, config.ComponentApiserver)
	if err != nil {
		t.Fatalf("SaveMasterComponentState() error = %v", err)
	}

	// 验证保存的状态
	if state.ImageTag != "v1.0.0" {
		t.Errorf("SaveMasterComponentState() ImageTag = %v, want %v", state.ImageTag, "v1.0.0")
	}

	if len(state.Args) != 2 {
		t.Errorf("SaveMasterComponentState() Args length = %v, want %v", len(state.Args), 2)
	}

	if len(state.Envs) != 2 {
		t.Errorf("SaveMasterComponentState() Envs length = %v, want %v", len(state.Envs), 2)
	}

	// 修改 Master 对象
	master.Spec.ApiServer.Image = "test-image:v2.0.0"
	master.Spec.ApiServer.ExtraArgs = []string{"--arg3=value3"}
	master.Spec.ApiServer.Env = []corev1.EnvVar{{Name: "ENV3", Value: "value3"}}

	// 恢复状态
	err = RestoreMasterComponentState(master, config.ComponentApiserver, state)
	if err != nil {
		t.Fatalf("RestoreMasterComponentState() error = %v", err)
	}

	// 验证恢复的状态
	if master.Spec.ApiServer.Image != "test-image:v1.0.0" {
		t.Errorf("RestoreMasterComponentState() Image = %v, want %v", master.Spec.ApiServer.Image, "test-image:v1.0.0")
	}

	if len(master.Spec.ApiServer.ExtraArgs) != 2 {
		t.Errorf("RestoreMasterComponentState() ExtraArgs length = %v, want %v", len(master.Spec.ApiServer.ExtraArgs), 2)
	}

	if len(master.Spec.ApiServer.Env) != 2 {
		t.Errorf("RestoreMasterComponentState() Env length = %v, want %v", len(master.Spec.ApiServer.Env), 2)
	}
}

func TestMasterComponentStateJSON(t *testing.T) {
	// 测试 JSON 序列化和反序列化
	state := &MasterComponentState{
		ImageTag: "v1.0.0",
		Args:     []string{"--arg1=value1", "--arg2=value2"},
		Envs: []corev1.EnvVar{
			{Name: "ENV1", Value: "value1"},
			{Name: "ENV2", Value: "value2"},
		},
	}

	// 序列化
	data, err := json.Marshal(state)
	if err != nil {
		t.Fatalf("json.Marshal() error = %v", err)
	}

	// 反序列化
	var restoredState MasterComponentState
	err = json.Unmarshal(data, &restoredState)
	if err != nil {
		t.Fatalf("json.Unmarshal() error = %v", err)
	}

	// 验证
	if restoredState.ImageTag != state.ImageTag {
		t.Errorf("ImageTag = %v, want %v", restoredState.ImageTag, state.ImageTag)
	}

	if len(restoredState.Args) != len(state.Args) {
		t.Errorf("Args length = %v, want %v", len(restoredState.Args), len(state.Args))
	}

	if len(restoredState.Envs) != len(state.Envs) {
		t.Errorf("Envs length = %v, want %v", len(restoredState.Envs), len(state.Envs))
	}
} 