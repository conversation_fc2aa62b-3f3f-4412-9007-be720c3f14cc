package metrics

import (
	"github.com/prometheus/client_golang/prometheus"
)

var (
	ComponentDialTestErrorTotal = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "component_dial_test_error_total",
		Help: "Total number of dial test error.",
	}, []string{"clusterId", "product", "component"})

	ComponentDialTestFailedTotal = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "component_dial_test_failed_total",
		Help: "Total number of dial test failed.",
	}, []string{"clusterId", "product", "component"})

	ClusterNodesCheckErrorTotal = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "cluster_nodes_check_error_total",
		Help: "Total number of check error.",
	}, []string{"clusterId", "product"})

	ClusterNodesCheckSkippedTotal = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "cluster_nodes_check_skipped_total",
		Help: "Total number of check skipped.",
	}, []string{"clusterId", "product"})

	ClusterNodesCheckFailedTotal = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "cluster_nodes_check_failed_total",
		Help: "Total number of check failed",
	}, []string{"clusterId", "product"})

	MarkTimeoutTaskFailedTotal = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "mark_timeout_task_failed_total",
		Help: "Total number of mark timeout task failed",
	}, []string{})

	JobCreateFailedTotal = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "healthcheck_job_create_failed_total",
		Help: "Total number of healthcheck job create failed",
	}, []string{})
	TaskExecFailedTotal = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "healthcheck_task_exec_failed_total",
		Help: "Total number of healthcheck task exec failed",
	}, []string{})
	TaskCreateFailedTotal = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "healthcheck_task_create_failed_total",
		Help: "Total number of healthcheck task create failed",
	}, []string{})
	TaskReportFailedTotal = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "healthcheck_task_report_failed_total",
		Help: "Total number of healthcheck task report failed",
	}, []string{})
	TaskListFailedTotal = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "healthcheck_task_list_failed_total",
		Help: "Total number of healthcheck task list failed",
	}, []string{})

	ClusterInfoFetchFailedTotal = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "cluster_info_fetch_failed_total",
		Help: "Total number of cluster info fetch failed",
	}, []string{})

	// third party task metrics
	TPTaskQueryFailedTotal = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "healthcheck_third_party_task_query_failed_total",
		Help: "Total number of third party task query failed",
	}, []string{"grpcServer"})
	TPTaskCreateFailedTotal = prometheus.NewGaugeVec(prometheus.GaugeOpts{
		Name: "healthcheck_third_party_task_create_failed_total",
		Help: "Total number of third party task create failed",
	}, []string{"grpcServer"})
)

func init() {
	prometheus.MustRegister(ComponentDialTestErrorTotal, ComponentDialTestFailedTotal, ClusterNodesCheckErrorTotal, ClusterNodesCheckSkippedTotal, ClusterNodesCheckFailedTotal, MarkTimeoutTaskFailedTotal, JobCreateFailedTotal, TaskExecFailedTotal, TaskCreateFailedTotal, TaskReportFailedTotal, TaskListFailedTotal, ClusterInfoFetchFailedTotal, TPTaskQueryFailedTotal, TPTaskCreateFailedTotal)
}
