package model

import (
	"encoding/json"
)

// ConfigMap操作类型常量
const (
	ConfigMapOperationUpdate = "update"
	ConfigMapOperationRemove = "remove"
)

type TagVersion struct {
	// 1.21.6-tke.422.xxx
	MajorVersion int // 主版本 1
	MinorVersion int // 次版本 21
	PatchVersion int // 补丁版本 6
	TkeVersion   int // tke version 422
}

type Container struct {
	Env      map[string]string   `json:"Env"`
	Args     []map[string]string `json:"Args"`
	ImageTag string              `json:"ImageTag"`
}

/*
	"ExtendInfo": {
				"Args": [{"type": "update", value: "--arg1=value1"},{"type": "remove", value: "--arg2=value2"}], // 待更新的args参数列表，type为update/delete，value为参数值
				"HostAlias":{"*******":["a.com","b.com","*******":["a.b.c","x.y.z"]}, // 待更新的hostAlias参数列表
				"Envs": [{"type": "update", "key": "key1", "value": "value1"}, {"type": "remove", "key": "key2"}],
				"Annotations": [{"type": "update", "key": "key1", "value": "value1"},{"type": "remove", "key": "key2"}],
				"ContainerName": "container1,container2", // 待更新的container name，支持单个container和多个container
				"Scheduler": true, // 是否需要更新调度器，默认false
				"UpdateStrategy": "RollingUpdate", // 滚动更新策略，默认OnDelete，支持OnDelete/RollingUpdate
				"SkipWarningPreCheckItem": true
			},
*/
type ExtendInfo struct {
	// TODO: 当前只支持一个container的变更
	// 1.如果deploy中只有一个container可以不写containerName
	// 2.如果deploy中多个container，必须写containerName
	// Containers map[string]Container `json:"Containers"`
	Envs                    []map[string]string `json:"Envs"`
	Args                    []map[string]string `json:"Args"`
	ContainerName           string              `json:"ContainerName"`
	Scheduler               bool                `json:"Scheduler"`
	UpdateStrategy          string              `json:"UpdateStrategy"`
	SkipWarningPreCheckItem bool                `json:"SkipWarningPreCheckItem"`

	HostAlias   map[string][]string `json:"HostAlias"`
	Annotations []map[string]string `json:"Annotations"`

	// addon发布
	App string `json:"App"`

	// 支持camp发布
	Name      string  `json:"Name"`
	Namespace string  `json:"Namespace"`
	Patch     []Patch `json:"Patch"`

	// rollback
	Rollback map[string]string `json:"Rollback"`

	// ekletagent
	EksId string `json:"EksId"`

	// ConfigMap更新数据
	ConfigMapData []ConfigMapDataItem `json:"ConfigMapData"`
}

// ConfigMapDataItem represents a single ConfigMap data operation
type ConfigMapDataItem struct {
	Type  string `json:"type"`  // "update" 或 "remove"
	Key   string `json:"key"`   // ConfigMap data中的key
	Value string `json:"value"` // ConfigMap data中的value（remove操作时可为空）
}

type Patch struct {
	// 变更key
	Key string `json:"key"`
	// 变更value
	Value      string `json:"value"`
	Scope      string `json:"scope"`
	OriginData string `json:"originData"`
}

// FormatExtendInfoToJSON 将ExtendInfo结构体格式化为JSON字符串
func FormatExtendInfoToJSON(extend *ExtendInfo) (string, error) {
	if extend == nil {
		return "", nil
	}
	jsonData, err := json.Marshal(extend)
	if err != nil {
		return "", err
	}
	return string(jsonData), nil
}
