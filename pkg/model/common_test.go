package model

import (
	"encoding/json"
	"testing"
)

func TestSkipWarningPreCheckItemCaseSensitivity(t *testing.T) {
	// Test case 1: Standard case (SkipWarningPreCheckItem)
	jsonData1 := `{"SkipWarningPreCheckItem": true}`
	var extInfo1 ExtendInfo
	err := json.Unmarshal([]byte(jsonData1), &extInfo1)
	if err != nil {
		t.Fatalf("Failed to unmarshal JSON: %v", err)
	}
	if !extInfo1.SkipWarningPreCheckItem {
		t.Errorf("Expected SkipWarningPreCheckItem to be true, got false")
	}

	// Test case 2: Lowercase case (skipWarningPreCheckItem)
	jsonData2 := `{"skipWarningPreCheckItem": true}`
	var extInfo2 ExtendInfo
	err = json.Unmarshal([]byte(jsonData2), &extInfo2)
	if err != nil {
		t.Fatalf("Failed to unmarshal JSON: %v", err)
	}
	if !extInfo2.SkipWarningPreCheckItem {
		t.<PERSON>("Expected SkipWarningPreCheckItem to be true, got false")
	}
}

func TestConfigMapDataItemSerialization(t *testing.T) {
	// Test ConfigMapDataItem JSON marshaling and unmarshaling
	testCases := []struct {
		name     string
		item     ConfigMapDataItem
		expected string
	}{
		{
			name: "update operation",
			item: ConfigMapDataItem{
				Type:  ConfigMapOperationUpdate,
				Key:   "config.yaml",
				Value: "server:\n  port: 8080",
			},
			expected: `{"type":"update","key":"config.yaml","value":"server:\n  port: 8080"}`,
		},
		{
			name: "remove operation",
			item: ConfigMapDataItem{
				Type:  ConfigMapOperationRemove,
				Key:   "deprecated.conf",
				Value: "",
			},
			expected: `{"type":"remove","key":"deprecated.conf","value":""}`,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Test marshaling
			jsonData, err := json.Marshal(tc.item)
			if err != nil {
				t.Fatalf("Failed to marshal ConfigMapDataItem: %v", err)
			}
			if string(jsonData) != tc.expected {
				t.Errorf("Expected JSON: %s, got: %s", tc.expected, string(jsonData))
			}

			// Test unmarshaling
			var item ConfigMapDataItem
			err = json.Unmarshal(jsonData, &item)
			if err != nil {
				t.Fatalf("Failed to unmarshal ConfigMapDataItem: %v", err)
			}
			if item.Type != tc.item.Type || item.Key != tc.item.Key || item.Value != tc.item.Value {
				t.Errorf("Unmarshaled item doesn't match original: got %+v, expected %+v", item, tc.item)
			}
		})
	}
}

func TestExtendInfoWithConfigMapData(t *testing.T) {
	// Test ExtendInfo with ConfigMapData field
	testData := ExtendInfo{
		ConfigMapData: []ConfigMapDataItem{
			{
				Type:  ConfigMapOperationUpdate,
				Key:   "config.yaml",
				Value: "server:\n  port: 8080\n  host: 0.0.0.0",
			},
			{
				Type:  ConfigMapOperationUpdate,
				Key:   "database.properties",
				Value: "host=localhost\nport=3306\ndb=myapp",
			},
			{
				Type:  ConfigMapOperationRemove,
				Key:   "deprecated.conf",
				Value: "",
			},
		},
	}

	// Test JSON marshaling
	jsonData, err := json.Marshal(testData)
	if err != nil {
		t.Fatalf("Failed to marshal ExtendInfo: %v", err)
	}

	// Test JSON unmarshaling
	var unmarshaled ExtendInfo
	err = json.Unmarshal(jsonData, &unmarshaled)
	if err != nil {
		t.Fatalf("Failed to unmarshal ExtendInfo: %v", err)
	}

	// Verify ConfigMapData field
	if len(unmarshaled.ConfigMapData) != 3 {
		t.Errorf("Expected 3 ConfigMapDataItems, got %d", len(unmarshaled.ConfigMapData))
	}

	// Verify first item (update operation)
	if unmarshaled.ConfigMapData[0].Type != ConfigMapOperationUpdate {
		t.Errorf("Expected first item type to be %s, got %s", ConfigMapOperationUpdate, unmarshaled.ConfigMapData[0].Type)
	}
	if unmarshaled.ConfigMapData[0].Key != "config.yaml" {
		t.Errorf("Expected first item key to be 'config.yaml', got %s", unmarshaled.ConfigMapData[0].Key)
	}

	// Verify third item (remove operation)
	if unmarshaled.ConfigMapData[2].Type != ConfigMapOperationRemove {
		t.Errorf("Expected third item type to be %s, got %s", ConfigMapOperationRemove, unmarshaled.ConfigMapData[2].Type)
	}
	if unmarshaled.ConfigMapData[2].Key != "deprecated.conf" {
		t.Errorf("Expected third item key to be 'deprecated.conf', got %s", unmarshaled.ConfigMapData[2].Key)
	}
}

func TestFormatExtendInfoToJSONWithConfigMapData(t *testing.T) {
	// Test FormatExtendInfoToJSON function with ConfigMapData
	extendInfo := &ExtendInfo{
		ConfigMapData: []ConfigMapDataItem{
			{
				Type:  ConfigMapOperationUpdate,
				Key:   "test.conf",
				Value: "test=value",
			},
		},
	}

	jsonStr, err := FormatExtendInfoToJSON(extendInfo)
	if err != nil {
		t.Fatalf("Failed to format ExtendInfo to JSON: %v", err)
	}

	// Verify the JSON contains ConfigMapData
	if jsonStr == "" {
		t.Error("Expected non-empty JSON string")
	}

	// Test with nil ExtendInfo
	jsonStr, err = FormatExtendInfoToJSON(nil)
	if err != nil {
		t.Fatalf("Failed to format nil ExtendInfo: %v", err)
	}
	if jsonStr != "" {
		t.Errorf("Expected empty string for nil ExtendInfo, got: %s", jsonStr)
	}
}

func TestConfigMapOperationConstants(t *testing.T) {
	// Test that constants are defined correctly
	if ConfigMapOperationUpdate != "update" {
		t.Errorf("Expected ConfigMapOperationUpdate to be 'update', got %s", ConfigMapOperationUpdate)
	}
	if ConfigMapOperationRemove != "remove" {
		t.Errorf("Expected ConfigMapOperationRemove to be 'remove', got %s", ConfigMapOperationRemove)
	}
}
