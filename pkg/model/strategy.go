package model

import (
	"errors"
	"fmt"
	"strings"
)

type HealthCheckRule struct {
	// Name表示健康检查规则名称，比如k8sEvent
	Name string
	// Value表示规则的具体内容，比如k8sEvent的Value可以是{"keyword":"FailedScheduling"}，一旦含有调度失败的关键字，则预检、后检失败
	Value map[string]interface{}
}

// GetValue key的格式可以支持key1.key2.key3，可以直接获取到叶子节点value
func (r *HealthCheckRule) GetValue(key string) (interface{}, error) {
	if key == "" {
		return nil, errors.New("key is empty")
	}
	if r.Value == nil {
		return nil, errors.New("value is nil")
	}
	keys := strings.Split(key, ".")
	currentMap := r.Value
	for i, k := range keys {
		if val, ok := currentMap[k]; ok {
			if nestedMap, isMap := val.(map[string]interface{}); isMap && i < len(keys)-1 {
				currentMap = nestedMap
			} else if i == len(keys)-1 {
				return val, nil // 返回叶子节点的值
			} else {
				return nil, fmt.Errorf("key path is invalid at key: %s", k)
			}
		} else {
			return nil, fmt.Errorf("key not found: %s", k)
		}
	}
	return nil, fmt.Errorf("failed to retrieve value for key: %s", key)
}

func (r *HealthCheckRule) GetString(key string) (string, error) {
	val, err := r.GetValue(key)
	if err != nil {
		return "", err
	}
	if val == nil {
		return "", errors.New("key not found")
	}
	if str, ok := val.(string); ok {
		return str, nil
	}
	return "", errors.New("value is not string")
}

func (r *HealthCheckRule) GetStringArray(key string) ([]string, error) {
	val, err := r.GetValue(key)
	if err != nil {
		return nil, err
	}
	if vals, ok := val.([]interface{}); ok {
		values := make([]string, 0)
		for _, v := range vals {
			if str, ok := v.(string); ok {
				values = append(values, str)
			} else {
				return nil, fmt.Errorf("value item is not string: %v", v)
			}
		}
		return values, nil
	}
	return nil, errors.New("value is not string array")
}

func (r *HealthCheckRule) Exist(key string) bool {
	val, err := r.GetValue(key)
	if err != nil {
		return false
	}
	return val != nil
}

type HealthCheckRules struct {
	PreCheckRules  []HealthCheckRule `json:"preCheckRules"`
	PostCheckRules []HealthCheckRule `json:"postCheckRules"`
}

type ComponentHealthCheckRules struct {
	PreCheckRules           []HealthCheckRule `json:"preCheckRules"`
	PostCheckRules          []HealthCheckRule `json:"postCheckRules"`
	ExcludePreCheckPlugins  []string          `json:"excludePreCheckPlugins"`
	ExcludePostCheckPlugins []string          `json:"excludePostCheckPlugins"`
}
