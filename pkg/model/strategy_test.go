package model

import (
	"github.com/stretchr/testify/assert"
	"reflect"
	"testing"
)

func TestHealthCheckRule_GetValue(t *testing.T) {
	tests := []struct {
		name    string
		rule    *HealthCheckRule
		key     string
		want    interface{}
		wantErr string
	}{
		{
			name: "empty key",
			rule: &HealthCheckRule{
				Value: map[string]interface{}{"test": "value"},
			},
			key:     "",
			want:    nil,
			wantErr: "key is empty",
		},
		{
			name: "nil value map",
			rule: &HealthCheckRule{
				Value: nil,
			},
			key:     "test",
			want:    nil,
			wantErr: "value is nil",
		},
		{
			name: "simple key value",
			rule: &HealthCheckRule{
				Value: map[string]interface{}{
					"keyword": "FailedScheduling",
				},
			},
			key:     "keyword",
			want:    "FailedScheduling",
			wantErr: "",
		},
		{
			name: "nested key value",
			rule: &HealthCheckRule{
				Value: map[string]interface{}{
					"k8s": map[string]interface{}{
						"event": map[string]interface{}{
							"keyword": "FailedScheduling",
						},
					},
				},
			},
			key:     "k8s.event.keyword",
			want:    "FailedScheduling",
			wantErr: "",
		},
		{
			name: "key not found",
			rule: &HealthCheckRule{
				Value: map[string]interface{}{
					"keyword": "FailedScheduling",
				},
			},
			key:     "nonexistent",
			want:    nil,
			wantErr: "key not found: nonexistent",
		},
		{
			name: "invalid path - expecting map but got string",
			rule: &HealthCheckRule{
				Value: map[string]interface{}{
					"k8s": "not a map",
				},
			},
			key:     "k8s.event",
			want:    nil,
			wantErr: "key path is invalid at key: k8s",
		},
		{
			name: "mixed type values",
			rule: &HealthCheckRule{
				Value: map[string]interface{}{
					"string": "value",
					"number": 42,
					"bool":   true,
					"nested": map[string]interface{}{
						"array": []string{"a", "b"},
					},
				},
			},
			key:     "nested.array",
			want:    []string{"a", "b"},
			wantErr: "",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.rule.GetValue(tt.key)

			if tt.wantErr != "" {
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.wantErr)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, tt.want, got)
			}
		})
	}
}

func TestHealthCheckRule_GetStringArray(t *testing.T) {
	tests := []struct {
		name    string
		rule    *HealthCheckRule
		key     string
		want    []string
		wantErr bool
	}{
		{
			name: "normal string array",
			rule: &HealthCheckRule{
				Name: "test",
				Value: map[string]interface{}{
					"keywords": []string{"error", "failed"},
				},
			},
			key:     "keywords",
			want:    []string{"error", "failed"},
			wantErr: false,
		},
		{
			name: "nested string array",
			rule: &HealthCheckRule{
				Name: "test",
				Value: map[string]interface{}{
					"events": map[string]interface{}{
						"keywords": []string{"FailedScheduling", "FailedMount"},
					},
				},
			},
			key:     "events.keywords",
			want:    []string{"FailedScheduling", "FailedMount"},
			wantErr: false,
		},
		{
			name: "nil value map",
			rule: &HealthCheckRule{
				Name:  "test",
				Value: nil,
			},
			key:     "keywords",
			want:    nil,
			wantErr: true,
		},
		{
			name: "key not exist",
			rule: &HealthCheckRule{
				Name:  "test",
				Value: map[string]interface{}{},
			},
			key:     "keywords",
			want:    nil,
			wantErr: true,
		},
		{
			name: "value is not string array",
			rule: &HealthCheckRule{
				Name: "test",
				Value: map[string]interface{}{
					"keywords": 123,
				},
			},
			key:     "keywords",
			want:    nil,
			wantErr: true,
		},
		{
			name: "value is interface array but not string array",
			rule: &HealthCheckRule{
				Name: "test",
				Value: map[string]interface{}{
					"mixed": []interface{}{123, "test"},
				},
			},
			key:     "mixed",
			want:    nil,
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.rule.GetStringArray(tt.key)

			// 检查错误
			if (err != nil) != tt.wantErr {
				t.Errorf("GetStringArray() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			// 检查返回值
			if !reflect.DeepEqual(got, tt.want) {
				t.Errorf("GetStringArray() got = %v, want %v", got, tt.want)
			}
		})
	}
}
