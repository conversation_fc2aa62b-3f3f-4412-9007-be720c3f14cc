package addon

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog/v2"
	applicationv1 "tkestack.io/tke/api/application/v1"
	"tkestack.io/tke/api/client/clientset/versioned"

	"git.woa.com/kmetis/starship/infra"
	"git.woa.com/kmetis/starship/pkg/clientset/application"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
)

const (
	ProviderName = "addon"
)

func init() {
	strategyprovider.RegisterReleaseStrategyFactory(
		ProviderName,
		func(ctx *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
			return NewAddonStrategy(ctx)
		},
	)
}

type AddonStrategy struct {
	cfg *strategyprovider.ReleaseConfig
}

func NewAddonStrategy(ctx *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
	return &AddonStrategy{cfg: ctx}, nil
}

func (s *AddonStrategy) Validate(ctx context.Context, request *strategyprovider.TaskStrategyRequest) error {
	// do nothing
	return nil
}

func (s *AddonStrategy) Exec(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	klog.V(5).Infof("start to update addon %s", request.TraceId)

	if request.Action != util.TaskActionUpgrade {
		klog.Errorf("update addon %s: skip %s", request.TraceId, request.Action)
		return nil, nil
	}

	app := new(applicationv1.App)
	if err := json.Unmarshal([]byte(request.Extend.App), app); err != nil {
		klog.Errorf("failed to update addon %s: %v", request.TraceId, err)
		return nil, err
	}

	if app.Namespace != request.ClusterId || app.Name != request.Component {
		err := fmt.Errorf("app not match %s/%s %s/%s",
			app.Namespace, app.Name, request.ClusterId, request.Component)
		klog.Errorf("update addon %s: %v", request.TraceId, err)
		return nil, err
	}

	old, err := s.updateApp(ctx, request, app)
	if err != nil {
		klog.Errorf("failed to update addon %s: %s/%s %v", request.TraceId, app.Namespace, app.Name, err)
		return nil, err
	}

	err = infra.CreateRevision(&infra.StarshipRevision{
		TaskId:       request.TaskId,
		SubTaskId:    request.SubTaskId,
		AppName:      request.Component,
		Strategy:     request.Strategy,
		ResourceType: ProviderName,
		ResourceInfo: app.Namespace + "/" + app.Name,
		Revision:     getRevision(old),
	})
	if err != nil {
		klog.Warningf("failed to create addon revision %s: %s/%s %v", request.TraceId, app.Namespace, app.Name, err)
	}

	klog.Infof("finish to update addon %s: %s/%s", request.TraceId, app.Namespace, app.Name)
	return &strategyprovider.TaskStrategyReply{}, nil
}

func (s *AddonStrategy) Rollback(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	if request.Action != util.TaskActionRollback {
		klog.Errorf("rollback addon %s: skip %s", request.TraceId, request.Action)
		return nil, nil
	}

	if request.Extend == nil || request.Extend.Rollback == nil || request.Extend.Rollback["TaskId"] == "" {
		klog.Errorf("failed to get revision for addon %s", request.TraceId)
		return nil, fmt.Errorf("rollback metadata wrong")
	}

	orgTaskId, _ := strconv.ParseInt(request.Extend.Rollback["TaskId"], 10, 64)
	rv, err := infra.GetRevisionByTask(orgTaskId)
	if err != nil {
		klog.Errorf("failed to get revision for %s: orgTask %d %v", request.TraceId, orgTaskId, err)
		return nil, err
	}

	app := new(applicationv1.App)
	if err = json.Unmarshal([]byte(rv.Revision), app); err != nil {
		klog.Errorf("failed to rollback addon %s: %v", request.TraceId, err)
		return nil, err
	}

	_, err = s.updateApp(ctx, request, app)
	if err != nil {
		klog.Errorf("failed to rollback addon %s: %s/%s %v", request.TraceId, app.Namespace, app.Name, err)
		return &strategyprovider.TaskStrategyReply{}, err
	}

	klog.Infof("finish to rollback addon %s: %s/%s", request.TraceId, app.Namespace, app.Name)
	return &strategyprovider.TaskStrategyReply{}, nil
}

func (s *AddonStrategy) updateApp(ctx context.Context, request *strategyprovider.TaskStrategyRequest, target *applicationv1.App) (*applicationv1.App, error) {
	var appclient *versioned.Clientset
	if request.ProductName == "tke" {
		appclient = application.GetClientSet().Client
	} else if request.ProductName == "eks" {
		appclient = application.GetClientSet().EksClient
	} else {
		return nil, fmt.Errorf("productName is invalid: %s", request.ProductName)
	}

	curApp, err := appclient.ApplicationV1().Apps(target.Namespace).Get(ctx, target.Name, metav1.GetOptions{})
	if err != nil {
		return nil, err
	}

	newApp := curApp.DeepCopy()
	newApp.Spec = *target.Spec.DeepCopy()
	// 如果存在之前starship的注解，这里也进行移除
	delete(newApp.Annotations, "application.tkestack.io/change-id")
	delete(newApp.Annotations, "application.tkestack.io/enable-starship-healthcheck")
	newApp.Status.Phase = applicationv1.AppPhaseUpgrading

	_, err = appclient.ApplicationV1().Apps(target.Namespace).Update(ctx, newApp, metav1.UpdateOptions{})
	if err != nil {
		return nil, err
	}

	return curApp, nil
}

func getRevision(app *applicationv1.App) string {
	appDate, _ := json.Marshal(app)
	return string(appDate)
}
