package appfabricapplication

import (
	"context"
	"encoding/json"
	"fmt"
	"k8s.io/klog"
	"net/http"
	"strconv"
	"strings"
	"time"

	"git.woa.com/kamp/camp-api/domain/server/yunapi/types"
	"git.woa.com/kmetis/starship/pkg/http_util"
	"git.woa.com/kmetis/starship/pkg/model"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	tadv1 "git.woa.com/tad-dev/api/apis/tad/v1"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

const (
	ApiModule    = "camp"
	ProviderName = "appFabricApplication"
)

const (
	CLSKeyUnMatchUpload = "UnMatchUpload"
	CLSKeyUnMatchedKey  = "UnMatchedKey"
	CLSKeyJsonStandard  = "JsonStandard"
	CLSKeyLogType       = "LogType"
	CLSKeyBacktracking  = "Backtracking"
	CLSKeyIsGBK         = "IsGBK"
	CLSKeyTimeKey       = "TimeKey"
	CLSKeyTimeFormat    = "TimeFormat"

	CLSKeyMetadataContainer = "MetadataContainer"
	CLSKeyMetadataLabels    = "MetadataLabels"
)

var (
	ErrInvalidKey     = fmt.Errorf("unsupported key")
	ErrOriginNotMatch = fmt.Errorf("origin data does not match")
	ErrNoChangeNeeded = fmt.Errorf("current value is same as new value, no need to update")
)

func init() {
	strategyprovider.RegisterReleaseStrategyFactory(
		ProviderName,
		func(cfg *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
			return NewAppFabricStrategy(cfg)
		},
	)
}

type AppFabricStrategy struct {
}

func NewAppFabricStrategy(ctx *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
	return &AppFabricStrategy{}, nil
}

func (a *AppFabricStrategy) Validate(ctx context.Context, request *strategyprovider.TaskStrategyRequest) error {
	if request.Extend == nil || request.Extend.Name == "" || request.Extend.Namespace == "" || len(request.Extend.Patch) == 0 {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,extendInfo is empty or invalid, ignore",
			request.ChangeId, request.TaskName, request.ClusterId, request.Component)
		return fmt.Errorf("extendInfo is empty or invalid")
	}
	for _, patch := range request.Extend.Patch {
		if patch.Key == "" || (patch.Scope != "" && patch.Scope != "all") {
			klog.Errorf("changeid %s,task name %s, cluster %s,component %s,patch %v is invalid, ignore",
				request.ChangeId, request.TaskName, request.ClusterId, request.Component, patch)
			return fmt.Errorf("extendInfo is empty or invalid")
		}
	}
	return util.ValidateTaskStrategyRequest(request)
}

func (a *AppFabricStrategy) Exec(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	if err := a.Validate(ctx, request); err != nil {
		klog.Errorf("changeid %s, task name %s, cluster %s, component %s, namespace %s, name %s, validate failed",
			request.ChangeId, request.TaskName, request.ClusterId, request.Component, request.Extend.Namespace, request.Extend.Name)
		return nil, fmt.Errorf("validate failed:%v (namespace: %s, name: %s)", err, request.Extend.Namespace, request.Extend.Name)
	}

	if request.Action != util.TaskActionUpgrade {
		klog.Errorf("changeid %s, task name %s, cluster %s, component %s, namespace %s, name %s, action %s not supported",
			request.ChangeId, request.TaskName, request.ClusterId, request.Component, request.Extend.Namespace, request.Extend.Name, request.Action)
		return nil, fmt.Errorf("action %s not supported (changeid: %s, namespace: %s, name: %s)",
			request.Action, request.ChangeId, request.Extend.Namespace, request.Extend.Name)
	}

	client, err := util.GetTargetTadClient(request)
	if err != nil {
		klog.Errorf("changeid %s, task name %s, cluster %s, component %s, namespace %s, name %s, failed to get k8s client, err is %v",
			request.ChangeId, request.TaskName, request.ClusterId, request.Component, request.Extend.Namespace, request.Extend.Name, err)
		return nil, fmt.Errorf("failed to get k8s client, err is %v (namespace: %s, name: %s)", err, request.Extend.Namespace, request.Extend.Name)
	}

	app, err := client.CoreV1().Applications(request.Extend.Namespace).Get(ctx, request.Extend.Name, metav1.GetOptions{})
	if err != nil {
		klog.Errorf("changeid %s, task name %s, cluster %s, component %s, namespace %s, name %s, failed to get Application[%s/%s], err is %v",
			request.ChangeId, request.TaskName, request.ClusterId, request.Component, request.Extend.Namespace, request.Extend.Name,
			request.Extend.Namespace, request.Extend.Name, err)
		return nil, fmt.Errorf("failed to get Application[%s/%s], err is %v", request.Extend.Namespace, request.Extend.Name, err)
	}

	klog.Infof("start to update Application,request info is %+v", request)

	appInfo, err := a.ExtractAppInfo(app)
	if err != nil {
		klog.Errorf("changeid %s, task name %s, cluster %s, component %s, namespace %s, name %s, failed to extract app info: %v",
			request.ChangeId, request.TaskName, request.ClusterId, request.Component, request.Extend.Namespace, request.Extend.Name, err)
		return nil, fmt.Errorf("failed to extract app info: %v (namespace: %s, name: %s)", err, request.Extend.Namespace, request.Extend.Name)
	}

	instance, err := a.CallDescribeInstance(request.ApiEndpoint, appInfo)
	if err != nil {
		klog.Errorf("changeid %s, task name %s, cluster %s, component %s, namespace %s, name %s, failed to describe instance: %v",
			request.ChangeId, request.TaskName, request.ClusterId, request.Component, request.Extend.Namespace, request.Extend.Name, err)
		return nil, fmt.Errorf("failed to describe instance: %v (namespace: %s, name: %s)", err, request.Extend.Namespace, request.Extend.Name)
	}

	klog.Infof("start to update Application,origin yaml is :%s", instance.Response.Instance.YAML)

	clsTrait, err := a.FindCLSTrait(instance)
	if err != nil {
		klog.Warningf("changeid %s, task name %s, namespace %s, name %s, %v",
			request.ChangeId, request.TaskName, request.Extend.Namespace, request.Extend.Name, err)
		return nil, fmt.Errorf("%v (changeid: %s, namespace: %s, name: %s)",
			err, request.ChangeId, request.Extend.Namespace, request.Extend.Name)
	}

	// 根据extend 修改trait
	err = a.ModifyCLSTrait(&clsTrait, request.Extend.Patch)
	if err != nil {
		klog.Errorf("changeid %s, task name %s, namespace %s, name %s, failed to modify cls trait: %v",
			request.ChangeId, request.TaskName, request.Extend.Namespace, request.Extend.Name, err)
		return nil, fmt.Errorf("failed to modify cls trait: %v (changeid: %s, namespace: %s, name: %s)",
			err, request.ChangeId, request.Extend.Namespace, request.Extend.Name)
	}

	// 调用 ModifyTrait 接口
	if err := a.ModifyTrait(request.ApiEndpoint, instance.Response.Instance.Components[0].Name, &clsTrait, appInfo); err != nil {
		klog.Errorf("changeid %s, task name %s, namespace %s, name %s, failed to modify trait: %v",
			request.ChangeId, request.TaskName, request.Extend.Namespace, request.Extend.Name, err)
		return nil, fmt.Errorf("failed to modify trait: %v (changeid: %s, namespace: %s, name: %s)",
			err, request.ChangeId, request.Extend.Namespace, request.Extend.Name)
	}

	return nil, nil

}

// ExtractAppInfo 从 Application 对象中提取所需信息
func (a *AppFabricStrategy) ExtractAppInfo(app *tadv1.Application) (*AppInfo, error) {
	labels := app.GetLabels()
	if labels == nil {
		return nil, fmt.Errorf("application labels is empty")
	}

	// 从 annotations 中解析用户信息
	annotations := app.GetAnnotations()
	if annotations == nil {
		return nil, fmt.Errorf("application annotations is empty")
	}

	creatorInfo := annotations["app.camp.io/creator"]
	if creatorInfo == "" {
		return nil, fmt.Errorf("creator info is empty in annotations")
	}

	if err := json.Unmarshal([]byte(creatorInfo), &userInfo); err != nil {
		return nil, fmt.Errorf("failed to parse user info: %v", err)
	}

	// 检查用户信息字段
	if userInfo.OwnerUserID == "" {
		return nil, fmt.Errorf("ownerUserID is empty in creator info")
	}
	if userInfo.TenantID == "" {
		return nil, fmt.Errorf("tenantID is empty in creator info")
	}
	if userInfo.UserID == "" {
		return nil, fmt.Errorf("userID is empty in creator info")
	}

	// 转换字符串为数字并检查转换结果
	uin, err := strconv.ParseInt(userInfo.OwnerUserID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid ownerUserID format: %v", err)
	}
	appId, err := strconv.ParseInt(userInfo.TenantID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid tenantID format: %v", err)
	}
	subAccountUin, err := strconv.ParseInt(userInfo.UserID, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid userID format: %v", err)
	}

	// 检查必需的标签
	applicationID := labels["app.camp.io/application-id"]
	if applicationID == "" {
		return nil, fmt.Errorf("application-id label is empty")
	}

	projectID := labels["app.camp.io/project-id"]
	if projectID == "" {
		return nil, fmt.Errorf("project-id label is empty")
	}

	environmentName := labels["env.tad.io/name"]
	if environmentName == "" {
		return nil, fmt.Errorf("environment name label is empty")
	}

	instanceID := labels["app.camp.io/id"]
	if instanceID == "" {
		return nil, fmt.Errorf("instance-id label is empty")
	}

	return &AppInfo{
		ApplicationID:   applicationID,
		ProjectID:       projectID,
		EnvironmentName: environmentName,
		InstanceID:      instanceID,
		Uin:             uin,
		AppId:           appId,
		SubAccountUin:   subAccountUin,
	}, nil
}

// CallDescribeInstance 调用 DescribeInstance 接口
func (a *AppFabricStrategy) CallDescribeInstance(endpoint string, appInfo *AppInfo) (*DescribeInstanceResponse, error) {
	url := fmt.Sprintf("http://%s", endpoint)

	// 构建请求体
	requestBody := DescribeInstanceRequest{
		BaseRequest: BaseRequest{
			Action:          "DescribeInstance",
			ApiModule:       ApiModule,
			ApplicationID:   appInfo.ApplicationID,
			ProjectID:       appInfo.ProjectID,
			EnvironmentName: appInfo.EnvironmentName,
			InstanceID:      appInfo.InstanceID,
			Uin:             appInfo.Uin,
			AppId:           appInfo.AppId,
			SubAccountUin:   appInfo.SubAccountUin,
			Version:         "2022-09-20",
		},
	}

	body, err := json.Marshal(requestBody)
	if err != nil {
		return &DescribeInstanceResponse{}, fmt.Errorf("failed to marshal request body: %v", err)
	}

	// 发送请求
	respBody, statusCode, err := http_util.PostRequest(url, body, nil, 10*time.Second)
	if err != nil {
		return &DescribeInstanceResponse{}, fmt.Errorf("failed to send request: %v", err)
	}

	if statusCode != http.StatusOK {
		return &DescribeInstanceResponse{}, fmt.Errorf("received non-OK status code: %d", statusCode)
	}

	klog.Infof("DescribeInstance response: %s", string(respBody))

	var resp DescribeInstanceResponse
	if err := json.Unmarshal(respBody, &resp); err != nil {
		return &DescribeInstanceResponse{}, fmt.Errorf("failed to unmarshal response body: %v", err)
	}
	return &resp, nil
}

// FindCLSTrait 在实例的组件中查找 CLS trait
func (a *AppFabricStrategy) FindCLSTrait(instance *DescribeInstanceResponse) (types.Trait, error) {
	var clsTrait types.Trait
	for _, comp := range instance.Response.Instance.Components {
		for _, trait := range comp.Traits {
			if trait.Type == "cls" {
				return trait, nil
			}
		}
	}
	return clsTrait, fmt.Errorf("no CLS traits found in components")
}

// ModifyCLSTrait 根据 extend.Patch 修改 clsTrait.Properties.CLS 对象
func (a *AppFabricStrategy) ModifyCLSTrait(clsTrait *types.Trait, patches []model.Patch) error {
	clsObjects := clsTrait.Properties.CLS.Objects

	for _, patch := range patches {
		key := patch.Key
		value := patch.Value
		scope := patch.Scope
		originData := patch.OriginData

		// 遍历所有 CLS 对象
		for objIdx := range clsObjects {
			obj := &clsObjects[objIdx] // 使用指针直接修改原对象

			// 根据 scope 决定处理哪些 details
			details := obj.Details
			if scope != "all" && len(details) > 0 {
				details = details[:1] // 只处理第一个 detail
			}

			for detailIdx := range details {
				detail := &details[detailIdx]

				// 根据 key 处理不同字段
				handleError := func(originalErr error, fieldType string) error {
					// 保留原始错误类型，同时添加上下文信息
					return fmt.Errorf("%s %s: %w", fieldType, key, originalErr)
				}

				switch key {
				case CLSKeyUnMatchUpload, CLSKeyUnMatchedKey, CLSKeyJsonStandard, CLSKeyLogType,
					CLSKeyBacktracking, CLSKeyIsGBK, CLSKeyTimeKey, CLSKeyTimeFormat:
					// 统一处理所有需要checkAndSet的字段
					var targetField *string
					if detail.Output.CLS == nil {
						return fmt.Errorf("CLS configuration is missing for key %s", key)
					}
					if er := detail.Output.CLS.ExtractRule; er != nil {
						switch key {
						case CLSKeyUnMatchUpload:
							targetField = &er.UnMatchUpload
						case CLSKeyUnMatchedKey:
							targetField = &er.UnMatchedKey
						case CLSKeyJsonStandard:
							targetField = &er.JsonStandard
						case CLSKeyLogType:
							targetField = &detail.Output.CLS.LogType
						case CLSKeyBacktracking:
							targetField = &er.Backtracking
						case CLSKeyIsGBK:
							targetField = &er.IsGBK
						case CLSKeyTimeKey:
							targetField = &er.TimeKey
						case CLSKeyTimeFormat:
							targetField = &er.TimeFormat
						}
					}
					if targetField != nil {
						if err := checkAndSet(targetField, value, originData); err != nil {
							return handleError(err, "CLS field")
						}
					}

				case CLSKeyMetadataContainer, CLSKeyMetadataLabels:
					// 统一处理metadata相关字段
					var targetSlice *[]string
					switch key {
					case CLSKeyMetadataContainer:
						targetSlice = &detail.Input.MetadataContainer
					case CLSKeyMetadataLabels:
						targetSlice = &detail.Input.MetadataLabels
					}

					if err := updateMetadataSlice(targetSlice, value, originData); err != nil {
						return handleError(err, "metadata")
					}

				default:
					return fmt.Errorf("%w: %s", ErrInvalidKey, key)
				}
				obj.Details[detailIdx] = *detail
			}
		}
	}
	return nil
}

func checkAndSet(current *string, newVal string, originData string) error {
	if originData != "" {
		if originData == "__nil__" {
			if *current != "" {
				return ErrOriginNotMatch
			}
		} else {
			if *current != originData {
				return ErrOriginNotMatch
			}
		}
	}
	if *current == newVal {
		return ErrNoChangeNeeded
	}
	*current = newVal
	return nil
}

func updateMetadataSlice(target *[]string, value, originData string) error {
	// 如果提供了originData，先验证原始值是否匹配

	if originData != "" {
		if originData == "__nil__" {
			if *target != nil {
				return ErrOriginNotMatch
			}
		} else {
			originValues := strings.Split(originData, ",")
			for i := range originValues {
				originValues[i] = strings.TrimSpace(originValues[i])
			}

			if !util.CompareStringSlices(*target, originValues) {
				return ErrOriginNotMatch
			}
		}
	}

	newValues := strings.Split(value, ",")
	for i := range newValues {
		newValues[i] = strings.TrimSpace(newValues[i])
	}

	// 检查新值是否与当前值相同
	if util.CompareStringSlices(*target, newValues) {
		return ErrNoChangeNeeded
	}

	if value == "" {
		*target = nil
	} else {
		*target = newValues
	}
	return nil
}

func (a *AppFabricStrategy) ModifyTrait(endpoint string, componentName string, clsTrait *types.Trait, appInfo *AppInfo) error {
	url := fmt.Sprintf("http://%s", endpoint)

	// 构建 ModifyTrait 请求
	modifyReq := &ModifyTraitRequest{
		BaseRequest: BaseRequest{
			Action:          "ModifyTrait",
			ApiModule:       ApiModule,
			ApplicationID:   appInfo.ApplicationID,
			ProjectID:       appInfo.ProjectID,
			EnvironmentName: appInfo.EnvironmentName,
			InstanceID:      appInfo.InstanceID,
			Uin:             appInfo.Uin,
			AppId:           appInfo.AppId,
			SubAccountUin:   appInfo.SubAccountUin,
			Version:         "2022-09-20",
		},
		ComponentName: componentName, // 根据实际情况设置
		Trait:         *clsTrait,
	}

	body, err := json.Marshal(modifyReq)
	if err != nil {
		return fmt.Errorf("failed to marshal request body: %v", err)
	}

	// 发送请求
	respBody, statusCode, err := http_util.PostRequest(url, body, nil, 10*time.Second)
	if err != nil {
		return fmt.Errorf("failed to send request: %v", err)
	}

	if statusCode != http.StatusOK {
		return fmt.Errorf("received non-OK status code: %d", statusCode)
	}

	klog.Infof("ModifyTrait response: %s", string(respBody))
	return nil
}

func (a *AppFabricStrategy) Rollback(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	return nil, fmt.Errorf("not implement")
}
