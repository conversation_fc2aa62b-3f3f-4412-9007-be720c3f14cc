package appfabricapplication_test

import (
	"errors"
	"fmt"
	"strings"
	"testing"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"git.woa.com/kamp/camp-api/domain/server/yunapi/types"
	"git.woa.com/kmetis/starship/pkg/model"
	"git.woa.com/kmetis/starship/pkg/strategy/appfabricapplication"
	"git.woa.com/kmetis/starship/pkg/util"
	tadv1 "git.woa.com/tad-dev/api/apis/tad/v1"
)

func TestCallDescribeInstance(t *testing.T) {
	// 创建测试用的模拟Application对象
	mockApp := &tadv1.Application{
		ObjectMeta: metav1.ObjectMeta{
			Labels: map[string]string{
				"app.camp.io/application-id": "app-pmbl2cd8",
				"app.camp.io/project-id":     "prj-q7clfnct",
				"env.tad.io/name":            "development",
				"app.camp.io/id":             "tad-6cxrlsrx",
			},
			Annotations: map[string]string{
				"core.camp.io/creator": `{
					"userID": "100040634284",
					"tenantID": "1251316161",
					"ownerUserID": "2252646423",
					"name": "blakeke"
				}`,
			},
		},
	}

	// 创建策略实例
	strategy := &appfabricapplication.AppFabricStrategy{}
	// 提取应用信息

	appInfo, err := strategy.ExtractAppInfo(mockApp)
	if err != nil {
		t.Fatalf("提取应用信息失败: %v", err)
	}

	// 调用接口
	instance, err := strategy.CallDescribeInstance("**************", appInfo)
	if err != nil {
		t.Fatalf("调用DescribeInstance失败: %v", err)
	}
	fmt.Println(instance)
}

func TestAppFabricStrategy_ModifyTrait(t *testing.T) {

	mockApp := &tadv1.Application{
		ObjectMeta: metav1.ObjectMeta{
			Labels: map[string]string{
				"app.camp.io/application-id": "app-pmbl2cd8",
				"app.camp.io/project-id":     "prj-q7clfnct",
				"env.tad.io/name":            "development",
				"app.camp.io/id":             "tad-6cxrlsrx",
			},
			Annotations: map[string]string{
				"app.camp.io/creator": `{
					"userID": "100040634284",
					"tenantID": "1251316161",
					"ownerUserID": "2252646423",
					"name": "blakeke"
				}`,
			},
		},
	}

	// 创建策略实例
	strategy := &appfabricapplication.AppFabricStrategy{}
	// 提取应用信息

	appInfo, err := strategy.ExtractAppInfo(mockApp)
	if err != nil {
		t.Fatalf("提取应用信息失败: %v", err)
	}

	instance, err := strategy.CallDescribeInstance("**************", appInfo)
	if err != nil {
		t.Fatalf("调用DescribeInstance失败: %v", err)
	}
	clsTrait, err := strategy.FindCLSTrait(instance)
	if err != nil {
		panic(err)
	}
	clsTrait.Properties.CLS.Objects[0].Details[0].Input.LogPath = "/var/log/2.log"

	// 调用 ModifyTrait 接口
	if err := strategy.ModifyTrait("**************", "nginx", &clsTrait, appInfo); err != nil {
		panic(err)
	}
}

func createTestCLS() *types.CLS {
	return &types.CLS{
		Details: []struct {
			Input struct {
				Container         string `validate:"required"`
				LogPath           string
				FilePattern       string
				Stdout            bool
				FilePaths         []types.FilePath
				MetadataLabels    []string
				MetadataContainer []string
			}
			Output struct {
				CLS *types.CLSOutput
			}
		}{
			{
				Input: struct {
					Container         string `validate:"required"`
					LogPath           string
					FilePattern       string
					Stdout            bool
					FilePaths         []types.FilePath
					MetadataLabels    []string
					MetadataContainer []string
				}(struct {
					Container         string
					LogPath           string
					FilePattern       string
					Stdout            bool
					FilePaths         []types.FilePath
					MetadataLabels    []string
					MetadataContainer []string
				}{
					Container:         "test-container",
					LogPath:           "/var/log/1.log",
					FilePattern:       "*.log",
					Stdout:            false,
					FilePaths:         []types.FilePath{{Path: "/var/log", File: "1.log"}},
					MetadataLabels:    []string{"label1"},
					MetadataContainer: []string{"existing-container"},
				}),
				Output: struct {
					CLS *types.CLSOutput
				}{
					CLS: &types.CLSOutput{
						CLSOutputDetail: types.CLSOutputDetail{
							ExtractRule: &types.ExtractRule{
								UnMatchUpload: "original-value",
								UnMatchedKey:  "original-match",
								TimeFormat:    "default-format",
							},
							LogType: "original-log-type",
						},
					},
				},
			},
			{
				Input: struct {
					Container         string `validate:"required"`
					LogPath           string
					FilePattern       string
					Stdout            bool
					FilePaths         []types.FilePath
					MetadataLabels    []string
					MetadataContainer []string
				}(struct {
					Container         string
					LogPath           string
					FilePattern       string
					Stdout            bool
					FilePaths         []types.FilePath
					MetadataLabels    []string
					MetadataContainer []string
				}{
					Container:         "second-container",
					LogPath:           "/var/log/2.log",
					FilePattern:       "*.log",
					Stdout:            false,
					FilePaths:         []types.FilePath{{Path: "/var/log", File: "2.log"}},
					MetadataLabels:    []string{"label2"},
					MetadataContainer: []string{"second-container"},
				}),
				Output: struct {
					CLS *types.CLSOutput
				}{
					CLS: &types.CLSOutput{
						CLSOutputDetail: types.CLSOutputDetail{
							ExtractRule: &types.ExtractRule{
								Backtracking: "original-backtrack",
							},
							LogType: "second-log-type",
						},
					},
				},
			},
			{
				Input: struct {
					Container         string `validate:"required"`
					LogPath           string
					FilePattern       string
					Stdout            bool
					FilePaths         []types.FilePath
					MetadataLabels    []string
					MetadataContainer []string
				}(struct {
					Container         string
					LogPath           string
					FilePattern       string
					Stdout            bool
					FilePaths         []types.FilePath
					MetadataLabels    []string
					MetadataContainer []string
				}{
					Container:         "null-container",
					LogPath:           "/var/log/3.log",
					FilePattern:       "*.log",
					Stdout:            false,
					FilePaths:         []types.FilePath{{Path: "/var/log", File: "3.log"}},
					MetadataLabels:    []string{"label3"},
					MetadataContainer: []string{"__NULL__"},
				}),
				Output: struct {
					CLS *types.CLSOutput
				}{
					CLS: &types.CLSOutput{
						CLSOutputDetail: types.CLSOutputDetail{
							ExtractRule: &types.ExtractRule{
								UnMatchUpload: "original-value",
								TimeFormat:    "default-format",
							},
							LogType: "original-log-type",
						},
					},
				},
			},
		},
	}
}

func createTestCLS1() *types.CLS {
	return &types.CLS{
		Details: []struct {
			Input struct {
				Container         string `validate:"required"`
				LogPath           string
				FilePattern       string
				Stdout            bool
				FilePaths         []types.FilePath
				MetadataLabels    []string
				MetadataContainer []string
			}
			Output struct {
				CLS *types.CLSOutput
			}
		}{
			{
				Input: struct {
					Container         string `validate:"required"`
					LogPath           string
					FilePattern       string
					Stdout            bool
					FilePaths         []types.FilePath
					MetadataLabels    []string
					MetadataContainer []string
				}(struct {
					Container         string
					LogPath           string
					FilePattern       string
					Stdout            bool
					FilePaths         []types.FilePath
					MetadataLabels    []string
					MetadataContainer []string
				}{
					Container:         "null-container",
					LogPath:           "/var/log/3.log",
					FilePattern:       "*.log",
					Stdout:            false,
					FilePaths:         []types.FilePath{{Path: "/var/log", File: "3.log"}},
					MetadataLabels:    []string{"label3"},
					MetadataContainer: []string{"__NULL__"},
				}),
				Output: struct {
					CLS *types.CLSOutput
				}{
					CLS: &types.CLSOutput{
						CLSOutputDetail: types.CLSOutputDetail{
							ExtractRule: &types.ExtractRule{
								UnMatchUpload: "original-value",
								TimeFormat:    "default-format",
							},
							LogType: "original-log-type",
						},
					},
				},
			},
		},
	}
}

func TestModifyCLSTrait_NormalCases(t *testing.T) {
	strategy := &appfabricapplication.AppFabricStrategy{}

	t.Run("Should modify single field", func(t *testing.T) {
		cls := createTestCLS()
		patches := []model.Patch{
			{
				Key:   "UnMatchUpload",
				Value: "new-value",
			},
		}

		err := strategy.ModifyCLSTrait(
			&types.Trait{
				Properties: &types.TraitProperties{
					CLS: &types.CLSTrait{
						Objects: []types.CLS{*cls}}}}, patches)
		if err != nil {
			t.Fatalf("Unexpected error: %v", err)
		}

		if cls.Details[0].Output.CLS.ExtractRule.UnMatchUpload != "new-value" {
			t.Errorf("UnMatchUpload not updated, got: %s", cls.Details[0].Output.CLS.ExtractRule.UnMatchUpload)
		}
	})

	t.Run("Should modify metadata container with multiple values", func(t *testing.T) {
		cls := createTestCLS()
		patches := []model.Patch{
			{
				Key:   "MetadataContainer",
				Value: "new,values,with spaces ",
			},
		}

		err := strategy.ModifyCLSTrait(&types.Trait{
			Properties: &types.TraitProperties{CLS: &types.CLSTrait{Objects: []types.CLS{*cls}}}}, patches)
		if err != nil {
			t.Fatalf("Unexpected error: %v", err)
		}

		expected := []string{"new", "values", "with spaces"}
		if !util.CompareStringSlices(cls.Details[0].Input.MetadataContainer, expected) {
			t.Errorf("MetadataContainer not updated properly, got: %v", cls.Details[0].Input.MetadataContainer)
		}
	})

	t.Run("Should modify all details when scope is all", func(t *testing.T) {
		cls := createTestCLS()
		patches := []model.Patch{
			{
				Key:   "LogType",
				Value: "new-log-type",
				Scope: "all",
			},
		}

		err := strategy.ModifyCLSTrait(&types.Trait{Properties: &types.TraitProperties{CLS: &types.CLSTrait{Objects: []types.CLS{*cls}}}}, patches)
		if err != nil {
			t.Fatalf("Unexpected error: %v", err)
		}

		for _, detail := range cls.Details {
			if detail.Output.CLS.LogType != "new-log-type" {
				t.Errorf("LogType not updated in all details, got: %s", detail.Output.CLS.LogType)
			}
		}
	})

	t.Run("Should set UnMatchedKey to empty string", func(t *testing.T) {
		cls := createTestCLS()
		patches := []model.Patch{
			{
				Key:   "UnMatchedKey",
				Value: "",
			},
		}

		err := strategy.ModifyCLSTrait(&types.Trait{Properties: &types.TraitProperties{CLS: &types.CLSTrait{Objects: []types.CLS{*cls}}}}, patches)
		if err != nil {
			t.Fatalf("Unexpected error: %v", err)
		}

		if cls.Details[0].Output.CLS.ExtractRule.UnMatchedKey != "" {
			t.Errorf("UnMatchedKey not set to empty string, got: %s", cls.Details[0].Output.CLS.ExtractRule.UnMatchedKey)
		}
	})
}

func TestModifyCLSTrait_EdgeCases(t *testing.T) {
	strategy := &appfabricapplication.AppFabricStrategy{}

	t.Run("Should return error for invalid key", func(t *testing.T) {
		cls := createTestCLS()
		patches := []model.Patch{
			{
				Key:   "InvalidKey",
				Value: "some-value",
			},
		}

		err := strategy.ModifyCLSTrait(&types.Trait{Properties: &types.TraitProperties{CLS: &types.CLSTrait{Objects: []types.CLS{*cls}}}}, patches)
		if err == nil {
			t.Fatal("Expected error but got none")
		}

		if !strings.Contains(err.Error(), "unsupported key") {
			t.Errorf("Unexpected error message: %v", err)
		}
	})

	t.Run("Should return no modification error", func(t *testing.T) {
		cls := createTestCLS()
		patches := []model.Patch{
			{
				Key:   "UnMatchUpload",
				Value: "original-value", // Same as existing value
			},
		}

		err := strategy.ModifyCLSTrait(&types.Trait{Properties: &types.TraitProperties{CLS: &types.CLSTrait{Objects: []types.CLS{*cls}}}}, patches)
		if !errors.Is(err, appfabricapplication.ErrNoChangeNeeded) {
			t.Errorf("Expected ErrValueSame, got: %v", err)
		}
	})

	t.Run("Should respect originData validation", func(t *testing.T) {
		cls := createTestCLS()
		originalValue := cls.Details[0].Output.CLS.ExtractRule.UnMatchUpload
		patches := []model.Patch{
			{
				Key:        "UnMatchUpload",
				Value:      "new-value",
				OriginData: "wrong-origin-data",
			},
		}

		err := strategy.ModifyCLSTrait(&types.Trait{Properties: &types.TraitProperties{CLS: &types.CLSTrait{Objects: []types.CLS{*cls}}}}, patches)
		if !errors.Is(err, appfabricapplication.ErrOriginNotMatch) {
			t.Errorf("Expected ErrOriginNotMatch, got: %v", err)
		}

		if cls.Details[0].Output.CLS.ExtractRule.UnMatchUpload != originalValue {
			t.Error("Value should not be modified with wrong originData")
		}
	})
}

func TestModifyCLSTrait_MetadataCases(t *testing.T) {
	strategy := &appfabricapplication.AppFabricStrategy{}

	t.Run("Should initialize nil metadata slices", func(t *testing.T) {
		cls := createTestCLS()
		cls.Details[0].Input.MetadataLabels = nil
		patches := []model.Patch{
			{
				Key:   "MetadataLabels",
				Value: "new-label",
			},
		}

		err := strategy.ModifyCLSTrait(&types.Trait{Properties: &types.TraitProperties{CLS: &types.CLSTrait{Objects: []types.CLS{*cls}}}}, patches)
		if err != nil {
			t.Fatalf("Unexpected error: %v", err)
		}

		if cls.Details[0].Input.MetadataLabels == nil {
			t.Fatal("MetadataLabels should be initialized")
		}
	})

	t.Run("Should handle multiple metadata values with trimming", func(t *testing.T) {
		cls := createTestCLS()
		patches := []model.Patch{
			{
				Key:   "MetadataLabels",
				Value: "label2,label3",
			},
		}

		err := strategy.ModifyCLSTrait(&types.Trait{Properties: &types.TraitProperties{CLS: &types.CLSTrait{Objects: []types.CLS{*cls}}}}, patches)
		if err != nil {
			t.Fatalf("Unexpected error: %v", err)
		}

		expected := []string{"label2", "label3"}
		if !util.CompareStringSlices(cls.Details[0].Input.MetadataLabels, expected) {
			t.Errorf("MetadataLabels not updated properly, got: %v", cls.Details[0].Input.MetadataLabels)
		}
	})

	t.Run("Should update MetadataContainer with originData match", func(t *testing.T) {
		cls := createTestCLS()
		originData := "existing-container"
		patches := []model.Patch{
			{
				Key:        "MetadataContainer",
				Value:      "new-container",
				OriginData: originData,
			},
		}

		err := strategy.ModifyCLSTrait(&types.Trait{Properties: &types.TraitProperties{CLS: &types.CLSTrait{Objects: []types.CLS{*cls}}}}, patches)
		if err != nil {
			t.Fatalf("Unexpected error: %v", err)
		}

		expected := []string{"new-container"}
		if !util.CompareStringSlices(cls.Details[0].Input.MetadataContainer, expected) {
			t.Errorf("MetadataContainer not updated properly, got: %v", cls.Details[0].Input.MetadataContainer)
		}
	})

	t.Run("Should not update MetadataContainer with originData mismatch", func(t *testing.T) {
		cls := createTestCLS()
		originData := "wrong-origin-data"
		patches := []model.Patch{
			{
				Key:        "MetadataContainer",
				Value:      "new-container",
				OriginData: originData,
			},
		}

		err := strategy.ModifyCLSTrait(&types.Trait{Properties: &types.TraitProperties{CLS: &types.CLSTrait{Objects: []types.CLS{*cls}}}}, patches)
		if !errors.Is(err, appfabricapplication.ErrOriginNotMatch) {
			t.Errorf("Expected ErrOriginNotMatch, got: %v", err)
		}

		expected := []string{"existing-container"}
		if !util.CompareStringSlices(cls.Details[0].Input.MetadataContainer, expected) {
			t.Errorf("MetadataContainer should not be updated, got: %v", cls.Details[0].Input.MetadataContainer)
		}
	})

	t.Run("Should update MetadataLabels with originData match", func(t *testing.T) {
		cls := createTestCLS()
		originData := "label1"
		patches := []model.Patch{
			{
				Key:        "MetadataLabels",
				Value:      "new-label",
				OriginData: originData,
			},
		}

		err := strategy.ModifyCLSTrait(&types.Trait{Properties: &types.TraitProperties{CLS: &types.CLSTrait{Objects: []types.CLS{*cls}}}}, patches)
		if err != nil {
			t.Fatalf("Unexpected error: %v", err)
		}

		expected := []string{"new-label"}
		if !util.CompareStringSlices(cls.Details[0].Input.MetadataLabels, expected) {
			t.Errorf("MetadataLabels not updated properly, got: %v", cls.Details[0].Input.MetadataLabels)
		}
	})

	t.Run("Should not update MetadataLabels with originData mismatch", func(t *testing.T) {
		cls := createTestCLS()
		originData := "wrong-origin-data"
		patches := []model.Patch{
			{
				Key:        "MetadataLabels",
				Value:      "new-label",
				OriginData: originData,
			},
		}

		err := strategy.ModifyCLSTrait(&types.Trait{Properties: &types.TraitProperties{CLS: &types.CLSTrait{Objects: []types.CLS{*cls}}}}, patches)
		if !errors.Is(err, appfabricapplication.ErrOriginNotMatch) {
			t.Errorf("Expected ErrOriginNotMatch, got: %v", err)
		}

		expected := []string{"label1"}
		if !util.CompareStringSlices(cls.Details[0].Input.MetadataLabels, expected) {
			t.Errorf("MetadataLabels should not be updated, got: %v", cls.Details[0].Input.MetadataLabels)
		}
	})

	t.Run("Should set MetadataContainer to nil when value is __NULL__", func(t *testing.T) {
		cls := createTestCLS1()
		patches := []model.Patch{
			{
				Key:        "MetadataContainer",
				OriginData: "__NULL__",
				Scope:      "all",
			},
		}

		err := strategy.ModifyCLSTrait(&types.Trait{Properties: &types.TraitProperties{CLS: &types.CLSTrait{Objects: []types.CLS{*cls}}}}, patches)
		if err != nil {
			t.Fatalf("Unexpected error: %v", err)
		}

		if cls.Details[0].Input.MetadataContainer != nil {
			t.Errorf("MetadataContainer should be nil, got: %v", cls.Details[0].Input.MetadataContainer)
		}
	})
}
