package appfabricapplication

import (
	camp "git.woa.com/kamp/camp-api/domain/server/yunapi/handlers"
	"git.woa.com/kamp/camp-api/domain/server/yunapi/types"
)

type AppInfo struct {
	ApplicationID   string `json:"application_id"`
	ProjectID       string `json:"project_id"`
	EnvironmentName string `json:"environment_name"`
	InstanceID      string `json:"instance_id"`
	Uin             int64  `json:"uin"`
	AppId           int64  `json:"app_id"`
	SubAccountUin   int64  `json:"sub_account_uin"`
}

var userInfo struct {
	UserID      string `json:"userID"`
	TenantID    string `json:"tenantID"`
	OwnerUserID string `json:"ownerUserID"`
	Name        string `json:"name"`
}

type BaseRequest struct {
	Action          string `json:"Action"`
	ApiModule       string `json:"ApiModule"`
	ApplicationID   string `json:"ApplicationID"`
	ProjectID       string `json:"ProjectID"`
	EnvironmentName string `json:"EnvironmentName"`
	InstanceID      string `json:"InstanceID"`
	Uin             int64  `json:"Uin"`
	AppId           int64  `json:"AppId"`
	SubAccountUin   int64  `json:"SubAccountUin"`
	Version         string `json:"Version"`
}

type DescribeInstanceRequest struct {
	BaseRequest
}

type ModifyTraitRequest struct {
	BaseRequest
	ComponentName string      `json:"ComponentName"`
	Trait         types.Trait `json:"Trait"`
}

type DescribeInstanceResponse struct {
	Response camp.DescribeInstanceResponse `json:"Response"`
}
