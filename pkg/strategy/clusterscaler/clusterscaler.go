package clusterscaler

import (
	"context"
	"encoding/json"
	"fmt"

	"k8s.io/apimachinery/pkg/types"
	"k8s.io/klog"

	kmetisclientset "git.woa.com/kmetis/kmetis/pkg/client/clientset/versioned"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/kmetis/starship/pkg/util/kubeclient"
)

const (
	ProviderName = "clusterscaler"
)

func init() {
	strategyprovider.RegisterReleaseStrategyFactory(
		ProviderName,
		func(ctx *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
			return NewClusterScalerStrategy(ctx)
		},
	)
}

type ClusterScalerStrategy struct {
	cfg *strategyprovider.ReleaseConfig
}

// NewClusterScalerStrategy returns new instance of a ClusterScalerStrategy
func NewClusterScalerStrategy(ctx *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
	return &ClusterScalerStrategy{cfg: ctx}, nil
}

func (c *ClusterScalerStrategy) Validate(ctx context.Context, request *strategyprovider.TaskStrategyRequest) error {
	// 检查 ExtendInfo 中是否包含 annotations
	if request.Extend == nil || len(request.Extend.Annotations) == 0 {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,annotations is empty in ExtendInfo, %s",
			request.ChangeId, request.TaskName, request.ClusterId, request.Component, request.TraceId)
		return fmt.Errorf("annotations is empty in ExtendInfo")
	}
	// 不进行通用检查
	// return util.ValidateTaskStrategyRequest(request)
	return nil
}

func (c *ClusterScalerStrategy) Exec(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	klog.Infof("start to execute clusterscaler strategy, changeId:%s, traceId:%s, cluster:%s, component:%s",
		request.ChangeId, request.TraceId, request.ClusterId, request.Component)

	// 验证请求参数
	if err := c.Validate(ctx, request); err != nil {
		klog.Errorf("validate request failed, changeId:%s, err:%v", request.ChangeId, err)
		return &strategyprovider.TaskStrategyReply{
			Code:   "FAIL",
			Detail: fmt.Sprintf("validate request failed: %v", err),
		}, err
	}

	// 获取 kmetisclient
	kmetisClient, err := c.getKmetisClient(request)
	if err != nil {
		klog.Errorf("failed to get kmetis client, changeId:%s, err:%v", request.ChangeId, err)
		return &strategyprovider.TaskStrategyReply{
			Code:   "ERROR",
			Detail: fmt.Sprintf("failed to get kmetis client: %v", err),
		}, err
	}

	// 执行 patch 操作
	if err := c.patchClusterScaler(ctx, kmetisClient, request); err != nil {
		klog.Errorf("failed to patch clusterscaler, changeId:%s, err:%v", request.ChangeId, err)
		return &strategyprovider.TaskStrategyReply{
			Code:   "ERROR",
			Detail: fmt.Sprintf("failed to patch clusterscaler: %v", err),
		}, err
	}

	klog.Infof("finish to execute clusterscaler strategy, changeId:%s, traceId:%s, cluster:%s, component:%s",
		request.ChangeId, request.TraceId, request.ClusterId, request.Component)

	return &strategyprovider.TaskStrategyReply{
		Code:   "PASS",
		Detail: "clusterscaler patched successfully",
	}, nil
}

func (c *ClusterScalerStrategy) Rollback(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	return nil, fmt.Errorf("not implement")
}

// getKmetisClient 根据集群信息获取 kmetisclient
func (c *ClusterScalerStrategy) getKmetisClient(request *strategyprovider.TaskStrategyRequest) (kmetisclientset.Interface, error) {
	// 获取 meta 集群的 rest config
	productName := request.ProductName
	// eks集群的meta集群是tke集群，因此需要将productName转换为tke
	if request.ProductName == "eks" {
		productName = "tke"
	}

	restConfig, err := kubeclient.GetRestConfig(request.MetaClusterId, productName)
	if err != nil {
		return nil, fmt.Errorf("failed to get rest config for meta cluster %s: %v", request.MetaClusterId, err)
	}

	// 创建 kmetisclient
	kmetisClient, err := kmetisclientset.NewForConfig(restConfig)
	if err != nil {
		return nil, fmt.Errorf("failed to create kmetis client: %v", err)
	}

	return kmetisClient, nil
}

// patchClusterScaler 执行 clusterscaler 的 patch 操作
func (c *ClusterScalerStrategy) patchClusterScaler(ctx context.Context, kmetisClient kmetisclientset.Interface, request *strategyprovider.TaskStrategyRequest) error {
	clusterId := request.ClusterId
	namespace := clusterId         // 集群 ID 作为命名空间名称
	clusterScalerName := clusterId // 同名 clusterscaler 对象

	// 构建 patch annotations
	patchAnnotations, err := util.BuildPatchAnnotations(request.Extend.Annotations)
	if err != nil {
		return fmt.Errorf("failed to build patch annotations: %v", err)
	}

	// 构建 patch 数据
	patchData := map[string]interface{}{
		"metadata": map[string]interface{}{
			"annotations": patchAnnotations,
		},
	}

	patchBytes, err := json.Marshal(patchData)
	if err != nil {
		return fmt.Errorf("failed to marshal patch data: %v", err)
	}

	// 执行 patch 操作
	_, err = kmetisClient.AutopilotV1beta1().ClusterScalers(namespace).Patch(
		clusterScalerName,
		types.MergePatchType,
		patchBytes,
	)
	if err != nil {
		return fmt.Errorf("failed to patch clusterscaler %s in namespace %s: %v", clusterScalerName, namespace, err)
	}

	return nil
}
