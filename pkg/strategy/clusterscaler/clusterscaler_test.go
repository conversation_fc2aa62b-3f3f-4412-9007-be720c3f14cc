package clusterscaler

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"

	autopilotv1beta1 "git.woa.com/kmetis/kmetis/pkg/apis/autopilot.k8s.io/v1beta1"
	kmetisfake "git.woa.com/kmetis/kmetis/pkg/client/clientset/versioned/fake"
	"git.woa.com/kmetis/starship/pkg/model"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
)

func TestClusterScalerStrategy_Exec(t *testing.T) {
	tests := []struct {
		name           string
		request        *strategyprovider.TaskStrategyRequest
		existingObjs   []runtime.Object
		expectedError  bool
		expectedCode   string
		expectedDetail string
	}{
		{
			name: "successful patch with annotations",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:     "cls-test123",
				MetaClusterId: "cls-meta123",
				ProductName:   "tke",
				ChangeId:      "change-123",
				TraceId:       "trace-123",
				Component:     "test-component",
				Extend: &model.ExtendInfo{
					Annotations: []map[string]string{
						{"type": "update", "key": "test-key", "value": "test-value"},
						{"type": "update", "key": "another-key", "value": "another-value"},
					},
				},
			},
			existingObjs: []runtime.Object{
				&autopilotv1beta1.ClusterScaler{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cls-test123",
						Namespace: "cls-test123",
						Annotations: map[string]string{
							"existing-key": "existing-value",
						},
					},
					Spec: autopilotv1beta1.ClusterScalerSpec{
						ClusterId: "cls-test123",
					},
				},
			},
			expectedError:  false,
			expectedCode:   "PASS",
			expectedDetail: "clusterscaler patched successfully",
		},
		{
			name: "missing annotations in ExtendInfo",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:     "cls-test123",
				MetaClusterId: "cls-meta123",
				ProductName:   "tke",
				ChangeId:      "change-123",
				TraceId:       "trace-123",
				Component:     "test-component",
				Extend:        &model.ExtendInfo{},
			},
			existingObjs:   []runtime.Object{},
			expectedError:  true,
			expectedCode:   "FAIL",
			expectedDetail: "annotations is empty in ExtendInfo",
		},
		{
			name: "nil ExtendInfo",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:     "cls-test123",
				MetaClusterId: "cls-meta123",
				ProductName:   "tke",
				ChangeId:      "change-123",
				TraceId:       "trace-123",
				Component:     "test-component",
				Extend:        nil,
			},
			existingObjs:   []runtime.Object{},
			expectedError:  true,
			expectedCode:   "FAIL",
			expectedDetail: "annotations is empty in ExtendInfo",
		},
		{
			name: "eks product name conversion",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:     "cls-test123",
				MetaClusterId: "cls-meta123",
				ProductName:   "eks", // should be converted to tke for meta cluster
				ChangeId:      "change-123",
				TraceId:       "trace-123",
				Component:     "test-component",
				Extend: &model.ExtendInfo{
					Annotations: []map[string]string{
						{"type": "update", "key": "test-key", "value": "test-value"},
					},
				},
			},
			existingObjs: []runtime.Object{
				&autopilotv1beta1.ClusterScaler{
					ObjectMeta: metav1.ObjectMeta{
						Name:      "cls-test123",
						Namespace: "cls-test123",
					},
					Spec: autopilotv1beta1.ClusterScalerSpec{
						ClusterId: "cls-test123",
					},
				},
			},
			expectedError:  false,
			expectedCode:   "PASS",
			expectedDetail: "clusterscaler patched successfully",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// 创建 fake kmetis client
			fakeClient := kmetisfake.NewSimpleClientset(tt.existingObjs...)

			// 创建 strategy 实例
			strategy := &ClusterScalerStrategy{}

			// 由于我们无法轻易 mock kubeclient.GetRestConfig，我们需要修改测试方法
			// 这里我们直接测试 patchClusterScaler 方法
			if !tt.expectedError || (tt.expectedError && tt.expectedCode != "FAIL") {
				// 只有当不是参数验证错误时才测试 patch 逻辑
				err := strategy.patchClusterScaler(context.Background(), fakeClient, tt.request)
				if tt.expectedError {
					assert.Error(t, err)
				} else {
					assert.NoError(t, err)

					// 验证 patch 是否成功应用
					clusterScaler, err := fakeClient.AutopilotV1beta1().ClusterScalers(tt.request.ClusterId).Get(
						tt.request.ClusterId, metav1.GetOptions{})
					assert.NoError(t, err)

					// 验证 annotations 是否正确更新
					for _, annotation := range tt.request.Extend.Annotations {
						if annotation["type"] == "update" {
							assert.Equal(t, annotation["value"], clusterScaler.Annotations[annotation["key"]])
						}
					}
				}
			}
		})
	}
}

func TestClusterScalerStrategy_Rollback(t *testing.T) {
	strategy := &ClusterScalerStrategy{}
	request := &strategyprovider.TaskStrategyRequest{
		ClusterId:     "cls-test123",
		MetaClusterId: "cls-meta123",
		ProductName:   "tke",
		ChangeId:      "change-123",
		TraceId:       "trace-123",
		Component:     "test-component",
	}

	reply, err := strategy.Rollback(context.Background(), request)

	assert.NoError(t, err)
	assert.Equal(t, "PASS", reply.Code)
	assert.Equal(t, "clusterscaler rollback completed", reply.Detail)
}

func TestNewClusterScalerStrategy(t *testing.T) {
	strategy, err := NewClusterScalerStrategy(nil)

	assert.NoError(t, err)
	assert.NotNil(t, strategy)
	assert.IsType(t, &ClusterScalerStrategy{}, strategy)
}

func TestClusterScalerStrategy_Validate(t *testing.T) {
	strategy := &ClusterScalerStrategy{}

	// 测试有效请求（包含 annotations）
	validRequest := &strategyprovider.TaskStrategyRequest{
		ClusterId:   "cls-test123",
		Component:   "test-component",
		ChangeId:    "change-123",
		TaskName:    "test-task",
		TraceId:     "trace-123",
		ProductName: "tke",
		Extend: &model.ExtendInfo{
			Annotations: []map[string]string{
				{"type": "update", "key": "test-key", "value": "test-value"},
			},
		},
	}

	err := strategy.Validate(context.Background(), validRequest)
	assert.NoError(t, err)

	// 测试无效请求（缺少 annotations）
	invalidRequest := &strategyprovider.TaskStrategyRequest{
		ClusterId:   "cls-test123",
		Component:   "test-component",
		ChangeId:    "change-123",
		TaskName:    "test-task",
		TraceId:     "trace-123",
		ProductName: "tke",
		Extend:      &model.ExtendInfo{}, // 空的 ExtendInfo
	}
	err = strategy.Validate(context.Background(), invalidRequest)
	assert.Error(t, err)

	// 测试 nil ExtendInfo
	nilExtendRequest := &strategyprovider.TaskStrategyRequest{
		ClusterId:   "cls-test123",
		Component:   "test-component",
		ChangeId:    "change-123",
		TaskName:    "test-task",
		TraceId:     "trace-123",
		ProductName: "tke",
		Extend:      nil,
	}
	err = strategy.Validate(context.Background(), nilExtendRequest)
	assert.Error(t, err)
}
