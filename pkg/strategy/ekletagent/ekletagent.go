package ekletagent

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog/v2"
	platformapiv1 "tkestack.io/tke/api/platform/v1"

	"git.woa.com/kmetis/starship/pkg/clientset/platform"
	"git.woa.com/kmetis/starship/pkg/http_util"
	"git.woa.com/kmetis/starship/pkg/metrics"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
)

const (
	ProviderName      = "eklet-agent"    // 策略提供者标识
	EkletagentTimeout = 10 * time.Second // HTTP超时时间

	// API版本常量
	Version = "2018-05-25"
)

// 升级任务单元
type UpgradeTask struct {
	ClusterId string `json:"ClusterId"` // 字段名大写，符合API要求
	EksId     string `json:"EksId"`     // 字段名大写，符合API要求
	Version   string `json:"Version"`   // 字段名大写，符合API要求
}

// 升级任务请求体
type UpgradeTaskRequest struct {
	Action       string        `json:"Action"`       // API动作名称
	AppId        int64         `json:"AppId"`        // 应用ID
	Version      string        `json:"Version"`      // API版本
	EksTaskParam []UpgradeTask `json:"EksTaskParam"` // 任务参数数组
}

// 升级任务响应体
type Response struct {
	Id        []uint64 `json:"Id"`
	RequestId string   `json:"RequestId"`
}

type ApiResponse struct {
	Response Response `json:"Response"`
}

// 全局任务报告器
var gtr *EkletAgentReporter

// 初始化时注册策略工厂
func init() {
	strategyprovider.RegisterReleaseStrategyFactory(
		ProviderName,
		func(ctx *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
			return NewEkletAgentStrategy(ctx)
		},
	)

	initEkletAgentReporter()
}

// EkletAgentStrategy 实现了Eklet-Agent的升级策略
type EkletAgentStrategy struct {
	cfg *strategyprovider.ReleaseConfig // 策略配置信息
}

// NewEkletAgentStrategy 创建并返回一个新的EkletAgentStrategy实例
func NewEkletAgentStrategy(cfg *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
	strategy := &EkletAgentStrategy{cfg: cfg}
	return strategy, nil
}

// Exec 执行Eklet-Agent升级任务
func (e *EkletAgentStrategy) Exec(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	// 检查request是否为nil，防止空指针panic
	if request == nil {
		return nil, fmt.Errorf("request is nil")
	}

	baseLog := fmt.Sprintf("task-id:%d, traceId:%s", request.TaskId, request.TraceId)
	klog.Infof("Starting EkletAgent upgrade task, %s", baseLog)

	if request.Action != util.TaskActionUpgrade {
		klog.Errorf("changeid %s, task name %s, cluster %s, component %s, namespace %s, name %s, action %s not supported",
			request.ChangeId, request.TaskName, request.ClusterId, request.Component, request.Extend.Namespace, request.Extend.Name, request.Action)
		return nil, nil
	}

	if request.Extend == nil {
		return nil, fmt.Errorf("request.Extend is nil")
	}

	// 检查必要字段是否为空, ImageTag字段实际上是Eklet-Agent的版本号
	if request.Extend.EksId == "" || request.ImageTag == "" {
		return nil, fmt.Errorf("EksId or EkletagentVersion is empty")
	}
	appIdStr, err := getAppIdFromCluster(request.ClusterId, request.ProductName)
	if err != nil {
		return nil, fmt.Errorf("get AppId from cluster failed: %w", err)
	}
	//AppId 类型从string转换为int64
	appIdNum, err := strconv.ParseInt(appIdStr, 10, 64)
	if err != nil {
		return nil, fmt.Errorf("convert AppId to int64 failed")
	}

	// 构造HTTP请求参数, 这里一次只插入一条数据。实际上支持插入多条数据，但需要注意批量插入的最大数量限制为100条。
	payload := UpgradeTaskRequest{
		Action:  "CreateEKSOperationTask",
		AppId:   appIdNum,
		Version: Version,
		EksTaskParam: []UpgradeTask{
			{
				ClusterId: request.ClusterId,
				EksId:     request.Extend.EksId,
				Version:   request.ImageTag, // 这里的ImageTag字段实际上是Eklet-Agent的版本号
			},
		},
	}
	// 序列化请求参数
	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, err
	}
	// 获取短region，然后动态替换EkletAgentServer中的region字段
	shortRegion, err := util.GetShortRegion()
	if err != nil {
		klog.Errorf("Failed to get short region: %v, %s", err, baseLog)
		return nil, fmt.Errorf("failed to get short region: %v", err)
	}

	// 动态替换EkletAgentServer URL中的region字段
	ekletAgentServer := strings.Replace(request.EkletAgentServer, "{region}", shortRegion, -1)

	// 发送HTTP POST请求
	klog.Infof("Sending upgrade request to server: %s (original: %s, region: %s)", ekletAgentServer, request.EkletAgentServer, shortRegion)

	respBody, statusCode, err := http_util.PostRequest(ekletAgentServer, payloadBytes, nil, EkletagentTimeout)
	if err != nil {
		klog.Errorf("HTTP request failed: %v, %s", err, baseLog)
		return nil, fmt.Errorf("HTTP request failed: %v", err)
	}

	if statusCode != http.StatusOK {
		klog.Errorf("HTTP request failed with status code: %d, %s", statusCode, baseLog)
		return nil, fmt.Errorf("HTTP request failed with status code: %d", statusCode)
	}

	// 解析响应体
	var apiResponse ApiResponse
	if err := json.Unmarshal(respBody, &apiResponse); err != nil {
		klog.Errorf("Failed to unmarshal response: %v, %s", err, baseLog)
		return nil, fmt.Errorf("failed to unmarshal response: %v", err)
	}
	//{ "Response": { "Id": [ 23, 24 ], "RequestId": "b4ebccdc-544e-4627-97cb-981dc538e3e3" } }

	// 检查批量插入是否失败
	if len(apiResponse.Response.Id) == 0 {
		return nil, fmt.Errorf("batch insertion failed")
	}

	// 为每个ID添加异步任务进行结果查询
	klog.Infof("Adding async query tasks, task count: %d, %s", len(apiResponse.Response.Id), baseLog)
	for _, id := range apiResponse.Response.Id {
		gtr.AddTask(request.SubTaskId, request.ClusterId, id, appIdNum, ekletAgentServer) // id为任务ID
	}
	return &strategyprovider.TaskStrategyReply{}, nil
}

// Rollback 回滚升级任务（未实现，预留）
func (e *EkletAgentStrategy) Rollback(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	// 检查request是否为nil，防止空指针panic
	if request == nil {
		return nil, fmt.Errorf("request is nil")
	}

	baseLog := fmt.Sprintf("task-id:%d, traceId:%s", request.TaskId, request.TraceId)
	klog.Infof("Received rollback request, but rollback functionality is not implemented in current version, %s", baseLog)
	// TODO: 实现回滚逻辑
	return nil, fmt.Errorf("rollback not implemented")
}

func getAppIdFromCluster(clusterId, productName string) (string, error) {

	var cluster *platformapiv1.Cluster
	var err error
	if productName == "tke" {
		cluster, err = platform.GetClientSet().Client.PlatformV1().Clusters().Get(context.TODO(), clusterId, metav1.GetOptions{})
	} else if productName == "eks" {
		cluster, err = platform.GetClientSet().EksClient.PlatformV1().Clusters().Get(context.TODO(), clusterId, metav1.GetOptions{})
	} else {
		return "", fmt.Errorf("productName is invalid: %s", productName)
	}
	if err != nil {
		metrics.ClusterInfoFetchFailedTotal.WithLabelValues().Inc()
		return "", fmt.Errorf("failed to get cluster:%v", err)
	}

	appId := cluster.Spec.TenantID
	return appId, nil
}
