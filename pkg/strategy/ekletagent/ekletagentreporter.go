package ekletagent

import (
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/klog"

	"git.woa.com/kmetis/starship/infra"
	"git.woa.com/kmetis/starship/pkg/http_util"
	"git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/kmetis/starship/pkg/util/queue"
)

var EkletAgentQueryURL = ""

const (
	DefaultWorks    = 5
	DefaultDuration = 30 * time.Second
	HttpTimeout     = 10 * time.Second

	// 重试和超时配置常量
	MaxRetryAttempts = 10                // 最大重试次数
	TaskTimeout      = 120 * time.Minute // 任务超时时间
)

// EkletAgentTask 表示一个待查询的Eklet-Agent升级任务
type EkletAgentTask struct {
	OwnerTaskID   int64     // starship子任务ID
	ClusterId     string    // 集群ID（新接口必选）
	TaskId        uint64    // 升级任务ID（可选）
	AppId         int64     // 应用ID
	RetryCount    int       // 当前重试次数
	CreatedTime   time.Time // 任务创建时间
	LastAttempted time.Time // 最后一次尝试时间
}

// EkletAgentTaskResult 表示Eklet-Agent任务的查询结果
type EkletAgentTaskResult struct {
	TaskStatus string // 任务状态
	FailReason string // 失败原因
	EksId      string // EKS ID
	Version    string // 版本
	TaskId     uint64 // 任务ID
	Script     string // 脚本
	Result     string // 结果
}

// GetEksInfoFromDBResponse 表示Eklet-Agent查询API的响应结构
type GetEksInfoFromDBResponse struct {
	Response struct {
		EksTaskInfo []EksTaskInfo `json:"EksTaskInfo"`
	} `json:"Response"`
	RequestId string `json:"RequestId"`
}

// QueryTaskStatusRequest 表示查询任务状态的请求结构
type QueryTaskStatusRequest struct {
	Action    string `json:"Action"`    // API动作名称
	AppId     int64  `json:"AppId"`     // 应用ID
	Version   string `json:"Version"`   // API版本
	ClusterId string `json:"clusterId"` // 集群ID
	Id        uint64 `json:"Id"`        // 任务ID
}

// EksTaskInfo 表示单个任务的详细信息（字段名已适配新接口）
type EksTaskInfo struct {
	Id         uint64 `json:"Id"`
	EksId      string `json:"eksId"`
	Script     string `json:"script"`
	Version    string `json:"version"`
	TaskStatus string `json:"taskStatus"`
	Result     string `json:"result"`
	FailReason string `json:"failReason"`
}

// ===================== 报告器实现 =====================

// EkletAgentReporter 负责异步轮询Eklet-Agent任务状态，并回写到starship任务系统
type EkletAgentReporter struct {
	tasks    *queue.ConcurrentQueue // 并发安全的任务队列
	stop     chan struct{}          // 停止信号通道
	works    int                    // 工作协程数量
	duration time.Duration          // 处理间隔时间
}

// initEkletAgentReporter 初始化全局Eklet-Agent任务报告器
func initEkletAgentReporter() {
	gtr = &EkletAgentReporter{
		tasks:    queue.NewConcurrentQueue(),
		stop:     make(chan struct{}),
		works:    DefaultWorks,
		duration: DefaultDuration,
	}
	gtr.Start()
}

// AddTask 添加一个待查询的Eklet-Agent任务到队列
func (r *EkletAgentReporter) AddTask(ownerTaskID int64, clusterId string, taskId uint64, appId int64, EkletAgentServer string) {
	now := time.Now()
	klog.Infof("Adding query task: OwnerTaskID=%d, TaskId=%d", ownerTaskID, taskId)

	task := &EkletAgentTask{
		OwnerTaskID:   ownerTaskID,
		ClusterId:     clusterId,
		TaskId:        taskId, // 任务ID
		AppId:         appId,  // 应用ID
		RetryCount:    0,
		CreatedTime:   now,
		LastAttempted: now,
	}

	r.tasks.Push(task)
	EkletAgentQueryURL = EkletAgentServer
}

// Start 启动多个worker协程，定期处理任务队列
func (r *EkletAgentReporter) Start() {
	for i := 0; i < r.works; i++ {
		go wait.Until(func() { r.runTaskProcessWorker() }, r.duration, r.stop)
	}
	klog.Infof("EkletAgent reporter started with %d workers", r.works)
}

// Stop 停止所有worker协程（幂等）
func (r *EkletAgentReporter) Stop() {
	select {
	case <-r.stop:
		// 已关闭，无需重复关闭
	default:
		close(r.stop)
	}
}

// runTaskProcessWorker 是worker主循环，每次从队列取出一个任务并处理
func (r *EkletAgentReporter) runTaskProcessWorker() {
	t, exist := r.tasks.Pop()
	if !exist {
		return
	}

	task, ok := t.(*EkletAgentTask)
	if !ok {
		return
	}

	now := time.Now()
	// 检查任务是否超时
	elapsedTime := now.Sub(task.CreatedTime)
	if elapsedTime > TaskTimeout {
		r.handleTaskFailure(task, "task timeout")
		return
	}

	// 检查是否超过最大重试次数
	if task.RetryCount >= MaxRetryAttempts {
		r.handleTaskFailure(task, fmt.Sprintf("exceeded max retry attempts (%d)", MaxRetryAttempts))
		return
	}

	// 更新最后尝试时间
	task.LastAttempted = now

	// 查询Eklet-Agent任务状态
	result, err := queryTaskStatus(task.ClusterId, task.TaskId, task.AppId)
	if err != nil {
		// 增加重试计数
		task.RetryCount++

		// 如果还没达到最大重试次数，重新入队
		if task.RetryCount < MaxRetryAttempts {
			r.tasks.Push(task)
		} else {
			r.handleTaskFailure(task, fmt.Sprintf("query failed after %d attempts: %v", MaxRetryAttempts, err))
		}
		return
	}

	// 重置重试计数（查询成功）
	task.RetryCount = 0
	// 任务未完成，继续等待
	if result.TaskStatus != util.EkletAgentTaskTypeCompleted && result.TaskStatus != util.EkletAgentTaskTypeFailed {
		r.tasks.Push(task)
		return
	}

	// 任务完成，回写starship子任务状态
	starshipSubTask, err := infra.GetSubTask(task.OwnerTaskID)
	if err != nil {
		r.tasks.Push(task)
		return
	}

	completionTime := time.Now()
	starshipSubTask.EndTime = &completionTime
	starshipSubTask.Status = util.TaskStatusDone
	starshipSubTask.Reason = result.FailReason

	if err := infra.UpdateSubTask(starshipSubTask); err != nil {
		r.tasks.Push(task)
		return
	}
	// 仅在任务失败时更新父任务状态
	if result.TaskStatus == util.EkletAgentTaskTypeFailed {
		parentTask, err := infra.GetTask(starshipSubTask.ParentTaskId)
		if err != nil {
			r.tasks.Push(task)
			return
		}
		parentTask.Status = util.TaskStatusDone
		parentTask.Reason = result.FailReason
		if err := infra.UpdateTask(parentTask); err != nil {
			r.tasks.Push(task)
			return
		}
	}

	totalElapsed := completionTime.Sub(task.CreatedTime)
	klog.Infof("Task completed: TaskId=%v, elapsed=%v, status=%s", task.TaskId, totalElapsed, result.TaskStatus)

}

// handleTaskFailure 处理任务失败情况，更新任务状态为失败
func (r *EkletAgentReporter) handleTaskFailure(task *EkletAgentTask, reason string) {

	// 获取子任务
	starshipSubTask, err := infra.GetSubTask(task.OwnerTaskID)
	if err != nil {
		klog.Errorf("Failed to get subtask during failure handling, OwnerTaskID=%v, error=%v", task.OwnerTaskID, err)
		return
	}

	// 更新子任务状态为失败
	now := time.Now()
	starshipSubTask.EndTime = &now
	starshipSubTask.Status = util.TaskStatusDone
	starshipSubTask.Reason = reason

	if err := infra.UpdateSubTask(starshipSubTask); err != nil {
		return
	}

	// 更新父任务状态
	parentTask, err := infra.GetTask(starshipSubTask.ParentTaskId)
	if err != nil {
		return
	}

	parentTask.Status = util.TaskStatusDone
	parentTask.Reason = reason

	if err := infra.UpdateTask(parentTask); err != nil {
		return
	}

	klog.Infof("Task failed: TaskId=%v, reason=%s", task.TaskId, reason)
}

// ===================== 查询任务状态 =====================

// queryTaskStatus 查询Eklet-Agent任务状态
// 参数：clusterId - 集群ID，taskId - 任务ID，appId - 应用ID
// 返回：任务结果结构体，或错误
func queryTaskStatus(clusterId string, taskId uint64, appId int64) (*EkletAgentTaskResult, error) {

	// 构造查询请求参数
	payload := QueryTaskStatusRequest{
		Action:    "DescribeEKSOperationTask",
		AppId:     appId,   // 使用实际的应用ID
		Version:   Version, // 使用主文件中定义的版本常量
		ClusterId: clusterId,
		Id:        taskId,
	}

	payloadBytes, err := json.Marshal(payload)
	if err != nil {
		return nil, fmt.Errorf("marshal payload failed: %w", err)
	}

	respBody, statusCode, err := http_util.PostRequest(EkletAgentQueryURL, payloadBytes, nil, HttpTimeout)
	if err != nil {
		return nil, fmt.Errorf("http request failed: %w", err)
	}

	if statusCode != http.StatusOK {
		return nil, fmt.Errorf("query task status failed, http status: %d", statusCode)
	}

	var apiResp GetEksInfoFromDBResponse
	if err := json.Unmarshal(respBody, &apiResp); err != nil {
		return nil, fmt.Errorf("decode response failed: %w", err)
	}

	if len(apiResp.Response.EksTaskInfo) == 0 {
		return nil, fmt.Errorf("no EksTaskInfo found for clusterId=%s, taskId=%d", clusterId, taskId)
	}

	info := apiResp.Response.EksTaskInfo[0]
	result := &EkletAgentTaskResult{
		TaskStatus: info.TaskStatus, // 任务状态
		FailReason: info.FailReason, // 升级错误原因
		EksId:      info.EksId,      // 目标子机id
		Version:    info.Version,    // 目标版本号
		TaskId:     info.Id,         // 任务ID
		Script:     info.Script,     // 执行脚本
		Result:     info.Result,     // 执行结果
	}

	return result, nil
}
