package grpc

import (
	"context"
	"crypto/md5"
	"fmt"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"

	"git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/metrics"
	"git.woa.com/kmetis/starship/pkg/model"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util/kubeclient"
)

const (
	ProviderName   = "grpc"
	grpcTimeout    = time.Second * 10
	grpcReportPush = "push"
	grpcReportPull = "pull"
)

var gtr *GrpcTaskReporter

func init() {
	strategyprovider.RegisterReleaseStrategyFactory(
		ProviderName,
		func(ctx *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
			return NewGrpcStrategy(ctx)
		},
	)

	initGrpcTaskReporter()
}

type GrpcStrategy struct {
	cfg *strategyprovider.ReleaseConfig
}

func NewGrpcStrategy(ctx *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
	return &GrpcStrategy{cfg: ctx}, nil
}

func getTPToken(request *strategyprovider.TaskStrategyRequest) (*pb.TPToken, error) {
	if request.IanvsToken != "" {
		return &pb.TPToken{Token: request.IanvsToken, Username: request.User, Type: "ianvs"}, nil
	}

	host, token, err := kubeclient.GetClusterClientInfo(request)
	if err != nil {
		return nil, err
	}
	return &pb.TPToken{Token: token, Host: host, Username: request.User, Type: "tke"}, nil
}

func (d *GrpcStrategy) Exec(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	conn, err := grpc.NewClient(request.GrpcServer, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	// Contact the server and print out its response.
	ctx, cancel := context.WithTimeout(ctx, grpcTimeout)
	defer cancel()

	tpToken, err := getTPToken(request)
	if err != nil {
		return nil, err
	}

	extender, err := model.FormatExtendInfoToJSON(request.Extend)
	if err != nil {
		return nil, err
	}

	client := pb.NewTPTaskEngineClient(conn)
	req := &pb.TPCreateTaskRequest{
		// 这里使用的是long region
		Region:      request.Region,
		ClusterId:   request.ClusterId,
		Workload:    request.Component,
		ImageTag:    request.ImageTag,
		ClientToken: genClientToken(request),
		ReportId:    request.SubTaskId,
		Token:       tpToken,
		Action:      request.Action,
		Extender:    extender,
	}
	res, err := client.CreateTask(ctx, req)
	if err != nil {
		metrics.TPTaskCreateFailedTotal.WithLabelValues(request.GrpcServer).Add(1)
		return nil, err
	}

	if res.GetCode() != 0 {
		return nil, fmt.Errorf("create task failed: code %d reason %s", res.GetCode(), res.GetReason())
	}

	if request.GrpcReportModel == grpcReportPull {
		// pull模式，则添加异步任务进行结果的查询
		gtr.AddTask(request.Region, res.TaskId, request.GrpcServer, request.SubTaskId)
	}
	return &strategyprovider.TaskStrategyReply{}, nil
}

func genClientToken(request *strategyprovider.TaskStrategyRequest) string {
	token := fmt.Sprintf("%s-%s-%s-%s-%d-%s",
		request.Region, request.ClusterId, request.Component, request.ImageTag, request.SubTaskId, request.Action)
	return fmt.Sprintf("%x", md5.Sum([]byte(token)))
}

func (d *GrpcStrategy) Rollback(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	return nil, fmt.Errorf("not implement")
}
