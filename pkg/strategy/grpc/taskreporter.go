package grpc

import (
	"context"
	"encoding/json"
	"fmt"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/klog"

	"git.woa.com/kmetis/starship/infra"
	"git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/metrics"
	"git.woa.com/kmetis/starship/pkg/model"
	"git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/kmetis/starship/pkg/util/queue"
)

type taskKey struct {
	taskId      string // grpc task id
	ownerTaskId int64  // owner taskid: starship task id
	region      string
	grpcServer  string
}

type taskResult struct {
	status string
	code   int32
	reason string
	risks  []*pb.TPRisk
}

type grpcTask struct {
	info   taskKey
	result taskResult
}

type GrpcTaskReporter struct {
	tasks *queue.ConcurrentQueue
	stop  chan struct{}

	works    int
	duration time.Duration
}

func initGrpcTaskReporter() {
	gtr = &GrpcTaskReporter{
		tasks:    queue.NewConcurrentQueue(),
		stop:     make(chan struct{}),
		works:    5,
		duration: 30,
	}
	gtr.Start()
}

// ownerTaskId: starship的子任务id
func (r *GrpcTaskReporter) AddTask(region, taskId, grpcServer string, ownerTaskId int64) {
	r.tasks.Push(&grpcTask{info: taskKey{taskId: taskId, ownerTaskId: ownerTaskId, region: region, grpcServer: grpcServer}})
}

func (r *GrpcTaskReporter) Start() {
	for i := 0; i < r.works; i++ {
		go wait.Until(r.runTaskProcessWorker, r.duration*time.Second, r.stop)
	}
}

func (r *GrpcTaskReporter) Stop() {
	r.stop <- struct{}{}
}

func (r *GrpcTaskReporter) runTaskProcessWorker() {
	t, exist := r.tasks.Pop()
	if !exist {
		return
	}

	task, ok := t.(*grpcTask)
	if !ok {
		klog.Errorf("invalid task type: %v", t)
		return
	}

	rst, err := getTaskResult(task.info)
	if err != nil {
		klog.Errorf("describe task %v result failed: %v", t, err)
		metrics.TPTaskQueryFailedTotal.WithLabelValues(task.info.grpcServer).Add(1)
		r.tasks.Push(t)
		return
	}

	if rst.status != util.TaskStatusDone {
		klog.Warningf("describe task %v result: %v", t, rst)
		r.tasks.Push(t)
		return
	}

	starshipSubTask, err := infra.GetSubTask(task.info.ownerTaskId)
	if err != nil {
		klog.Errorf("GetSubTask task %v failed: %v", t, err)
		r.tasks.Push(t)
		return
	}

	if starshipSubTask.Status == util.TaskStatusDone {
		klog.Warningf("subtask %v for grpc task %v has done", starshipSubTask, t)
		// 不return，用于后续异常时的重试处理
	}

	// 子任务失败 或者 postcheck结束时更新task为完成
	parentTask, err := infra.GetTask(starshipSubTask.ParentTaskId)
	if err != nil {
		klog.Errorf("GetTask %d failed: %v", starshipSubTask.ParentTaskId, err)
		r.tasks.Push(t)
		return
	}
	if parentTask.Status == util.TaskStatusDone {
		klog.Warningf("task: %d has done for rpc task %s", parentTask.ID, task.info.taskId)
		return
	}

	// 逻辑参考：ReportTaskResult，更新子任务、risk、父任务。
	// 更新subtask为完成
	now := time.Now()
	starshipSubTask.EndTime = &now
	starshipSubTask.Status = util.TaskStatusDone
	starshipSubTask.Reason = rst.reason
	// starshipSubTask.CostTime = time.Now().Sub(startTime).Seconds()
	hasRisk := isHealthCheckHitRisk(rst.risks, parentTask.ExtendInfo)
	if hasRisk {
		errMsg := fmt.Sprintf("error: %s hit risks", starshipSubTask.Action)
		if rst.reason == "" {
			starshipSubTask.Reason = errMsg
		}
	}
	err = infra.UpdateSubTask(starshipSubTask)
	if err != nil {
		klog.Errorf("UpdateSubTask %v failed: %v", starshipSubTask, err)
		r.tasks.Push(t)
		return
	}

	err = createRisks(starshipSubTask, rst.risks)
	if err != nil {
		klog.Errorf("createRisks %v failed: %v", starshipSubTask, err)
		r.tasks.Push(t)
		return
	}

	// 失败、后检完成 或者 只有预检且完成时，更新父任务为完成
	if isHealthCheckHitRisk(rst.risks, parentTask.ExtendInfo) || rst.reason != "" || (starshipSubTask.Action == util.TaskActionPostCheck && starshipSubTask.Status == util.TaskStatusDone) ||
		(parentTask.Type == util.TaskActionPreCheck && starshipSubTask.Status == util.TaskStatusDone) {
		parentTask.Status = util.TaskStatusDone
		parentTask.Reason = starshipSubTask.Reason
		err = infra.UpdateTask(parentTask)
		if err != nil {
			klog.Errorf("UpdateTask %d failed: %v", starshipSubTask.ParentTaskId, err)
			r.tasks.Push(t)
			return
		}
	}
}

func getTaskResult(info taskKey) (*taskResult, error) {
	conn, err := grpc.NewClient(info.grpcServer, grpc.WithTransportCredentials(insecure.NewCredentials()))
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	// Contact the server and print out its response.
	ctx, cancel := context.WithTimeout(context.Background(), grpcTimeout)
	defer cancel()

	client := pb.NewTPTaskEngineClient(conn)
	req := &pb.TPDescribeTaskRequest{
		Region: info.region,
		TaskId: info.taskId,
	}
	res, err := client.DescribeTask(ctx, req)
	if err != nil {
		return nil, err
	}

	return &taskResult{
		status: res.GetStatus(),
		code:   res.GetCode(),
		reason: res.GetReason(),
		risks:  res.GetRisks(),
	}, nil
}

func createRisks(subtask *infra.StarshipSubtask, risks []*pb.TPRisk) error {
	var starshipRisks []*infra.StarshipRisks
	for _, v := range risks {
		// 空risk不允许上报
		if v.Code == "" || v.AppName == "" {
			klog.Errorf("subtask %d risk's empty: %v", subtask.ID, v)
			continue
		}
		newRisk := &infra.StarshipRisks{
			SubTaskId:       subtask.ID,
			AppName:         subtask.AppName,
			Name:            v.GetName(),
			Code:            v.GetCode(),
			Detail:          v.GetDetail(),
			Level:           v.GetLevel(),
			Solution:        v.GetSolution(),
			HealthCheckName: v.GetResrouce(),
		}
		starshipRisks = append(starshipRisks, newRisk)
	}

	if len(starshipRisks) > 0 {
		// 对于app-controller过来的请求，会只更新upgrade任务状态，没有risk信息。
		err := infra.CreateRisk(starshipRisks)
		if err != nil {
			klog.Errorf("create risk for subtask %d failed: %v", subtask.ID, err)
			return err
		}
	}

	return nil
}

func ReportTaskResult(ctx context.Context, req *pb.TPReportTaskResultRequest) (*pb.TPReportTaskResultReply, error) {
	klog.Infof("report task result request:%+v", req)
	rsp := &pb.TPReportTaskResultReply{}

	starshipSubTask, err := infra.GetSubTask(req.GetReportId())
	if err != nil {
		klog.Errorf("GetSubTask task %d failed: %v", req.GetReportId(), err)
		rsp.Code = 1
		rsp.Reason = err.Error()
		return rsp, nil
	}

	if starshipSubTask.Status == util.TaskStatusDone {
		klog.Warningf("subtask %d for grpc task %s has done", req.GetReportId(), req.GetTaskId())
		return rsp, nil
	}

	parentTask, err := infra.GetTask(starshipSubTask.ParentTaskId)
	if err != nil {
		klog.Errorf("GetTask %d failed: %v", starshipSubTask.ParentTaskId, err)
		return rsp, nil
	}
	if parentTask.Status == util.TaskStatusDone {
		klog.Warningf("task: %d has done for rpc task %s", parentTask.ID, req.GetTaskId())
		return rsp, nil
	}

	// 逻辑参考：ReportTaskResult，更新子任务、risk、父任务。
	// 更新subtask为完成
	starshipSubTask.Status = util.TaskStatusDone
	starshipSubTask.Reason = req.GetReason()
	now := time.Now()
	starshipSubTask.EndTime = &now
	hasRisk := isHealthCheckHitRisk(req.GetRisks(), parentTask.ExtendInfo)
	if hasRisk {
		errMsg := fmt.Sprintf("error: %s hit risks", starshipSubTask.Action)
		if req.GetReason() == "" {
			starshipSubTask.Reason = errMsg
		}
	}
	err = infra.UpdateSubTask(starshipSubTask)
	if err != nil {
		klog.Errorf("UpdateSubTask %d failed: %v", req.GetReportId(), err)
		rsp.Code = 2
		rsp.Reason = err.Error()
		return rsp, nil
	}

	// 对于预检/后检任务，更新扫描的risks, upgrade也需要更新risks
	err = createRisks(starshipSubTask, req.GetRisks())
	if err != nil {
		klog.Errorf("createRisks %d failed: %v", req.GetReportId(), err)
		rsp.Code = 3
		rsp.Reason = err.Error()
		return rsp, nil
	}

	// 子任务失败 或者 postcheck结束时更新task为完成
	if isHealthCheckHitRisk(req.GetRisks(), parentTask.ExtendInfo) || req.GetReason() != "" || (starshipSubTask.Action == util.TaskActionPostCheck && starshipSubTask.Status == util.TaskStatusDone) {
		parentTask, err := infra.GetTask(starshipSubTask.ParentTaskId)
		if err != nil {
			klog.Errorf("GetTask %d failed: %v", starshipSubTask.ParentTaskId, err)
			rsp.Code = 4
			rsp.Reason = err.Error()
			return rsp, nil
		}

		parentTask.Status = util.TaskStatusDone
		parentTask.Reason = starshipSubTask.Reason
		err = infra.UpdateTask(parentTask)
		if err != nil {
			klog.Errorf("UpdateTask %d failed: %v", starshipSubTask.ParentTaskId, err)
			rsp.Code = 5
			rsp.Reason = err.Error()
			return rsp, nil
		}
	}
	return rsp, nil
}

func isHealthCheckHitRisk(risks []*pb.TPRisk, extendInfo string) bool {
	var extend model.ExtendInfo
	if extendInfo != "" {
		if err := json.Unmarshal([]byte(extendInfo), &extend); err != nil {
			klog.Errorf("Failed to unmarshal extendInfo: %v", err)
			// 反序列化失败时，按默认逻辑处理
			return defaultHealthCheckHitRisk(risks)
		}
	}

	skipWarning := extend.SkipWarningPreCheckItem
	for _, v := range risks {
		if v == nil {
			continue
		}
		if v.Code != healthcheck.HEALTHCHECK_CODE_PASS {
			if v.Level == healthcheck.RISK_LEVEL_FATAL {
				return true
			}
			if v.Level == healthcheck.RISK_LEVEL_WARNING && !skipWarning {
				return true
			}
		}
	}
	return false
}

func defaultHealthCheckHitRisk(risks []*pb.TPRisk) bool {
	for _, v := range risks {
		if v.Code != healthcheck.HEALTHCHECK_CODE_PASS && (v.Level == healthcheck.RISK_LEVEL_FATAL || v.Level == healthcheck.RISK_LEVEL_WARNING) {
			return true
		}
	}
	return false
}
