package configmap

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/klog/v2"

	"git.woa.com/kmetis/starship/infra"
	"git.woa.com/kmetis/starship/pkg/model"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/kmetis/starship/pkg/util/kubeclient"
)

const (
	ProviderName = "configmap"

	// Simplified retry configuration
	MaxRetries    = 3
	RetryInterval = 1 * time.Second
)

// ConfigMapState represents the state of a ConfigMap for rollback purposes
type ConfigMapState struct {
	Data            map[string]string `json:"data"`
	ResourceVersion string            `json:"resourceVersion"`
	Namespace       string            `json:"namespace"`
	Name            string            `json:"name"`
}

// RetryableOperation represents an operation that can be retried
type RetryableOperation func(ctx context.Context, attempt int) (interface{}, error)

func init() {
	strategyprovider.RegisterReleaseStrategyFactory(
		ProviderName,
		func(ctx *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
			return NewConfigMapStrategy(ctx)
		},
	)
}

// ConfigMapStrategy implements the Strategy interface for ConfigMap operations
type ConfigMapStrategy struct {
	config *strategyprovider.ReleaseConfig
}

// executeWithRetry executes an operation with simple retry logic
func (c *ConfigMapStrategy) executeWithRetry(ctx context.Context, operation RetryableOperation, operationName string, request *strategyprovider.TaskStrategyRequest) (interface{}, error) {
	var lastErr error

	for attempt := 1; attempt <= MaxRetries; attempt++ {
		// Execute the operation
		result, err := operation(ctx, attempt)
		if err == nil {
			return result, nil
		}
		lastErr = err

		// If this is the last attempt, don't wait
		if attempt >= MaxRetries {
			break
		}
		time.Sleep(RetryInterval)
	}

	return nil, fmt.Errorf("%s operation failed after %d attempts: %v", operationName, MaxRetries, lastErr)
}

// getConfigMapRevision retrieves the current ConfigMap and returns its state for rollback
func (c *ConfigMapStrategy) getConfigMapRevision(ctx context.Context, client kubernetes.Interface, namespace, configMapName string) (*ConfigMapState, error) {
	configMap, err := client.CoreV1().ConfigMaps(namespace).Get(ctx, configMapName, metav1.GetOptions{})
	if err != nil {
		return nil, fmt.Errorf("failed to get ConfigMap %s/%s: %v", namespace, configMapName, err)
	}

	// Create a deep copy of the data to avoid reference issues
	dataCopy := make(map[string]string)
	if configMap.Data != nil {
		for k, v := range configMap.Data {
			dataCopy[k] = v
		}
	}

	state := &ConfigMapState{
		Data:            dataCopy,
		ResourceVersion: configMap.ResourceVersion,
		Namespace:       namespace,
		Name:            configMapName,
	}

	return state, nil
}

// NewConfigMapStrategy creates a new instance of ConfigMapStrategy
func NewConfigMapStrategy(ctx *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
	if ctx == nil {
		return nil, fmt.Errorf("release config cannot be nil")
	}

	return &ConfigMapStrategy{
		config: ctx,
	}, nil
}

// Validate validates the TaskStrategyRequest for ConfigMap operations
func (c *ConfigMapStrategy) Validate(ctx context.Context, request *strategyprovider.TaskStrategyRequest) error {
	// 基础参数验证
	// if err := util.ValidateTaskStrategyRequest(request); err != nil {
	// 	return fmt.Errorf("basic validation failed: %v", err)
	// }

	// ConfigMap特定验证
	if request.Extend == nil {
		return fmt.Errorf("ExtendInfo cannot be nil for ConfigMap operations")
	}

	// 验证ConfigMapData字段
	if len(request.Extend.ConfigMapData) == 0 {
		return fmt.Errorf("ConfigMapData cannot be empty")
	}

	// 验证ConfigMapData中每个操作项的合法性
	if err := util.ValidateConfigMapData(request.Extend.ConfigMapData); err != nil {
		return fmt.Errorf("ConfigMapData validation failed: %v", err)
	}

	// 执行额外的ConfigMap请求验证
	if err := c.validateConfigMapRequest(request); err != nil {
		return fmt.Errorf("ConfigMap request validation failed: %v", err)
	}

	return nil
}

// Exec executes the ConfigMap strategy operation
func (c *ConfigMapStrategy) Exec(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	// 验证请求参数
	if err := c.Validate(ctx, request); err != nil {
		klog.Errorf("changeid %s, task name %s, cluster %s, component %s, validate failed: %v",
			request.ChangeId, request.TaskName, request.ClusterId, request.Component, err)
		return nil, fmt.Errorf("validate failed: %v", err)
	}

	// 检查操作类型，只处理upgrade操作
	if request.Action != util.TaskActionUpgrade {
		klog.Errorf("changeid %s, task name %s, cluster %s, component %s, action %s ignored",
			request.ChangeId, request.TaskName, request.ClusterId, request.Component, request.Action)
		return nil, nil
	}

	// 获取Kubernetes客户端
	client, err := kubeclient.GetK8sClientWithUserAgent(request, "autopilot-controller")
	if err != nil {
		klog.Errorf("changeid %s, task name %s, cluster %s, component %s, failed to get k8s client: %v",
			request.ChangeId, request.TaskName, request.ClusterId, request.Component, err)
		return nil, fmt.Errorf("failed to get k8s client: %v", err)
	}

	// 执行ConfigMap更新操作，支持重试机制
	clusterId := request.ClusterId
	namespace := util.GetComponentNamespace(clusterId, request.Namespace)
	configMapName := util.GetWorkloadName(clusterId, request.WorkloadName)

	// 获取原始ConfigMap状态用于回滚
	originalStateResult, err := c.executeWithRetry(ctx, func(ctx context.Context, attempt int) (interface{}, error) {
		return c.getConfigMapRevision(ctx, client, namespace, configMapName)
	}, "get-original-state", request)

	if err != nil {
		return nil, fmt.Errorf("failed to get original ConfigMap state: %v", err)
	}

	originalState := originalStateResult.(*ConfigMapState)

	// 执行ConfigMap更新操作
	_, err = c.executeWithRetry(ctx, func(ctx context.Context, attempt int) (interface{}, error) {
		return c.updateConfigMapWithRetry(ctx, client, namespace, configMapName, request.Extend.ConfigMapData, request)
	}, "update-configmap", request)

	if err != nil {
		return nil, fmt.Errorf("ConfigMap update failed: %v", err)
	}

	// 记录操作历史，存储原始状态用于回滚
	if err := c.recordOperationWithState(request, "update", originalState, namespace, configMapName); err != nil {
		klog.Warningf("changeid %s, task name %s, failed to record operation history: %v",
			request.ChangeId, request.TaskName, err)
		// 历史记录失败不影响主流程，只记录警告日志
	}

	return nil, err
}

// Rollback performs rollback operation for ConfigMap
func (c *ConfigMapStrategy) Rollback(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {

	// 检查操作类型
	if request.Action != util.TaskActionRollback {
		klog.Errorf("changeid %s, task name %s, cluster %s, component %s, invalid action for rollback: %s",
			request.ChangeId, request.TaskName, request.ClusterId, request.Component, request.Action)
		return nil, fmt.Errorf("invalid action for rollback: %s", request.Action)
	}

	// 获取Kubernetes客户端
	client, err := kubeclient.GetK8sClientWithUserAgent(request, "autopilot-controller")
	if err != nil {
		return nil, fmt.Errorf("failed to get k8s client: %v", err)
	}

	clusterId := request.ClusterId
	namespace := util.GetComponentNamespace(clusterId, request.Namespace)
	configMapName := util.GetWorkloadName(clusterId, request.WorkloadName)

	// 获取回滚目标任务ID
	var orgTaskId int64
	if request.Extend != nil && request.Extend.Rollback != nil {
		if taskIdStr, exists := request.Extend.Rollback["TaskId"]; exists {
			if parsedTaskId, parseErr := strconv.ParseInt(taskIdStr, 10, 64); parseErr == nil {
				orgTaskId = parsedTaskId
			}
		}
	}

	if orgTaskId == 0 {
		return nil, fmt.Errorf("invalid or missing original task ID for rollback")
	}

	// 获取历史版本信息
	revision, err := infra.GetRevisionByTask(orgTaskId)
	if err != nil {
		klog.Errorf("changeid %s, task name %s, failed to get revision history for task %d: %v",
			request.ChangeId, request.TaskName, orgTaskId, err)
		return nil, fmt.Errorf("failed to get revision history for task %d: %v", orgTaskId, err)
	}

	// 验证revision记录是否匹配当前请求
	expectedResourceInfo := fmt.Sprintf("%s/%s", namespace, configMapName)
	if revision.ResourceInfo != expectedResourceInfo {
		klog.Errorf("changeid %s, task name %s, revision resource info mismatch: expected %s, got %s",
			request.ChangeId, request.TaskName, expectedResourceInfo, revision.ResourceInfo)
		return nil, fmt.Errorf("revision resource info mismatch: expected %s, got %s",
			expectedResourceInfo, revision.ResourceInfo)
	}

	if revision.Strategy != ProviderName {
		klog.Errorf("changeid %s, task name %s, revision strategy mismatch: expected %s, got %s",
			request.ChangeId, request.TaskName, ProviderName, revision.Strategy)
		return nil, fmt.Errorf("revision strategy mismatch: expected %s, got %s",
			ProviderName, revision.Strategy)
	}

	// 解析存储的ConfigMap状态
	var originalState *ConfigMapState
	if err := json.Unmarshal([]byte(revision.Revision), &originalState); err != nil {
		klog.Errorf("changeid %s, task name %s, failed to unmarshal ConfigMap state: %v",
			request.ChangeId, request.TaskName, err)
		return nil, fmt.Errorf("failed to unmarshal ConfigMap state: %v", err)
	}

	_, err = c.executeWithRetry(ctx, func(ctx context.Context, attempt int) (interface{}, error) {
		return c.rollbackConfigMapWithRetry(ctx, client, namespace, configMapName, originalState, request)
	}, "rollback-configmap", request)

	if err != nil {
		klog.Errorf("changeid %s, task name %s, cluster %s, component %s, ConfigMap rollback failed: %v",
			request.ChangeId, request.TaskName, request.ClusterId, request.Component, err)
		return nil, fmt.Errorf("ConfigMap rollback failed: %v", err)
	}

	return nil, nil
}

// recordOperationWithState records the ConfigMap operation with complete state for rollback
func (c *ConfigMapStrategy) recordOperationWithState(request *strategyprovider.TaskStrategyRequest, operationType string, state *ConfigMapState, namespace, configmapName string) error {
	if request.TaskId == 0 || request.SubTaskId == 0 {
		return fmt.Errorf("invalid task ID or sub-task ID")
	}

	// 将状态序列化为JSON字符串
	stateJSON, err := json.Marshal(state)
	if err != nil {
		return fmt.Errorf("failed to marshal ConfigMap state: %v", err)
	}

	revisionRecord := &infra.StarshipRevision{
		TaskId:       request.TaskId,
		SubTaskId:    request.SubTaskId,
		AppName:      request.Component,
		Strategy:     ProviderName,
		ResourceType: "ConfigMap",
		ResourceInfo: fmt.Sprintf("%s/%s", namespace, configmapName),
		Revision:     string(stateJSON),
	}

	if err := infra.CreateRevision(revisionRecord); err != nil {
		return fmt.Errorf("failed to create revision record: %v", err)
	}

	klog.Infof("changeid %s, task name %s, recorded %s operation for ConfigMap %s/%s with complete state, data keys: %d",
		request.ChangeId, request.TaskName, operationType, namespace, configmapName, len(state.Data))

	return nil
}

// rollbackConfigMapWithRetry performs a single ConfigMap rollback attempt
func (c *ConfigMapStrategy) rollbackConfigMapWithRetry(ctx context.Context, client kubernetes.Interface, namespace, configMapName string, originalState *ConfigMapState, request *strategyprovider.TaskStrategyRequest) (string, error) {
	// 获取当前ConfigMap
	configMap, err := client.CoreV1().ConfigMaps(namespace).Get(ctx, configMapName, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return "", fmt.Errorf("ConfigMap %s/%s not found for rollback: %v", namespace, configMapName, err)
		}
		return "", fmt.Errorf("failed to get ConfigMap %s/%s for rollback: %v", namespace, configMapName, err)
	}

	// 恢复原始数据
	if configMap.Data == nil {
		configMap.Data = make(map[string]string)
	}

	// 清空当前数据并恢复原始数据
	configMap.Data = make(map[string]string)
	if originalState.Data != nil {
		for k, v := range originalState.Data {
			configMap.Data[k] = v
		}
	}

	// 执行Kubernetes API更新
	updatedConfigMap, err := client.CoreV1().ConfigMaps(namespace).Update(ctx, configMap, metav1.UpdateOptions{})
	if err != nil {
		if errors.IsConflict(err) {
			return "", fmt.Errorf("ConfigMap rollback conflict: %v", err)
		}
		return "", fmt.Errorf("failed to rollback ConfigMap %s/%s: %v", namespace, configMapName, err)
	}

	return updatedConfigMap.ResourceVersion, nil
}

// validateConfigMapRequest performs additional ConfigMap-specific request validation
func (c *ConfigMapStrategy) validateConfigMapRequest(request *strategyprovider.TaskStrategyRequest) error {
	// 检查必要的字段
	if request.Namespace == "" {
		return fmt.Errorf("namespace cannot be empty for ConfigMap operations")
	}

	if request.WorkloadName == "" {
		return fmt.Errorf("workload name (ConfigMap name) cannot be empty")
	}

	if request.ClusterId == "" {
		return fmt.Errorf("cluster ID cannot be empty")
	}

	// 检查ConfigMapData操作的合理性
	updateCount := 0
	removeCount := 0
	for _, item := range request.Extend.ConfigMapData {
		switch item.Type {
		case model.ConfigMapOperationUpdate:
			updateCount++
		case model.ConfigMapOperationRemove:
			removeCount++
		}
	}

	if updateCount == 0 && removeCount == 0 {
		return fmt.Errorf("no valid ConfigMap operations found")
	}

	return nil
}

// updateConfigMapWithRetry performs a single ConfigMap update attempt
func (c *ConfigMapStrategy) updateConfigMapWithRetry(ctx context.Context, client kubernetes.Interface, namespace, configMapName string, operations []model.ConfigMapDataItem, request *strategyprovider.TaskStrategyRequest) (string, error) {
	// 获取目标ConfigMap
	configMap, err := client.CoreV1().ConfigMaps(namespace).Get(ctx, configMapName, metav1.GetOptions{})
	if err != nil {
		if errors.IsNotFound(err) {
			return "", fmt.Errorf("ConfigMap %s/%s not found: %v", namespace, configMapName, err)
		}
		return "", fmt.Errorf("failed to get ConfigMap %s/%s: %v", namespace, configMapName, err)
	}

	if configMap.Data == nil {
		configMap.Data = make(map[string]string)
	}

	// 使用BuildNewConfigMapData函数合并数据
	newData, err := util.BuildNewConfigMapData(configMap.Data, operations)
	if err != nil {
		return "", fmt.Errorf("failed to build new ConfigMap data: %v", err)
	}

	// 更新ConfigMap的data字段
	configMap.Data = newData

	// 执行Kubernetes API更新
	updatedConfigMap, err := client.CoreV1().ConfigMaps(namespace).Update(ctx, configMap, metav1.UpdateOptions{})
	if err != nil {
		if errors.IsConflict(err) {
			return "", fmt.Errorf("ConfigMap update conflict: %v", err)
		}
		return "", fmt.Errorf("failed to update ConfigMap %s/%s: %v", namespace, configMapName, err)
	}

	return updatedConfigMap.ResourceVersion, nil
}
