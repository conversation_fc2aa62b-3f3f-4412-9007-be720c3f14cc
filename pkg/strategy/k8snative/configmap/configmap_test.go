package configmap

import (
	"context"
	"encoding/json"
	"fmt"
	"testing"

	corev1 "k8s.io/api/core/v1"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/kubernetes/fake"
	k8stesting "k8s.io/client-go/testing"

	"git.woa.com/kmetis/starship/pkg/model"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
)

func TestNewConfigMapStrategy(t *testing.T) {
	tests := []struct {
		name        string
		config      *strategyprovider.ReleaseConfig
		expectError bool
	}{
		{
			name:        "nil config",
			config:      nil,
			expectError: true,
		},
		{
			name:        "valid config",
			config:      &strategyprovider.ReleaseConfig{},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			strategy, err := NewConfigMapStrategy(tt.config)

			if tt.expectError {
				if err == nil {
					t.Errorf("expected error but got none")
				}
				if strategy != nil {
					t.Errorf("expected nil strategy but got %v", strategy)
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				if strategy == nil {
					t.Errorf("expected strategy but got nil")
				}
			}
		})
	}
}

func TestConfigMapStrategy_Validate(t *testing.T) {
	strategy := &ConfigMapStrategy{
		config: &strategyprovider.ReleaseConfig{},
	}

	tests := []struct {
		name        string
		request     *strategyprovider.TaskStrategyRequest
		expectError bool
		errorMsg    string
	}{
		{
			name: "valid request with update operation",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "test-configmap",
				WorkloadType: util.ReleaseStrategyDeployment,
				User:         "test-user",
				Location:     util.ComponentLocationUser,
				ImageTag:     "dummy-tag",
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				Extend: &model.ExtendInfo{
					ConfigMapData: []model.ConfigMapDataItem{
						{Type: model.ConfigMapOperationUpdate, Key: "config.yaml", Value: "test: value"},
					},
				},
			},
			expectError: false,
		},
		{
			name: "valid request with remove operation",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "test-configmap",
				WorkloadType: util.ReleaseStrategyDeployment,
				User:         "test-user",
				Location:     util.ComponentLocationUser,
				ImageTag:     "dummy-tag",
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				Extend: &model.ExtendInfo{
					ConfigMapData: []model.ConfigMapDataItem{
						{Type: model.ConfigMapOperationRemove, Key: "old-config.yaml"},
					},
				},
			},
			expectError: false,
		},
		{
			name: "valid request with multiple operations",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "test-configmap",
				WorkloadType: util.ReleaseStrategyDeployment,
				User:         "test-user",
				Location:     util.ComponentLocationUser,
				ImageTag:     "dummy-tag",
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				Extend: &model.ExtendInfo{
					ConfigMapData: []model.ConfigMapDataItem{
						{Type: model.ConfigMapOperationUpdate, Key: "config.yaml", Value: "new: value"},
						{Type: model.ConfigMapOperationRemove, Key: "old-config.yaml"},
						{Type: model.ConfigMapOperationUpdate, Key: "database.conf", Value: "host=localhost"},
					},
				},
			},
			expectError: false,
		},
		{
			name: "nil extend info",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "test-configmap",
				WorkloadType: util.ReleaseStrategyDeployment,
				User:         "test-user",
				Location:     util.ComponentLocationUser,
				ImageTag:     "dummy-tag",
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				Extend:       nil,
			},
			expectError: true,
			errorMsg:    "ExtendInfo cannot be nil",
		},
		{
			name: "empty ConfigMapData",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "test-configmap",
				WorkloadType: util.ReleaseStrategyDeployment,
				User:         "test-user",
				Location:     util.ComponentLocationUser,
				ImageTag:     "dummy-tag",
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				Extend: &model.ExtendInfo{
					ConfigMapData: []model.ConfigMapDataItem{},
				},
			},
			expectError: true,
			errorMsg:    "ConfigMapData cannot be empty",
		},
		{
			name: "invalid operation type",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "test-configmap",
				WorkloadType: util.ReleaseStrategyDeployment,
				User:         "test-user",
				Location:     util.ComponentLocationUser,
				ImageTag:     "dummy-tag",
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				Extend: &model.ExtendInfo{
					ConfigMapData: []model.ConfigMapDataItem{
						{Type: "invalid", Key: "config.yaml", Value: "test: value"},
					},
				},
			},
			expectError: true,
			errorMsg:    "invalid operation type",
		},
		{
			name: "empty key",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "test-configmap",
				WorkloadType: util.ReleaseStrategyDeployment,
				User:         "test-user",
				Location:     util.ComponentLocationUser,
				ImageTag:     "dummy-tag",
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				Extend: &model.ExtendInfo{
					ConfigMapData: []model.ConfigMapDataItem{
						{Type: model.ConfigMapOperationUpdate, Key: "", Value: "test: value"},
					},
				},
			},
			expectError: true,
			errorMsg:    "key cannot be empty",
		},
		{
			name: "empty value for update operation",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "test-configmap",
				WorkloadType: util.ReleaseStrategyDeployment,
				User:         "test-user",
				Location:     util.ComponentLocationUser,
				ImageTag:     "dummy-tag",
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				Extend: &model.ExtendInfo{
					ConfigMapData: []model.ConfigMapDataItem{
						{Type: model.ConfigMapOperationUpdate, Key: "config.yaml", Value: ""},
					},
				},
			},
			expectError: true,
			errorMsg:    "value cannot be empty for update operation",
		},
		{
			name: "empty namespace",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "",
				WorkloadName: "test-configmap",
				WorkloadType: util.ReleaseStrategyDeployment,
				User:         "test-user",
				Location:     util.ComponentLocationUser,
				ImageTag:     "dummy-tag",
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				Extend: &model.ExtendInfo{
					ConfigMapData: []model.ConfigMapDataItem{
						{Type: model.ConfigMapOperationUpdate, Key: "config.yaml", Value: "test: value"},
					},
				},
			},
			expectError: true,
			errorMsg:    "namespace",
		},
		{
			name: "empty workload name",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "",
				WorkloadType: util.ReleaseStrategyDeployment,
				User:         "test-user",
				Location:     util.ComponentLocationUser,
				ImageTag:     "dummy-tag",
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				Extend: &model.ExtendInfo{
					ConfigMapData: []model.ConfigMapDataItem{
						{Type: model.ConfigMapOperationUpdate, Key: "config.yaml", Value: "test: value"},
					},
				},
			},
			expectError: true,
			errorMsg:    "workload name",
		},
		{
			name: "empty cluster ID",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "test-configmap",
				WorkloadType: util.ReleaseStrategyDeployment,
				User:         "test-user",
				Location:     util.ComponentLocationUser,
				ImageTag:     "dummy-tag",
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				Extend: &model.ExtendInfo{
					ConfigMapData: []model.ConfigMapDataItem{
						{Type: model.ConfigMapOperationUpdate, Key: "config.yaml", Value: "test: value"},
					},
				},
			},
			expectError: true,
			errorMsg:    "cluster id",
		},
		{
			name: "invalid key format",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "test-configmap",
				WorkloadType: util.ReleaseStrategyDeployment,
				User:         "test-user",
				Location:     util.ComponentLocationUser,
				ImageTag:     "dummy-tag",
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				Extend: &model.ExtendInfo{
					ConfigMapData: []model.ConfigMapDataItem{
						{Type: model.ConfigMapOperationUpdate, Key: "-invalid-key", Value: "test: value"},
					},
				},
			},
			expectError: true,
			errorMsg:    "invalid key format",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := strategy.Validate(context.Background(), tt.request)

			if tt.expectError {
				if err == nil {
					t.Errorf("expected error but got none")
				} else if tt.errorMsg != "" && !containsString(err.Error(), tt.errorMsg) {
					t.Errorf("expected error message to contain '%s', got: %v", tt.errorMsg, err)
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
			}
		})
	}
}

// Helper function to check if a string contains a substring
func containsString(s, substr string) bool {
	return len(s) >= len(substr) && (s == substr || len(substr) == 0 ||
		(len(s) > len(substr) && (s[:len(substr)] == substr || s[len(s)-len(substr):] == substr ||
			func() bool {
				for i := 0; i <= len(s)-len(substr); i++ {
					if s[i:i+len(substr)] == substr {
						return true
					}
				}
				return false
			}())))
}

func TestConfigMapStrategy_Exec(t *testing.T) {
	strategy := &ConfigMapStrategy{
		config: &strategyprovider.ReleaseConfig{},
	}

	tests := []struct {
		name        string
		request     *strategyprovider.TaskStrategyRequest
		expectError bool
		errorMsg    string
	}{
		{
			name: "non-upgrade action should be ignored",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "test-configmap",
				WorkloadType: util.ReleaseStrategyDeployment,
				User:         "test-user",
				Location:     util.ComponentLocationUser,
				Action:       util.TaskActionPreCheck,
				TaskId:       123,
				SubTaskId:    456,
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				ImageTag:     "dummy-tag",
				Extend: &model.ExtendInfo{
					ConfigMapData: []model.ConfigMapDataItem{
						{Type: model.ConfigMapOperationUpdate, Key: "config.yaml", Value: "test: value"},
					},
				},
			},
			expectError: false,
		},
		{
			name: "postcheck action should be ignored",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "test-configmap",
				WorkloadType: util.ReleaseStrategyDeployment,
				User:         "test-user",
				Location:     util.ComponentLocationUser,
				Action:       util.TaskActionPostCheck,
				TaskId:       123,
				SubTaskId:    456,
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				ImageTag:     "dummy-tag",
				Extend: &model.ExtendInfo{
					ConfigMapData: []model.ConfigMapDataItem{
						{Type: model.ConfigMapOperationUpdate, Key: "config.yaml", Value: "test: value"},
					},
				},
			},
			expectError: false,
		},
		{
			name: "upgrade action with network call failure",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "test-configmap",
				WorkloadType: util.ReleaseStrategyDeployment,
				User:         "test-user",
				Location:     util.ComponentLocationUser,
				Action:       util.TaskActionUpgrade,
				TaskId:       123,
				SubTaskId:    456,
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				ImageTag:     "dummy-tag",
				Extend: &model.ExtendInfo{
					ConfigMapData: []model.ConfigMapDataItem{
						{Type: model.ConfigMapOperationUpdate, Key: "config.yaml", Value: "test: value"},
					},
				},
			},
			expectError: true,
			errorMsg:    "failed to get k8s client",
		},
		{
			name: "invalid request - empty namespace",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "",
				WorkloadName: "test-configmap",
				User:         "test-user",
				Location:     util.ComponentLocationUser,
				Action:       util.TaskActionUpgrade,
				TaskId:       123,
				SubTaskId:    456,
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				ImageTag:     "dummy-tag",
				Extend: &model.ExtendInfo{
					ConfigMapData: []model.ConfigMapDataItem{
						{Type: model.ConfigMapOperationUpdate, Key: "config.yaml", Value: "test: value"},
					},
				},
			},
			expectError: true,
			errorMsg:    "validate failed",
		},
		{
			name: "invalid request - nil extend info",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "test-configmap",
				User:         "test-user",
				Location:     util.ComponentLocationUser,
				Action:       util.TaskActionUpgrade,
				TaskId:       123,
				SubTaskId:    456,
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				ImageTag:     "dummy-tag",
				Extend:       nil,
			},
			expectError: true,
			errorMsg:    "validate failed",
		},
		{
			name: "invalid request - empty ConfigMapData",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "test-configmap",
				WorkloadType: util.ReleaseStrategyDeployment,
				User:         "test-user",
				Location:     util.ComponentLocationUser,
				Action:       util.TaskActionUpgrade,
				TaskId:       123,
				SubTaskId:    456,
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				ImageTag:     "dummy-tag",
				Extend: &model.ExtendInfo{
					ConfigMapData: []model.ConfigMapDataItem{},
				},
			},
			expectError: true,
			errorMsg:    "validate failed",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			reply, err := strategy.Exec(context.Background(), tt.request)

			if tt.expectError {
				if err == nil {
					t.Errorf("expected error but got none")
				} else if tt.errorMsg != "" && !containsString(err.Error(), tt.errorMsg) {
					t.Errorf("expected error message to contain '%s', got: %v", tt.errorMsg, err)
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				if reply == nil {
					t.Errorf("expected reply but got nil")
				}
				// For non-upgrade actions, verify the reply contains the expected message
				if tt.request.Action != util.TaskActionUpgrade && reply != nil {
					expectedMsg := fmt.Sprintf("Action %s ignored for ConfigMap strategy", tt.request.Action)
					if reply.Detail != expectedMsg {
						t.Errorf("expected reply detail '%s', got '%s'", expectedMsg, reply.Detail)
					}
				}
			}
		})
	}
}

func TestConfigMapStrategy_Rollback(t *testing.T) {
	strategy := &ConfigMapStrategy{
		config: &strategyprovider.ReleaseConfig{},
	}

	tests := []struct {
		name        string
		request     *strategyprovider.TaskStrategyRequest
		expectError bool
		errorMsg    string
	}{
		{
			name: "valid rollback request - will fail due to missing revision history",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "test-configmap",
				WorkloadType: util.ReleaseStrategyDeployment,
				User:         "test-user",
				Location:     util.ComponentLocationUser,
				Action:       util.TaskActionRollback,
				TaskId:       123,
				SubTaskId:    456,
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				ImageTag:     "dummy-tag",
				Extend: &model.ExtendInfo{
					Rollback: map[string]string{
						"TaskId": "789",
					},
				},
			},
			expectError: true,
			errorMsg:    "failed to get k8s client",
		},
		{
			name: "invalid action - not rollback",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "test-configmap",
				WorkloadType: util.ReleaseStrategyDeployment,
				User:         "test-user",
				Location:     util.ComponentLocationUser,
				Action:       util.TaskActionUpgrade,
				TaskId:       123,
				SubTaskId:    456,
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				ImageTag:     "dummy-tag",
				Extend: &model.ExtendInfo{
					Rollback: map[string]string{
						"TaskId": "789",
					},
				},
			},
			expectError: true,
			errorMsg:    "invalid action for rollback",
		},
		{
			name: "invalid request - empty namespace",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "",
				WorkloadName: "test-configmap",
				User:         "test-user",
				Location:     util.ComponentLocationUser,
				Action:       util.TaskActionRollback,
				TaskId:       123,
				SubTaskId:    456,
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				ImageTag:     "dummy-tag",
				Extend: &model.ExtendInfo{
					Rollback: map[string]string{
						"TaskId": "789",
					},
				},
			},
			expectError: true,
			errorMsg:    "rollback validate failed",
		},
		{
			name: "missing original task ID",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "test-configmap",
				WorkloadType: util.ReleaseStrategyDeployment,
				User:         "test-user",
				Location:     util.ComponentLocationUser,
				Action:       util.TaskActionRollback,
				TaskId:       123,
				SubTaskId:    456,
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				ImageTag:     "dummy-tag",
				Extend: &model.ExtendInfo{
					Rollback: map[string]string{},
				},
			},
			expectError: true,
			errorMsg:    "failed to get k8s client",
		},
		{
			name: "nil extend info",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "test-configmap",
				WorkloadType: util.ReleaseStrategyDeployment,
				User:         "test-user",
				Location:     util.ComponentLocationUser,
				Action:       util.TaskActionRollback,
				TaskId:       123,
				SubTaskId:    456,
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				ImageTag:     "dummy-tag",
				Extend:       nil,
			},
			expectError: true,
			errorMsg:    "failed to get k8s client",
		},
		{
			name: "invalid original task ID format",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "test-configmap",
				WorkloadType: util.ReleaseStrategyDeployment,
				User:         "test-user",
				Location:     util.ComponentLocationUser,
				Action:       util.TaskActionRollback,
				TaskId:       123,
				SubTaskId:    456,
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				ImageTag:     "dummy-tag",
				Extend: &model.ExtendInfo{
					Rollback: map[string]string{
						"TaskId": "invalid-id",
					},
				},
			},
			expectError: true,
			errorMsg:    "failed to get k8s client",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			reply, err := strategy.Rollback(context.Background(), tt.request)

			if tt.expectError {
				if err == nil {
					t.Errorf("expected error but got none")
				} else if tt.errorMsg != "" && !containsString(err.Error(), tt.errorMsg) {
					t.Errorf("expected error message to contain '%s', got: %v", tt.errorMsg, err)
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				if reply == nil {
					t.Errorf("expected reply but got nil")
				}
			}
		})
	}
}

func TestConfigMapStrategy_getConfigMapRevision(t *testing.T) {
	strategy := &ConfigMapStrategy{
		config: &strategyprovider.ReleaseConfig{},
	}

	// Test with nil client - should panic and be recovered
	defer func() {
		if r := recover(); r == nil {
			t.Error("Expected panic with nil client, but no panic occurred")
		}
	}()

	// This will panic due to nil client, which is expected
	_, _ = strategy.getConfigMapRevision(context.Background(), nil, "default", "test-configmap")

	// Note: Testing with real Kubernetes client would require a test cluster
	// The function is designed to work with real Kubernetes API calls
}

func TestConfigMapState_Serialization(t *testing.T) {
	// Test ConfigMapState JSON serialization/deserialization
	originalState := &ConfigMapState{
		Data: map[string]string{
			"config.yaml":   "server:\n  port: 8080",
			"database.conf": "host=localhost\nport=3306",
		},
		ResourceVersion: "12345",
		Namespace:       "default",
		Name:            "test-configmap",
	}

	// Serialize to JSON
	jsonData, err := json.Marshal(originalState)
	if err != nil {
		t.Fatalf("Failed to marshal ConfigMapState: %v", err)
	}

	// Deserialize from JSON
	var restoredState ConfigMapState
	err = json.Unmarshal(jsonData, &restoredState)
	if err != nil {
		t.Fatalf("Failed to unmarshal ConfigMapState: %v", err)
	}

	// Verify data integrity
	if restoredState.ResourceVersion != originalState.ResourceVersion {
		t.Errorf("ResourceVersion mismatch: expected %s, got %s",
			originalState.ResourceVersion, restoredState.ResourceVersion)
	}

	if restoredState.Namespace != originalState.Namespace {
		t.Errorf("Namespace mismatch: expected %s, got %s",
			originalState.Namespace, restoredState.Namespace)
	}

	if restoredState.Name != originalState.Name {
		t.Errorf("Name mismatch: expected %s, got %s",
			originalState.Name, restoredState.Name)
	}

	if len(restoredState.Data) != len(originalState.Data) {
		t.Errorf("Data length mismatch: expected %d, got %d",
			len(originalState.Data), len(restoredState.Data))
	}

	for key, expectedValue := range originalState.Data {
		if actualValue, exists := restoredState.Data[key]; !exists {
			t.Errorf("Missing key in restored data: %s", key)
		} else if actualValue != expectedValue {
			t.Errorf("Value mismatch for key %s: expected %s, got %s",
				key, expectedValue, actualValue)
		}
	}
}

func TestConfigMapStrategy_validateConfigMapRequest(t *testing.T) {
	strategy := &ConfigMapStrategy{
		config: &strategyprovider.ReleaseConfig{},
	}

	tests := []struct {
		name        string
		request     *strategyprovider.TaskStrategyRequest
		expectError bool
		errorMsg    string
	}{
		{
			name: "valid request with update operations",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "test-configmap",
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				Extend: &model.ExtendInfo{
					ConfigMapData: []model.ConfigMapDataItem{
						{Type: model.ConfigMapOperationUpdate, Key: "config.yaml", Value: "test: value"},
						{Type: model.ConfigMapOperationUpdate, Key: "database.conf", Value: "host=localhost"},
					},
				},
			},
			expectError: false,
		},
		{
			name: "valid request with remove operations",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "test-configmap",
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				Extend: &model.ExtendInfo{
					ConfigMapData: []model.ConfigMapDataItem{
						{Type: model.ConfigMapOperationRemove, Key: "old-config.yaml"},
						{Type: model.ConfigMapOperationRemove, Key: "deprecated.conf"},
					},
				},
			},
			expectError: false,
		},
		{
			name: "valid request with mixed operations",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "test-configmap",
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				Extend: &model.ExtendInfo{
					ConfigMapData: []model.ConfigMapDataItem{
						{Type: model.ConfigMapOperationUpdate, Key: "config.yaml", Value: "new: value"},
						{Type: model.ConfigMapOperationRemove, Key: "old-config.yaml"},
					},
				},
			},
			expectError: false,
		},
		{
			name: "empty namespace",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "",
				WorkloadName: "test-configmap",
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				Extend: &model.ExtendInfo{
					ConfigMapData: []model.ConfigMapDataItem{
						{Type: model.ConfigMapOperationUpdate, Key: "config.yaml", Value: "test: value"},
					},
				},
			},
			expectError: true,
			errorMsg:    "namespace cannot be empty",
		},
		{
			name: "empty workload name",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "",
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				Extend: &model.ExtendInfo{
					ConfigMapData: []model.ConfigMapDataItem{
						{Type: model.ConfigMapOperationUpdate, Key: "config.yaml", Value: "test: value"},
					},
				},
			},
			expectError: true,
			errorMsg:    "workload name (ConfigMap name) cannot be empty",
		},
		{
			name: "empty cluster ID",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "test-configmap",
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				Extend: &model.ExtendInfo{
					ConfigMapData: []model.ConfigMapDataItem{
						{Type: model.ConfigMapOperationUpdate, Key: "config.yaml", Value: "test: value"},
					},
				},
			},
			expectError: true,
			errorMsg:    "cluster ID cannot be empty",
		},
		{
			name: "no valid operations",
			request: &strategyprovider.TaskStrategyRequest{
				ClusterId:    "test-cluster",
				Component:    "test-component",
				Namespace:    "default",
				WorkloadName: "test-configmap",
				ChangeId:     "test-change-id",
				TaskName:     "test-task",
				Extend: &model.ExtendInfo{
					ConfigMapData: []model.ConfigMapDataItem{},
				},
			},
			expectError: true,
			errorMsg:    "no valid ConfigMap operations found",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := strategy.validateConfigMapRequest(tt.request)

			if tt.expectError {
				if err == nil {
					t.Errorf("expected error but got none")
				} else if tt.errorMsg != "" && !containsString(err.Error(), tt.errorMsg) {
					t.Errorf("expected error message to contain '%s', got: %v", tt.errorMsg, err)
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
			}
		})
	}
}

func TestConfigMapStrategy_getConfigMapRevision_WithMockClient(t *testing.T) {
	strategy := &ConfigMapStrategy{
		config: &strategyprovider.ReleaseConfig{},
	}

	tests := []struct {
		name        string
		namespace   string
		configName  string
		configMap   *corev1.ConfigMap
		expectError bool
		errorMsg    string
	}{
		{
			name:       "successful retrieval",
			namespace:  "default",
			configName: "test-configmap",
			configMap: &corev1.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Name:            "test-configmap",
					Namespace:       "default",
					ResourceVersion: "12345",
				},
				Data: map[string]string{
					"config.yaml":   "server:\n  port: 8080",
					"database.conf": "host=localhost\nport=3306",
				},
			},
			expectError: false,
		},
		{
			name:        "configmap not found",
			namespace:   "default",
			configName:  "nonexistent-configmap",
			configMap:   nil,
			expectError: true,
			errorMsg:    "not found",
		},
		{
			name:       "configmap with nil data",
			namespace:  "default",
			configName: "empty-configmap",
			configMap: &corev1.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Name:            "empty-configmap",
					Namespace:       "default",
					ResourceVersion: "67890",
				},
				Data: nil,
			},
			expectError: false,
		},
		{
			name:       "configmap with empty data",
			namespace:  "default",
			configName: "empty-data-configmap",
			configMap: &corev1.ConfigMap{
				ObjectMeta: metav1.ObjectMeta{
					Name:            "empty-data-configmap",
					Namespace:       "default",
					ResourceVersion: "11111",
				},
				Data: map[string]string{},
			},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create fake client
			var objects []runtime.Object
			if tt.configMap != nil {
				objects = append(objects, tt.configMap)
			}
			fakeClient := fake.NewSimpleClientset(objects...)

			// Add reactor for not found case
			if tt.configMap == nil {
				fakeClient.PrependReactor("get", "configmaps", func(action k8stesting.Action) (handled bool, ret runtime.Object, err error) {
					return true, nil, k8serrors.NewNotFound(schema.GroupResource{Group: "", Resource: "configmaps"}, tt.configName)
				})
			}

			state, err := strategy.getConfigMapRevision(context.Background(), fakeClient, tt.namespace, tt.configName)

			if tt.expectError {
				if err == nil {
					t.Errorf("expected error but got none")
				} else if tt.errorMsg != "" && !containsString(err.Error(), tt.errorMsg) {
					t.Errorf("expected error message to contain '%s', got: %v", tt.errorMsg, err)
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
				if state == nil {
					t.Errorf("expected state but got nil")
				} else {
					// Verify state properties
					if state.Namespace != tt.namespace {
						t.Errorf("expected namespace %s, got %s", tt.namespace, state.Namespace)
					}
					if state.Name != tt.configName {
						t.Errorf("expected name %s, got %s", tt.configName, state.Name)
					}
					if tt.configMap != nil {
						if state.ResourceVersion != tt.configMap.ResourceVersion {
							t.Errorf("expected resource version %s, got %s", tt.configMap.ResourceVersion, state.ResourceVersion)
						}
						// Verify data deep copy
						if tt.configMap.Data != nil {
							if len(state.Data) != len(tt.configMap.Data) {
								t.Errorf("expected data length %d, got %d", len(tt.configMap.Data), len(state.Data))
							}
							for key, expectedValue := range tt.configMap.Data {
								if actualValue, exists := state.Data[key]; !exists {
									t.Errorf("missing key in state data: %s", key)
								} else if actualValue != expectedValue {
									t.Errorf("value mismatch for key %s: expected %s, got %s", key, expectedValue, actualValue)
								}
							}
						} else {
							if len(state.Data) != 0 {
								t.Errorf("expected empty data but got %d items", len(state.Data))
							}
						}
					}
				}
			}
		})
	}
}

func TestConfigMapStrategy_ErrorScenarios(t *testing.T) {
	strategy := &ConfigMapStrategy{
		config: &strategyprovider.ReleaseConfig{},
	}

	t.Run("ConfigMap not found error handling", func(t *testing.T) {
		request := &strategyprovider.TaskStrategyRequest{
			ClusterId:    "test-cluster",
			Component:    "test-component",
			Namespace:    "default",
			WorkloadName: "nonexistent-configmap",
			WorkloadType: util.ReleaseStrategyDeployment,
			User:         "test-user",
			Location:     util.ComponentLocationUser,
			Action:       util.TaskActionUpgrade,
			TaskId:       123,
			SubTaskId:    456,
			ChangeId:     "test-change-id",
			TaskName:     "test-task",
			ImageTag:     "dummy-tag",
			Extend: &model.ExtendInfo{
				ConfigMapData: []model.ConfigMapDataItem{
					{Type: model.ConfigMapOperationUpdate, Key: "config.yaml", Value: "test: value"},
				},
			},
		}

		// This will fail because util.GetTargetK8sClient will fail in test environment
		_, err := strategy.Exec(context.Background(), request)
		if err == nil {
			t.Errorf("expected error for nonexistent ConfigMap but got none")
		}
		if !containsString(err.Error(), "failed to get k8s client") {
			t.Errorf("expected 'failed to get k8s client' error, got: %v", err)
		}
	})

	t.Run("Permission denied error handling", func(t *testing.T) {
		request := &strategyprovider.TaskStrategyRequest{
			ClusterId:    "test-cluster",
			Component:    "test-component",
			Namespace:    "restricted-namespace",
			WorkloadName: "test-configmap",
			WorkloadType: util.ReleaseStrategyDeployment,
			User:         "test-user",
			Location:     util.ComponentLocationUser,
			Action:       util.TaskActionUpgrade,
			TaskId:       123,
			SubTaskId:    456,
			ChangeId:     "test-change-id",
			TaskName:     "test-task",
			ImageTag:     "dummy-tag",
			Extend: &model.ExtendInfo{
				ConfigMapData: []model.ConfigMapDataItem{
					{Type: model.ConfigMapOperationUpdate, Key: "config.yaml", Value: "test: value"},
				},
			},
		}

		// This will fail because util.GetTargetK8sClient will fail in test environment
		_, err := strategy.Exec(context.Background(), request)
		if err == nil {
			t.Errorf("expected error for permission denied but got none")
		}
	})

	t.Run("Invalid task and subtask IDs", func(t *testing.T) {
		request := &strategyprovider.TaskStrategyRequest{
			ClusterId:    "test-cluster",
			Component:    "test-component",
			Namespace:    "default",
			WorkloadName: "test-configmap",
			WorkloadType: util.ReleaseStrategyDeployment,
			User:         "test-user",
			Location:     util.ComponentLocationUser,
			Action:       util.TaskActionUpgrade,
			TaskId:       0, // Invalid task ID
			SubTaskId:    0, // Invalid subtask ID
			ChangeId:     "test-change-id",
			TaskName:     "test-task",
			ImageTag:     "dummy-tag",
			Extend: &model.ExtendInfo{
				ConfigMapData: []model.ConfigMapDataItem{
					{Type: model.ConfigMapOperationUpdate, Key: "config.yaml", Value: "test: value"},
				},
			},
		}

		// This will fail because util.GetTargetK8sClient will fail in test environment
		_, err := strategy.Exec(context.Background(), request)
		if err == nil {
			t.Errorf("expected error for invalid task IDs but got none")
		}
	})
}
