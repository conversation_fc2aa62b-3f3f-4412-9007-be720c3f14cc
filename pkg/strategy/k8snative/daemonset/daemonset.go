package daemonset

import (
	"context"
	"fmt"
	"math"
	"strconv"
	"strings"

	appsv1 "k8s.io/api/apps/v1"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/client-go/kubernetes"
	"k8s.io/klog/v2"
	applicationv1 "tkestack.io/tke/api/application/v1"

	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/kmetis/starship/pkg/util/kubeclient"
)

const (
	ProviderName          = "daemonset"
	MaxNodesForRolling    = 100  // 使用RollingUpdate的最大节点数
	MaxRollingUpdateRatio = 0.05 // RollingUpdate最大比例

	UpgradeJobLabelPrefix         = "upgradejob.starship.tkestack.io/"
	UpgradeJobDsNameLabel         = UpgradeJobLabelPrefix + "dsname"
	UpgradeJobDsGenerationLabel   = UpgradeJobLabelPrefix + "dsgeneration"
	UpgradeJobStarshipTaskIdLabel = UpgradeJobLabelPrefix + "taskid"
	UpgradeJobComponentNameLabel  = UpgradeJobLabelPrefix + "component"
)

func init() {
	strategyprovider.RegisterReleaseStrategyFactory(
		ProviderName,
		func(ctx *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
			return NewDaemonSetStrategy(ctx)
		},
	)
}

type DaemonSetStrategy struct {
}

func NewDaemonSetStrategy(ctx *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
	return &DaemonSetStrategy{}, nil
}

func (d *DaemonSetStrategy) Validate(ctx context.Context, request *strategyprovider.TaskStrategyRequest) error {
	return util.ValidateTaskStrategyRequest(request)
}

func (d *DaemonSetStrategy) Exec(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	if err := d.Validate(ctx, request); err != nil {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,validate failed", request.ChangeId, request.TaskName, request.ClusterId, request.Component)
		return nil, fmt.Errorf("validate failed:%v", err)
	}

	if request.Action != util.TaskActionUpgrade {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,action %s ignore", request.ChangeId, request.TaskName, request.ClusterId, request.Component, request.Action)
		return nil, nil
	}

	client, err := kubeclient.GetTargetK8sClient(request)
	if err != nil {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,failed to get k8s client,err is %v", request.ChangeId, request.TaskName, request.ClusterId, request.Component, err)
		return nil, fmt.Errorf("failed to get k8s client,err is %v", err)
	}

	clusterId := request.ClusterId
	component := request.Component
	namespace := util.GetComponentNamespace(clusterId, request.Namespace)
	workloadName := util.GetWorkloadName(clusterId, request.WorkloadName)

	ds, err := client.AppsV1().DaemonSets(namespace).Get(ctx, workloadName, metav1.GetOptions{})
	if err != nil {
		klog.Errorf("failed to get daemonset,cluster %s,component %s,workload name %s, err is %v", clusterId, component, workloadName, err)
		return nil, err
	}

	newDs := ds.DeepCopy()
	if err := util.UpdatePodTemplate(&newDs.Spec.Template, request); err != nil {
		klog.Errorf("failed to update pod template, cluster %s, component %s, workload name %s, err is %v", clusterId, component, workloadName, err)
		return nil, err
	}

	nodeCount, err := getNodeCount(client)
	if err != nil {
		klog.Errorf("failed to get node count, cluster %s, err is %v", clusterId, err)
		return nil, err
	}

	// 根据节点数和当前更新策略决定更新方式
	if err := d.decideUpgradeStrategy(nodeCount, request, newDs); err != nil {
		klog.Errorf("failed to decide upgrade strategy, cluster %s, err is %v", clusterId, err)
		return nil, err
	}

	updateDs, err := client.AppsV1().DaemonSets(namespace).Update(ctx, newDs, metav1.UpdateOptions{})
	if err != nil {
		klog.Errorf("failed to update daemonset,cluster %s,component %s,err is %v", clusterId, component, err)
		return nil, err
	}

	// 如果是OnDelete模式，创建升级Job
	if updateDs.Spec.UpdateStrategy.Type == appsv1.OnDeleteDaemonSetStrategyType {
		if err := createUpgradeJobsIfNotExist(ctx, nodeCount, request, updateDs); err != nil {
			klog.Errorf("failed to create upgrade jobs for daemonset %s/%s, err: %v", namespace, workloadName, err)
			return nil, err
		}
	}

	klog.Infof("finish to update daemonset,traceId %s,task name %s,cluster %s,component %s,workload name %s",
		request.TraceId, request.TaskName, clusterId, component, workloadName)

	return &strategyprovider.TaskStrategyReply{
		Detail: string(updateDs.Spec.UpdateStrategy.Type),
	}, nil
}

func (d *DaemonSetStrategy) decideUpgradeStrategy(nodeCount int, request *strategyprovider.TaskStrategyRequest, ds *appsv1.DaemonSet) error {
	// 如果指定了升级策略
	if request.Extend != nil && request.Extend.UpdateStrategy != "" {
		switch request.Extend.UpdateStrategy {
		case string(appsv1.OnDeleteDaemonSetStrategyType):
			ds.Spec.UpdateStrategy.Type = appsv1.OnDeleteDaemonSetStrategyType
			ds.Spec.UpdateStrategy.RollingUpdate = nil
			return nil
		case string(appsv1.RollingUpdateDaemonSetStrategyType):
			if nodeCount > MaxNodesForRolling {
				return fmt.Errorf("node count (%d) exceeds rolling update maximum limit (%d), please use OnDelete strategy",
					nodeCount, MaxNodesForRolling)
			}

			if ds.Spec.UpdateStrategy.RollingUpdate == nil {
				ds.Spec.UpdateStrategy.RollingUpdate = &appsv1.RollingUpdateDaemonSet{}
			}

			// 原策略是RollingUpdate时，检查现有配置
			if ds.Spec.UpdateStrategy.Type == appsv1.RollingUpdateDaemonSetStrategyType {
				if ds.Spec.UpdateStrategy.RollingUpdate.MaxUnavailable != nil {
					// 处理百分比情况
					if ds.Spec.UpdateStrategy.RollingUpdate.MaxUnavailable.Type == intstr.String {
						strValue := ds.Spec.UpdateStrategy.RollingUpdate.MaxUnavailable.StrVal
						if !strings.HasSuffix(strValue, "%") {
							return fmt.Errorf("invalid percentage format: %s", strValue)
						}

						percentStr := strings.TrimSuffix(strValue, "%")
						percent, err := strconv.ParseFloat(percentStr, 64)
						if err != nil {
							return fmt.Errorf("invalid percentage value: %v", err)
						}

						if percent > MaxRollingUpdateRatio*100 {
							return fmt.Errorf("MaxUnavailable percentage (%.1f%%) exceeds maximum limit (%.1f%%)",
								percent, MaxRollingUpdateRatio*100)
						}
					} else {
						// 处理整数值情况
						currentUnavailable := ds.Spec.UpdateStrategy.RollingUpdate.MaxUnavailable.IntValue()
						maxAllowed := int(math.Ceil(MaxRollingUpdateRatio * float64(nodeCount)))
						if currentUnavailable > maxAllowed {
							return fmt.Errorf("MaxUnavailable count (%d) exceeds maximum allowed value (%d)",
								currentUnavailable, maxAllowed)
						}
					}
				}
			} else {
				return fmt.Errorf("updateStrategy is not match")
			}
			return nil
		default:
			return fmt.Errorf("unsupported upgrade strategy: %s", request.Extend.UpdateStrategy)
		}
	}
	// 默认设置为OnDelete模式
	ds.Spec.UpdateStrategy.Type = appsv1.OnDeleteDaemonSetStrategyType
	ds.Spec.UpdateStrategy.RollingUpdate = nil
	return nil
}

func getNodeCount(client kubernetes.Interface) (int, error) {
	nodes, err := client.CoreV1().Nodes().List(context.TODO(), metav1.ListOptions{
		LabelSelector:   "node.kubernetes.io/instance-type!=eklet",
		ResourceVersion: "0",
	})
	if err != nil {
		return 0, fmt.Errorf("failed to get node count: %v", err)
	}

	return len(nodes.Items), nil
}

func createUpgradeJobsIfNotExist(ctx context.Context, nodeCount int, request *strategyprovider.TaskStrategyRequest, ds *appsv1.DaemonSet) error {
	client, err := kubeclient.GetApplicationClient(request.ProductName)
	if err != nil {
		return err
	}

	jobName := fmt.Sprintf("%s-%s-%d", request.ClusterId, ds.Name, ds.Generation)
	// 检查是否已存在相同版本的Job
	uj, err := client.ApplicationV1().UpgradeJobs(request.ClusterId).Get(ctx, jobName, metav1.GetOptions{})
	if err != nil && !errors.IsNotFound(err) {
		klog.Errorf("get UpgradeJob for app %s/%s failed: %v", ds.Namespace, ds.Name, err)
		return err
	}

	// job存在
	if err == nil {
		// Job存在且运行中，直接返回
		if uj.Spec.Pause == false {
			klog.Infof("UpgradeJob %s already exists, skip creation", jobName)
			return nil
		}

		// job存在，但暂停了
		uj.Spec.Pause = false
		uj.Status.Reason = new(string)
		_, err = client.ApplicationV1().UpgradeJobs(request.ClusterId).Update(ctx, uj, metav1.UpdateOptions{})
		if err != nil {
			klog.Errorf("update UpgradeJob for app %s/%s failed: %v", ds.Namespace, ds.Name, err)
			return err
		}
		return nil
	}

	// 删除旧版本的Jobs
	ujs, err := client.ApplicationV1().UpgradeJobs(request.ClusterId).List(ctx, metav1.ListOptions{
		LabelSelector: labels.SelectorFromSet(labels.Set{
			UpgradeJobDsNameLabel: ds.Name,
		}).String(),
	})
	if err != nil {
		klog.Errorf("get UpgradeJobs for daemonset %s/%s failed: %v", ds.Namespace, ds.Name, err)
		return err
	}

	for _, uj := range ujs.Items {
		if uj.Labels[UpgradeJobDsGenerationLabel] != strconv.Itoa(int(ds.Generation)) {
			if err := client.ApplicationV1().UpgradeJobs(uj.Namespace).Delete(ctx, uj.Name, metav1.DeleteOptions{}); err != nil {
				klog.Warningf("delete old UpgradeJob %s failed: %v", uj.Name, err)
			}
		}
	}

	// 计算分批参数
	batchNum, batchInterval, maxSurge, maxFailed := calculateSmartBatchStrategy(nodeCount)
	klog.Infof("Calculated batch strategy for %d nodes: batches=%d, interval=%ds, surge=%d",
		nodeCount, batchNum, batchInterval, maxSurge)

	uj = &applicationv1.UpgradeJob{
		TypeMeta: metav1.TypeMeta{
			Kind:       "UpgradeJob",
			APIVersion: "application.tkestack.io/v1",
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      jobName,
			Namespace: request.ClusterId,
			Labels: map[string]string{
				UpgradeJobDsNameLabel:         ds.Name,
				UpgradeJobDsGenerationLabel:   strconv.Itoa(int(ds.Generation)),
				UpgradeJobStarshipTaskIdLabel: strconv.Itoa(int(request.TaskId)),
				UpgradeJobComponentNameLabel:  request.Component,
			},
		},
		Spec: applicationv1.UpgradeJobSpec{
			TenantID:  "",
			Target:    ds.Namespace + "/" + ds.Name,
			Reference: "starship-daemonset",

			BatchNum:             int32Ptr(batchNum),
			BatchIntervalSeconds: int32Ptr(batchInterval),
			MaxFailed:            int32Ptr(maxFailed),
			MaxSurge:             int32Ptr(maxSurge),
		},
	}

	_, err = client.ApplicationV1().UpgradeJobs(request.ClusterId).Create(ctx, uj, metav1.CreateOptions{})
	if err != nil {
		klog.Errorf("create UpgradeJob for app %s/%s failed: %v", ds.Namespace, ds.Name, err)
		return err
	}

	return nil
}

func int32Ptr(i int32) *int32 {
	return &i
}

// 基于对数增长公式设计，节点越多批次越多
// 参数策略逻辑：
// -----------------------------------------------------------------
// | 集群规模 (节点数)   | batchNum | batchInterval | maxSurge | maxFailed |
// |-------------------|----------|---------------|----------|-----------|
// | 1                 | 1        | 310s          | 10       | 0         |
// | 10                | 3        | 330s          | 10       | 2         |
// | 100               | 7        | 370s          | 10       | 4         |
// | 500               | 13       | 430s          | 10       | 8         |
// | 1000              | 19       | 490s          | 10       | 10         |
// | 2000              | 30       | 600s          | 10       | 10         |
// | 2001              | 30       | 600s          | 15       | 10         |
// | 5000              | 60       | 600s          | 15       | 10         |
// | 10000+            | 60(max)  | 600s(max)     | 15       | 10(max)    |
// -----------------------------------------------------------------------
//
// 核心规则：
// 1. 批次数量 = floor(3*lg(n) + n/100) + 1，限制在[1,60]
// 2. 间隔时间 = 300 + 批次*10秒，限制在[330,600]
// 3. 最大并发 = 10（节点≤2000时），15（节点>2000时）
// 4. 最大失败数 = min(10, max(1, ceil(√n/3)))
// 注：间隔时间先统一调整为60s，后续根据业务再进行调整
func calculateSmartBatchStrategy(nodeCount int) (batchNum, batchInterval, maxSurge, maxFailed int32) {
	// 计算基础批次
	logPart := math.Log(float64(nodeCount)) / math.Log(10.0) * 3.0
	linearPart := float64(nodeCount) * 0.01
	batchNum = int32(math.Round(logPart + linearPart))

	if nodeCount > 1 && batchNum < 2 {
		batchNum = 2
	} else if batchNum < 1 {
		batchNum = 1
	}
	if batchNum > 60 {
		batchNum = 60
	}

	// 并发控制
	maxSurge = 10
	if nodeCount > 2000 {
		maxSurge = 15
	}

	// 间隔时间计算 (间隔时间太长，业务无法接受, 先将时间设置为60s，后续根据业务再进行调整)
	//batchInterval = 300 + batchNum*10
	//if batchInterval < 300 {
	//	batchInterval = 300
	//}
	//if batchInterval > 600 {
	//	batchInterval = 600
	//}
	batchInterval = 60

	// 计算失败数（基于节点数的平方根）
	maxFailed = int32(math.Ceil(math.Sqrt(float64(nodeCount)) / 3))
	if maxFailed < 1 {
		maxFailed = 1
	}
	if maxFailed > 10 {
		maxFailed = 10
	}
	if nodeCount == 1 {
		maxFailed = 0
	}

	return batchNum, batchInterval, maxSurge, maxFailed
}

func (d *DaemonSetStrategy) Rollback(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	return nil, fmt.Errorf("not implement")
}
