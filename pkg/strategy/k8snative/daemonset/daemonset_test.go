package daemonset

import (
	"testing"
)

func TestCalculateSmartBatchStrategy(t *testing.T) {
	tests := []struct {
		name         string
		nodeCount    int
		wantBatch    int32
		wantInterval int32
		wantSurge    int32
		wantFailed   int32
	}{
		{"1-node", 1, 1, 60, 10, 0},
		{"2-node", 2, 2, 60, 10, 1},
		{"10-nodes", 10, 3, 60, 10, 2},
		{"100-nodes", 100, 7, 60, 10, 4},
		{"500-nodes", 500, 13, 60, 10, 8},
		{"1000-nodes", 1000, 19, 60, 10, 10},
		{"2000-nodes", 2000, 30, 60, 10, 10},
		{"2001-nodes", 2001, 30, 60, 15, 10},
		{"5000-nodes", 5000, 60, 60, 15, 10},
		{"10000-nodes", 10000, 60, 60, 15, 10},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			batch, interval, surge, failed := calculateSmartBatchStrategy(tt.nodeCount)
			if batch != tt.wantBatch || interval != tt.wantInterval ||
				surge != tt.wantSurge || failed != tt.wantFailed {
				t.Errorf("calculateSmartBatchStrategy(%d) = (%d, %d, %d, %d), want (%d, %d, %d, %d)",
					tt.nodeCount, batch, interval, surge, failed,
					tt.wantBatch, tt.wantInterval, tt.wantSurge, tt.wantFailed)
			}
		})
	}
}
