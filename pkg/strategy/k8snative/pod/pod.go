package pod

import (
	"context"
	"encoding/json"
	"fmt"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/types"
	"k8s.io/client-go/kubernetes"
	"k8s.io/klog/v2"

	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/kmetis/starship/pkg/util/kubeclient"
)

const (
	ProviderName = "eks-pod"
)

func init() {
	strategyprovider.RegisterReleaseStrategyFactory(
		ProviderName,
		func(ctx *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
			return NewPodStrategy(ctx)
		},
	)
}

type PodStrategy struct {
	metaKubeclientset kubernetes.Interface
}

// NewPodStrategy 返回一个新的Pod策略实例
func NewPodStrategy(ctx *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
	return &PodStrategy{}, nil
}

func (p *PodStrategy) Validate(ctx context.Context, request *strategyprovider.TaskStrategyRequest) error {
	return util.ValidateTaskStrategyRequest(request)
}

func (p *PodStrategy) Exec(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	if err := p.Validate(ctx, request); err != nil {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,validate failed", request.ChangeId, request.TaskName, request.ClusterId, request.Component)
		return nil, fmt.Errorf("validate failed:%v", err)
	}

	if request.Action != util.TaskActionUpgrade {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,action %s ignore", request.ChangeId, request.TaskName, request.ClusterId, request.Component, request.Action)
		return nil, nil
	}

	client, err := kubeclient.GetTargetK8sClient(request)
	if err != nil {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,failed to get k8s client,err is %v", request.ChangeId, request.TaskName, request.ClusterId, request.Component, err)
		return nil, fmt.Errorf("failed to get k8s client,err is %v", err)
	}

	klog.V(5).Infof("start to update pod,request info is %+v", request)

	var namespace, podName string
	clusterId := request.ClusterId
	component := request.Component
	if request.Extend != nil && request.Extend.Namespace != "" && request.Extend.Name != "" {
		namespace = request.Extend.Namespace
		podName = request.Extend.Name
	} else {
		return nil, fmt.Errorf("namespace or name is empty")
	}

	klog.Infof("start to update pod, changeId:%s, traceId:%s, cluster:%s, component:%s, pod name:%s",
		request.ChangeId, request.TraceId, clusterId, component, podName)

	pod, err := client.CoreV1().Pods(namespace).Get(ctx, podName, metav1.GetOptions{})
	if err != nil {
		klog.Errorf("failed to get pod,cluster %s,component %s,pod name %s, err is %v", clusterId, component, podName, err)
		return nil, err
	}

	if len(request.Extend.Annotations) == 0 {
		return nil, fmt.Errorf("annotations is empty")
	}

	patchAnnotations, err := util.BuildPatchAnnotations(request.Extend.Annotations)
	if err != nil {
		klog.Errorf("failed to build patch annotations,cluster %s,component %s,err is %v", clusterId, component, err)
		return nil, err
	}

	patchData := map[string]interface{}{
		"metadata": map[string]interface{}{
			"uid":         string(pod.UID),
			"annotations": patchAnnotations,
		},
	}
	patchBytes, err := json.Marshal(patchData)
	if err != nil {
		klog.Errorf("failed to marshal patch data,cluster %s,component %s,err is %v", clusterId, component, err)
		return nil, err
	}

	_, err = client.CoreV1().Pods(namespace).Patch(
		ctx,
		podName,
		types.StrategicMergePatchType,
		patchBytes,
		metav1.PatchOptions{},
	)
	if err != nil {
		klog.Errorf("failed to update pod,cluster %s,component %s,err is %v", clusterId, component, err)
		return nil, err
	}
	klog.Infof("finish to update pod,traceId %s,task name %s,cluster %s,component %s,pod name %s",
		request.TraceId, request.TaskName, clusterId, component, podName)

	return nil, nil
}

func (p *PodStrategy) Rollback(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	return nil, fmt.Errorf("not implement")
}
