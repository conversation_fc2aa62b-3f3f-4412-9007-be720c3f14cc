package pod

import (
	"context"
	"k8s.io/client-go/kubernetes/fake"
	"testing"

	"git.woa.com/kmetis/starship/pkg/model"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func TestUpdatePodTemplate(t *testing.T) {
	podName := "test-pod"
	namespace := "test-ns"
	imageTag := "v1.2.3"

	originalPod := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      podName,
			Namespace: namespace,
			Annotations: map[string]string{
				"original": "annotation",
			},
		},
		Spec: corev1.PodSpec{
			Containers: []corev1.Container{
				{
					Name:  "main",
					Image: "nginx:1.0.0",
				},
			},
		},
	}
	request := &strategyprovider.TaskStrategyRequest{
		ClusterId: "test-cluster",
		Component: "test-component",
		ImageTag:  imageTag,
		Extend: &model.ExtendInfo{
			Name:          podName,
			Namespace:     namespace,
			ContainerName: "main",
			Annotations: []map[string]string{
				{"type": "update", "key": "original", "value": "value1"},
			},
		},
	}

	client := fake.NewSimpleClientset()
	createdPod, err := client.CoreV1().Pods(namespace).Create(context.TODO(), originalPod, metav1.CreateOptions{})
	if err != nil {
		t.Fatalf("创建Pod失败: %v", err)
	}

	newPod := createdPod.DeepCopy()
	annotations, err := util.BuildNewAnnotations(newPod.Annotations, request.Extend.Annotations)
	if err != nil {
		t.Errorf("Error building new annotations: %v", err)
	}
	newPod.Annotations = annotations
	updatePod, err := client.CoreV1().Pods(namespace).Update(context.TODO(), newPod, metav1.UpdateOptions{})

	if updatePod.Annotations["original"] != "value1" {
		t.Errorf("annotation not updated, got: %s, want: %s",
			updatePod.Annotations["original"], "value1")
	}
}
