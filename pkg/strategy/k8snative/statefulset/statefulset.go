package statefulset

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"

	appsv1 "k8s.io/api/apps/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/klog/v2"

	"git.woa.com/kmetis/starship/infra"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/kmetis/starship/pkg/util/kubeclient"
)

const (
	ProviderName = "statefulset"
)

func init() {
	strategyprovider.RegisterReleaseStrategyFactory(
		ProviderName,
		func(ctx *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
			return Newstatefulsetstrategy(ctx)
		},
	)
}

type statefulsetstrategy struct {
	metaKubeclientset kubernetes.Interface
}

// Newstatefulsetscaler returns new instance of a statefulsetstrategy
func Newstatefulsetstrategy(ctx *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
	// TODO: 获取当前地域的metacluster id集群列表，并进行初始化
	return &statefulsetstrategy{}, nil
}

func (d *statefulsetstrategy) Validate(ctx context.Context, request *strategyprovider.TaskStrategyRequest) error {
	return util.ValidateTaskStrategyRequest(request)
}

func (d *statefulsetstrategy) Exec(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	if err := d.Validate(ctx, request); err != nil {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,validate failed", request.ChangeId, request.TaskName, request.ClusterId, request.Component)
		return nil, fmt.Errorf("validate failed:%v", err)
	}

	if request.Action != util.TaskActionUpgrade {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,action %s ignore", request.ChangeId, request.TaskName, request.ClusterId, request.Component, request.Action)
		return nil, nil
	}

	client, err := kubeclient.GetTargetK8sClient(request)
	if err != nil {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,failed to get k8s client,err is %v", request.ChangeId, request.TaskName, request.ClusterId, request.Component, err)
		return nil, fmt.Errorf("failed to get k8s client,err is %v", err)
	}

	klog.V(5).Infof("start to update statefulsets,request info is %+v", request)

	clusterId := request.ClusterId
	component := request.Component
	namespace := util.GetComponentNamespace(clusterId, request.Namespace)
	workloadName := util.GetWorkloadName(clusterId, request.WorkloadName)

	klog.Infof("start to update statefulsets,changeid %s,task name %s,cluster %s,component %s,workload name %s", request.ChangeId, request.TaskName, clusterId, component, workloadName)

	sts, err := client.AppsV1().StatefulSets(namespace).Get(ctx, workloadName, metav1.GetOptions{})
	if err != nil {
		klog.Errorf("failed to get statefulsets,cluster %s,component %s,workload name %s, err is %v", clusterId, component, workloadName, err)
		return nil, err
	}

	newSts := sts.DeepCopy()
	if err := util.UpdatePodTemplate(&newSts.Spec.Template, request); err != nil {
		klog.Errorf("failed to update pod template, cluster %s, component %s, workload name %s, err is %v", clusterId, component, workloadName, err)
		return nil, err
	}

	_, err = client.AppsV1().StatefulSets(namespace).Update(ctx, newSts, metav1.UpdateOptions{})
	if err != nil {
		klog.Errorf("failed to update statefulsets,cluster %s,component %s,err is %v", clusterId, component, err)
		return nil, err
	}

	err = infra.CreateRevision(&infra.StarshipRevision{
		TaskId:       request.TaskId,
		SubTaskId:    request.SubTaskId,
		AppName:      request.Component,
		Strategy:     request.Strategy,
		ResourceType: ProviderName,
		ResourceInfo: namespace + "/" + workloadName,
		Revision:     getCRForSts(sts), // 记录最新的controllerrevision用于后续rollback
	})
	if err != nil {
		klog.Warningf("failed to create revision,cluster %s,component %s,workload name %s, err is %v", clusterId, component, workloadName, err)
	}

	klog.Infof("finish to update statefulsets,changeid %s,task name %s,cluster %s,component %s,workload name %s", request.ChangeId, request.TaskName, clusterId, component, workloadName)
	return nil, err
}

func (d *statefulsetstrategy) Rollback(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	if err := d.Validate(ctx, request); err != nil {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,validate failed", request.ChangeId, request.TaskName, request.ClusterId, request.Component)
		return nil, fmt.Errorf("validate failed:%v", err)
	}

	if request.Action != util.TaskActionRollback {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,action %s ignore", request.ChangeId, request.TaskName, request.ClusterId, request.Component, request.Action)
		return nil, nil
	}

	client, err := kubeclient.GetTargetK8sClient(request)
	if err != nil {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,failed to get k8s client,err is %v", request.ChangeId, request.TaskName, request.ClusterId, request.Component, err)
		return nil, fmt.Errorf("failed to get k8s client,err is %v", err)
	}

	klog.V(5).Infof("start to update statefulsets,request info is %+v", request)

	clusterId := request.ClusterId
	component := request.Component
	namespace := util.GetComponentNamespace(clusterId, request.Namespace)
	workloadName := util.GetWorkloadName(clusterId, request.WorkloadName)

	klog.Infof("start to update statefulsets,changeid %s,task name %s,cluster %s,component %s,workload name %s", request.ChangeId, request.TaskName, clusterId, component, workloadName)

	sts, err := client.AppsV1().StatefulSets(namespace).Get(ctx, workloadName, metav1.GetOptions{})
	if err != nil {
		klog.Errorf("failed to get statefulsets,cluster %s,component %s,workload name %s, err is %v", clusterId, component, workloadName, err)
		return nil, err
	}

	if request.Extend == nil || request.Extend.Rollback == nil || request.Extend.Rollback["TaskId"] == "" {
		klog.Errorf("failed to get revision for cluster %s,component %s,workload name %s, err is %v", clusterId, component, workloadName, err)
		return nil, err
	}

	orgTaskId, _ := strconv.ParseInt(request.Extend.Rollback["TaskId"], 10, 64)
	rv, err := infra.GetRevisionByTask(orgTaskId)
	if err != nil {
		klog.Errorf("failed to get revision for task %d,cluster %s,component %s,workload name %s, err is %v", orgTaskId, clusterId, component, workloadName, err)
		return nil, err
	}

	revision, err := client.AppsV1().ControllerRevisions(namespace).Get(context.TODO(), rv.Revision, metav1.GetOptions{})
	if err != nil {
		{
			klog.Errorf("failed to get revision for task %d,cluster %s,component %s,workload name %s, err is %v", orgTaskId, clusterId, component, workloadName, err)
			return nil, err
		}
	}

	var stsHistory appsv1.StatefulSet
	if err := json.Unmarshal(revision.Data.Raw, &stsHistory); err != nil {
		klog.Errorf("failed to unmarshal statefulsets,cluster %s,component %s,workload name %s, err is %v", clusterId, component, workloadName, err)
		return nil, fmt.Errorf("failed to unmarshal statefulsets,cluster %s,component %s,workload name %s, err is %v", clusterId, component, workloadName, err)
	}

	newSts := sts.DeepCopy()
	newSts.Spec.Template = *stsHistory.Spec.Template.DeepCopy()
	_, err = client.AppsV1().StatefulSets(namespace).Update(ctx, newSts, metav1.UpdateOptions{})
	if err != nil {
		klog.Errorf("failed to rollback statefulsets,cluster %s,component %s,err is %v", clusterId, component, err)
		return nil, err
	}
	klog.Infof("finish to rollback statefulsets,changeid %s,task name %s,cluster %s,component %s,workload name %s", request.ChangeId, request.TaskName, clusterId, component, workloadName)
	return nil, err
}

// UpdateRevision 是最新的revsion，CurrentRevision 是当前的revision
// 当sts进行升级，部分pod没有完成升级时，UpdateRevision 会与 CurrentRevision 不一致，但升级完成后，两个会一致
func getCRForSts(sts *appsv1.StatefulSet) string {
	return sts.Status.UpdateRevision
}
