package mastercrd

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"time"

	masterv1alpha1 "cloud.tencent.com/tke/master-operator/pkg/apis/master.cloud.tencent.com/v1alpha1"
	masterclientset "cloud.tencent.com/tke/master-operator/pkg/client/clientset/versioned"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/klog"

	"git.woa.com/kmetis/starship/infra"
	"git.woa.com/kmetis/starship/pkg/config"
	"git.woa.com/kmetis/starship/pkg/masterutil"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/kmetis/starship/pkg/util/kubeclient"
)

const (
	ProviderName = "masterCRD"
)

func init() {
	strategyprovider.RegisterReleaseStrategyFactory(
		ProviderName,
		func(ctx *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
			return NewMasterStrategy(ctx)
		},
	)
}

type MasterStrategy struct {
	metaKubeclientset kubernetes.Interface
	// TODO: 通过Informer优化性能
	// mastersLister masterlisters.MasterLister
	// masterclient  masterclientset.Interface
}

// NewMasterStrategy returns new instance of a masterCRD Strategy
func NewMasterStrategy(ctx *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
	// TODO: 获取当前地域的metacluster id集群列表，并进行初始化
	return &MasterStrategy{}, nil
}

func (d *MasterStrategy) Validate(ctx context.Context, request *strategyprovider.TaskStrategyRequest) error {
	return util.ValidateTaskStrategyRequest(request)
}

func (m *MasterStrategy) Exec(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	if err := m.Validate(ctx, request); err != nil {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,validate failed", request.ChangeId, request.TaskName, request.ClusterId, request.Component)
		return nil, fmt.Errorf("validate failed:%v", err)
	}

	if request.Action != util.TaskActionUpgrade {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,action %s ignore", request.ChangeId, request.TaskName, request.ClusterId, request.Component, request.Action)
		return nil, nil
	}

	masterClient, err := kubeclient.GetTargetMetaClient(request)
	if err != nil {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,failed to get k8s client,err is %v", request.ChangeId, request.TaskName, request.ClusterId, request.Component, err)
		return nil, fmt.Errorf("failed to get k8s client,err is %v", err)
	}

	klog.V(5).Infof("start to update master,request info is %+v", request)

	clusterId := request.ClusterId
	component := request.Component
	imageTag := request.ImageTag
	if err := m.waitForMasterPhaseRunning(masterClient, clusterId); err != nil {
		klog.Errorf("failed to wait for matster phase running,err is %v", err)
		return nil, err
	}
	master, err := masterClient.MasterV1alpha1().Masters(clusterId).Get(ctx, clusterId, metav1.GetOptions{})
	if err != nil {
		klog.Errorf("failed to get master,cluster %s,err is %v", clusterId, err)
		return nil, err
	}

	// 保存当前状态用于回滚
	oldState, err := masterutil.SaveMasterComponentState(master, component)
	if err != nil {
		klog.Errorf("failed to save master component state,cluster %s,component %s,err is %v", clusterId, component, err)
		return nil, err
	}

	// 将状态序列化为JSON字符串
	stateJSON, err := json.Marshal(oldState)
	if err != nil {
		klog.Errorf("failed to marshal master component state,cluster %s,component %s,err is %v", clusterId, component, err)
		return nil, err
	}

	newMaster := master.DeepCopy()

	// 更新镜像标签
	if err = masterutil.SetMasterAdvancedPara(newMaster, component, imageTag, nil, request.Extend); err != nil {
		klog.Errorf("failed to SetMasterAdvancedPara,cluster %s,err is %v", clusterId, err)
		return nil, err
	}

	// 更新启动参数
	if request.Extend != nil && len(request.Extend.Args) > 0 {
		if err = masterutil.UpdateMasterComponentArgs(newMaster, component, request.Extend.Args); err != nil {
			klog.Errorf("failed to update master component args,cluster %s,component %s,err is %v", clusterId, component, err)
			return nil, err
		}
	}

	// 更新环境变量
	if request.Extend != nil && len(request.Extend.Envs) > 0 {
		if err = masterutil.UpdateMasterComponentEnvs(newMaster, component, request.Extend.Envs); err != nil {
			klog.Errorf("failed to update master component envs,cluster %s,component %s,err is %v", clusterId, component, err)
			return nil, err
		}
	}

	_, err = masterClient.MasterV1alpha1().Masters(clusterId).Update(ctx, newMaster, metav1.UpdateOptions{})
	if err != nil {
		klog.Errorf("cluster %s,component %s failed to update master, err is %v", clusterId, component, err)
		return nil, err
	}

	// 保存完整状态到revision
	err = infra.CreateRevision(&infra.StarshipRevision{
		TaskId:       request.TaskId,
		SubTaskId:    request.SubTaskId,
		AppName:      request.Component,
		Strategy:     request.Strategy,
		ResourceType: ProviderName,
		ResourceInfo: component,
		Revision:     string(stateJSON),
	})
	if err != nil {
		klog.Warningf("failed to create revision,cluster %s,component %s,err is %v", clusterId, component, err)
	}
	return nil, err
}

func (m *MasterStrategy) delFinalizers(masterClient masterclientset.Interface, clusterId string) error {
	num := 0
	for {
		num++
		if num > config.DefaultMaxRetryNum {
			break
		}
		master, err := masterClient.MasterV1alpha1().Masters(clusterId).Get(context.TODO(), clusterId, metav1.GetOptions{})
		if err != nil {
			klog.Errorf("Failed to get master %s in cluster,err:%s", clusterId, err)
			time.Sleep(time.Second)
			continue
		}
		newMaster := master.DeepCopy()
		if len(newMaster.Status.PhaseFinalizers) == 0 && newMaster.Status.Phase == masterv1alpha1.PhaseMasterRunning {
			break
		}
		if time.Since(newMaster.GetCreationTimestamp().Time) < time.Second*config.DefaultClusterCreationSecond {
			break
		}
		if len(newMaster.Status.PhaseFinalizers) > 0 {
			newMaster.Status.Phase = masterv1alpha1.PhaseMasterRunning
			newMaster.Status.PhaseFinalizers = []string{}
		} else {
			newMaster.Status.Phase = masterv1alpha1.PhaseMasterRunning
		}
		_, err = masterClient.MasterV1alpha1().Masters(clusterId).Update(context.TODO(), newMaster, metav1.UpdateOptions{})
		if err != nil {
			klog.Errorf("Failed to update master %s in cluster,err:%s", clusterId, err)
		}
	}
	return nil
}

func (m *MasterStrategy) waitForMasterPhaseRunning(masterClient masterclientset.Interface, clusterId string) error {
	// master operator specical logic....
	err := m.delFinalizers(masterClient, clusterId)
	if err != nil {
		klog.Errorf("Failed to delFinalizers %s in cluster,err:%s", clusterId, err)
		return err
	}
	num := 0
	for {
		num++
		master, err := masterClient.MasterV1alpha1().Masters(clusterId).Get(context.TODO(), clusterId, metav1.GetOptions{})
		if err != nil {
			klog.Errorf("Failed to get master %s in cluster,err:%s", clusterId, err)
			return err
		}
		klog.V(5).Infof("wait for master phase running,clusterid:%s", clusterId)
		if master.Status.Phase == "running" || num > config.DefaultMaxRetryNum {
			break
		}
		time.Sleep(time.Second)
	}
	return nil
}

// 支持master的参数rollback，包括镜像标签、启动参数和环境变量
func (m *MasterStrategy) Rollback(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	if err := m.Validate(ctx, request); err != nil {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,validate failed", request.ChangeId, request.TaskName, request.ClusterId, request.Component)
		return nil, fmt.Errorf("validate failed:%v", err)
	}

	if request.Action != util.TaskActionRollback {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,action %s ignore", request.ChangeId, request.TaskName, request.ClusterId, request.Component, request.Action)
		return nil, nil
	}

	masterClient, err := kubeclient.GetTargetMetaClient(request)
	if err != nil {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,failed to get k8s client,err is %v", request.ChangeId, request.TaskName, request.ClusterId, request.Component, err)
		return nil, fmt.Errorf("failed to get k8s client,err is %v", err)
	}

	klog.V(5).Infof("start to rollback master,request info is %+v", request)

	orgTaskId, _ := strconv.ParseInt(request.Extend.Rollback["TaskId"], 10, 64)
	rv, err := infra.GetRevisionByTask(orgTaskId)
	if err != nil {
		klog.Errorf("failed to get revision for task %d,cluster %s,component %s,err is %v",
			orgTaskId, request.ClusterId, request.Component, err)
		return nil, err
	}

	clusterId := request.ClusterId
	component := request.Component

	// 尝试解析保存的完整状态
	var oldState *masterutil.MasterComponentState
	if err := json.Unmarshal([]byte(rv.Revision), &oldState); err != nil {
		// 如果解析失败，说明是旧版本只保存了镜像标签，使用兼容模式
		klog.Warningf("failed to unmarshal master component state, using legacy mode, cluster %s, component %s, err is %v", clusterId, component, err)
		oldState = &masterutil.MasterComponentState{
			ImageTag: rv.Revision,
		}
	}

	if err := m.waitForMasterPhaseRunning(masterClient, clusterId); err != nil {
		klog.Errorf("failed to wait for matster phase running,err is %v", err)
		return nil, err
	}
	master, err := masterClient.MasterV1alpha1().Masters(clusterId).Get(ctx, clusterId, metav1.GetOptions{})
	if err != nil {
		klog.Errorf("failed to get master,cluster %s,err is %v", clusterId, err)
		return nil, err
	}
	newMaster := master.DeepCopy()

	// 恢复完整状态
	if err = masterutil.RestoreMasterComponentState(newMaster, component, oldState); err != nil {
		klog.Errorf("failed to restore master component state,cluster %s,component %s,err is %v", clusterId, component, err)
		return nil, err
	}

	_, err = masterClient.MasterV1alpha1().Masters(clusterId).Update(ctx, newMaster, metav1.UpdateOptions{})
	if err != nil {
		klog.Errorf("cluster %s,component %s failed to rollback master, err is %v", clusterId, component, err)
		return nil, err
	}

	klog.Infof("finish to rollback master,traceId %s,task name %s,cluster %s,component %s", request.TraceId, request.TaskName, clusterId, component)
	return nil, err
}
