package eklet

import (
	"context"
	"fmt"
	"strconv"

	appsv1 "k8s.io/api/apps/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/kubernetes"
	"k8s.io/klog/v2"
	"sigs.k8s.io/yaml"

	"git.woa.com/kmetis/starship/infra"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	rainutil "git.woa.com/kmetis/starship/pkg/task/util"
	"git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/kmetis/starship/pkg/util/kubeclient"
)

const (
	ProviderName = "ekseklet"
)

func init() {
	strategyprovider.RegisterReleaseStrategyFactory(
		ProviderName,
		func(ctx *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
			return NewDeploymentStrategy(ctx)
		},
	)
}

type DeploymentStrategy struct {
	metaKubeclientset kubernetes.Interface
}

// NewDeploymentScaler returns new instance of a deploymentStrategy
func NewDeploymentStrategy(ctx *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
	// TODO: 获取当前地域的metacluster id集群列表，并进行初始化
	return &DeploymentStrategy{}, nil
}

func (d *DeploymentStrategy) Validate(ctx context.Context, request *strategyprovider.TaskStrategyRequest) error {
	return util.ValidateTaskStrategyRequest(request)
}

func (d *DeploymentStrategy) Exec(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	if err := d.Validate(ctx, request); err != nil {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,validate failed", request.ChangeId, request.TaskName, request.ClusterId, request.Component)
		return nil, fmt.Errorf("validate failed:%v", err)
	}

	if request.Action != util.TaskActionUpgrade {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,action %s ignore", request.ChangeId, request.TaskName, request.ClusterId, request.Component, request.Action)
		return nil, nil
	}

	client, err := kubeclient.GetTargetK8sClient(request)
	if err != nil {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,failed to get k8s client,err is %v", request.ChangeId, request.TaskName, request.ClusterId, request.Component, err)
		return nil, fmt.Errorf("failed to get k8s client,err is %v", err)
	}

	klog.V(5).Infof("start to update deployments,request info is %+v", request)

	clusterId := request.ClusterId
	component := request.Component
	namespace := util.GetComponentNamespace(clusterId, request.Namespace)
	workloadName := util.GetWorkloadName(clusterId, request.WorkloadName)

	// 解析schedule配置的enable开关
	var scheduleConfig string
	var schedulerEnable bool
	if request.Extend != nil && request.Extend.Scheduler {
		componentsConfigContent := rainutil.GetRainbowData(fmt.Sprintf("components/%s/config", component))
		componentsConfig := make(map[string]interface{})
		if err := yaml.Unmarshal([]byte(componentsConfigContent), &componentsConfig); err != nil {
			klog.Errorf("unmarshal components config from %s failed: %v", componentsConfigContent, err)
			return nil, err
		}
		scheduleData, err := rainutil.ConvertToMap(componentsConfig, "scheduler")
		if err != nil {
			klog.Errorf("convert components config to map failed: %v", err)
			return nil, err
		}
		scheduleConfig = scheduleData[request.ProductName]
		schedulerEnable = true
	}

	if schedulerEnable {
		err := util.UpgradeSvc(client, request.ProductName, clusterId)
		if err != nil {
			klog.Errorf("failed to update svc,cluster %s,component %s,err is %v", clusterId, component, err)
			return nil, err
		}
	}

	klog.Infof("start to update deployments, changeId:%s, traceId:%s, cluster:%s, component:%s, workload name:%s", request.ChangeId, request.TraceId, clusterId, component, workloadName)

	dp, err := client.AppsV1().Deployments(namespace).Get(ctx, workloadName, metav1.GetOptions{})
	if err != nil {
		klog.Errorf("failed to get deployments,cluster %s,component %s,workload name %s, err is %v", clusterId, component, workloadName, err)
		return nil, err
	}

	newdp := dp.DeepCopy()
	if err := util.UpdatePodTemplate(&newdp.Spec.Template, request); err != nil {
		klog.Errorf("failed to update pod template, cluster %s, component %s, workload name %s, err is %v", clusterId, component, workloadName, err)
		return nil, err
	}

	_, err = client.AppsV1().Deployments(namespace).Update(ctx, newdp, metav1.UpdateOptions{})
	if err != nil {
		klog.Errorf("failed to update deployments,cluster %s,component %s,err is %v", clusterId, component, err)
		return nil, err
	}

	err = infra.CreateRevision(&infra.StarshipRevision{
		TaskId:       request.TaskId,
		SubTaskId:    request.SubTaskId,
		AppName:      request.Component,
		Strategy:     request.Strategy,
		ResourceType: ProviderName,
		ResourceInfo: namespace + "/" + workloadName,
		Revision:     getRevision(dp),
	})
	if err != nil {
		klog.Warningf("failed to create revision,cluster %s,component %s,workload name %s, err is %v", clusterId, component, workloadName, err)
	}

	klog.Infof("finish to update deployments,traceId %s,task name %s,cluster %s,component %s,workload name %s", request.TraceId, request.TaskName, clusterId, component, workloadName)

	if schedulerEnable {
		err = util.UpgradeScheduler(client, request.ProductName, clusterId, scheduleConfig)
		if err != nil {
			klog.Errorf("failed to update scheduler,cluster %s,component %s,err is %v", clusterId, component, err)
			return nil, err
		}
	}

	return nil, err
}

func (d *DeploymentStrategy) Rollback(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	if err := d.Validate(ctx, request); err != nil {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,validate failed", request.ChangeId, request.TaskName, request.ClusterId, request.Component)
		return nil, fmt.Errorf("validate failed:%v", err)
	}

	if request.Action != util.TaskActionRollback {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,action %s ignore", request.ChangeId, request.TaskName, request.ClusterId, request.Component, request.Action)
		return nil, nil
	}

	client, err := kubeclient.GetTargetK8sClient(request)
	if err != nil {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,failed to get k8s client,err is %v", request.ChangeId, request.TaskName, request.ClusterId, request.Component, err)
		return nil, fmt.Errorf("failed to get k8s client,err is %v", err)
	}

	klog.V(5).Infof("start to update deployments,request info is %+v", request)

	clusterId := request.ClusterId
	component := request.Component
	namespace := util.GetComponentNamespace(clusterId, request.Namespace)
	workloadName := util.GetWorkloadName(clusterId, request.WorkloadName)

	klog.Infof("start to update deployments, changeId:%s, traceId:%s, cluster:%s, component:%s, workload name:%s", request.ChangeId, request.TraceId, clusterId, component, workloadName)

	dp, err := client.AppsV1().Deployments(namespace).Get(ctx, workloadName, metav1.GetOptions{})
	if err != nil {
		klog.Errorf("failed to get deployments,cluster %s,component %s,workload name %s, err is %v", clusterId, component, workloadName, err)
		return nil, err
	}

	rsList, err := client.AppsV1().ReplicaSets(namespace).List(ctx, metav1.ListOptions{
		ResourceVersion: "0",
		LabelSelector:   labels.Set(dp.Spec.Selector.MatchLabels).String(),
	})
	if err != nil {
		klog.Errorf("failed to list rs,cluster %s,component %s,workload name %s, err is %v", clusterId, component, workloadName, err)
		return nil, err
	}

	if request.Extend == nil || request.Extend.Rollback == nil || request.Extend.Rollback["TaskId"] == "" {
		klog.Errorf("failed to get revision for cluster %s,component %s,workload name %s, err is %v", clusterId, component, workloadName, err)
		return nil, err
	}

	orgTaskId, _ := strconv.ParseInt(request.Extend.Rollback["TaskId"], 10, 64)
	rv, err := infra.GetRevisionByTask(orgTaskId)
	if err != nil {
		klog.Errorf("failed to get revision for task %d,cluster %s,component %s,workload name %s, err is %v", orgTaskId, clusterId, component, workloadName, err)
		return nil, err
	}

	var targetRS *appsv1.ReplicaSet
	for _, rs := range rsList.Items {
		if getRevision(&rs) == rv.Revision {
			targetRS = &rs
			break
		}
	}

	if targetRS == nil {
		klog.Errorf("failed to get rs for task %d,cluster %s,component %s,workload name %s", orgTaskId, clusterId, component, workloadName)
		return nil, fmt.Errorf("found rs %s failed: cluster %s,component %s,workload name %s", rv.Revision, clusterId, component, workloadName)
	}

	dpCopy := dp.DeepCopy()
	dpCopy.Spec.Template = targetRS.Spec.Template
	_, err = client.AppsV1().Deployments(namespace).Update(ctx, dpCopy, metav1.UpdateOptions{})
	if err != nil {
		klog.Errorf("failed to rollback deployments,cluster %s,component %s,err is %v", clusterId, component, err)
		return nil, err
	}
	klog.Infof("finish to rollback deployments,traceId %s,task name %s,cluster %s,component %s,workload name %s", request.TraceId, request.TaskName, clusterId, component, workloadName)

	return nil, err
}

func getRevision(obj metav1.Object) string {
	return obj.GetAnnotations()["deployment.kubernetes.io/revision"]
}
