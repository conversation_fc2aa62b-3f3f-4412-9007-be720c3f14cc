package plugin

import (
	"bytes"
	"context"
	"fmt"
	"text/template"

	"k8s.io/klog/v2"

	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/task/util"
)

const (
	ProviderName = "plugin"
)

func init() {
	strategyprovider.RegisterReleaseStrategyFactory(
		ProviderName,
		func(ctx *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
			return NewPluginStrategy(ctx)
		},
	)
}

type PluginStrategy struct {
	cfg *strategyprovider.ReleaseConfig
}

func NewPluginStrategy(ctx *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
	return &PluginStrategy{cfg: ctx}, nil
}

func (d *PluginStrategy) Exec(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	version := request.PluginVersion
	templateName := fmt.Sprintf("components/%s/plugin/templates/%s", d.cfg.Component, version)
	tpl := util.GetRainbowData(templateName)
	jobTemplate, err := template.New(templateName).Parse(tpl)
	if err != nil {
		return &strategyprovider.TaskStrategyReply{}, err
	}

	job := new(bytes.Buffer)
	if err = jobTemplate.Execute(job, d.cfg); err != nil {
		return &strategyprovider.TaskStrategyReply{}, err
	}

	fmt.Println("job content:", job.String())
	if exceed, err := util.IsJobExceedLimit(); err != nil || exceed {
		klog.Errorf("job %s/%s limit check faield: %v %v", request.ChangeId, request.TaskName, exceed, err)
		return &strategyprovider.TaskStrategyReply{}, fmt.Errorf("job limit check failed")
	}
	err = util.CreateOrUpdateResource(ctx, job.Bytes())
	if err != nil {
		klog.Errorf("create job failed, job: %v, error: %v", job.String(), err)
		return &strategyprovider.TaskStrategyReply{}, err
	}

	return nil, err
}

func (d *PluginStrategy) Rollback(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	return nil, fmt.Errorf("not implement")
}
