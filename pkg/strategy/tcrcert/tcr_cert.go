package tcrcert

import (
	"bytes"
	"context"
	"fmt"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/client-go/kubernetes"
	"k8s.io/klog"

	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	"git.woa.com/kmetis/starship/pkg/util/kubeclient"
)

const (
	ProviderName = "tcrCert"
)

func init() {
	strategyprovider.RegisterReleaseStrategyFactory(
		ProviderName,
		func(cfg *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
			return NewTcrCertStrategy(cfg)
		},
	)
}

type TcrCertStrategy struct {
}

func NewTcrCertStrategy(ctx *strategyprovider.ReleaseConfig) (strategyprovider.Strategy, error) {
	return &TcrCertStrategy{}, nil
}

func (t *TcrCertStrategy) Validate(ctx context.Context, request *strategyprovider.TaskStrategyRequest) error {
	if request.Extend == nil || request.Extend.Namespace == "" || request.TcrCertCrt == "" || request.TcrCertKey == "" ||
		request.TcrPrecheckExpiredTime == "" || request.TcrPostcheckExpiredTime == "" {
		klog.Errorf("changeid %s,task name %s, cluster %s,component %s,extendInfo is empty or invalid, %s",
			request.ChangeId, request.TaskName, request.ClusterId, request.Component, request.TraceId)
		return fmt.Errorf("extendInfo is empty or invalid")
	}
	// 不进行通用检查
	// return util.ValidateTaskStrategyRequest(request)
	return nil
}

func (t *TcrCertStrategy) Exec(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	klog.V(5).Infof("start to update tcr cert,request info is %+v", request)

	client, err := kubeclient.GetK8sClient(request.ClusterId, request.IanvsToken, request.User, request.ProductName)
	if err != nil {
		return nil, err
	}

	crtBytes, err := util.Base64Decode([]byte(request.TcrCertCrt))
	if err != nil {
		klog.Errorf("decode cert crt failed:%v, %s", err, request.TraceId)
		return nil, err
	}
	keyBytes, err := util.Base64Decode([]byte(request.TcrCertKey))
	if err != nil {
		klog.Errorf("decode cert key failed:%v, %s", err, request.TraceId)
		return nil, err
	}

	// 1.更新secret中的证书
	if err := updateTLSSecret(client, request.Extend.Namespace, "tcr-nginx-cert", crtBytes, keyBytes); err != nil {
		klog.Errorf("updateTLSSecret failed:%v, %s", err, request.TraceId)
		return nil, err
	}

	// 2.重启Pod加载新证书
	if err := restartDeployPod(client, request.Extend.Namespace, request.TraceId); err != nil {
		klog.Errorf("restartDeployPod failed:%v, %s", err, request.TraceId)
		return nil, err
	}

	return nil, nil
}

func (t *TcrCertStrategy) Rollback(ctx context.Context, request *strategyprovider.TaskStrategyRequest) (*strategyprovider.TaskStrategyReply, error) {
	return nil, fmt.Errorf("not implement")
}

func updateTLSSecret(client kubernetes.Interface, namespace, secretName string, newCrt, newKey []byte) error {
	secret, err := client.CoreV1().Secrets(namespace).Get(context.TODO(), secretName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("failed to get secret: %v", err)
	}
	if secret.Data == nil {
		return fmt.Errorf("secret %s/%s has no data", namespace, secretName)
	}

	if bytes.Equal(newCrt, secret.Data["tls.crt"]) && bytes.Equal(newKey, secret.Data["tls.key"]) {
		klog.Infof("tls.crt and tls.key are not changed, skip update: %s/%s", namespace, secretName)
		return nil
	}

	newSecret := secret.DeepCopy()
	newSecret.Data["tls.crt"] = newCrt
	newSecret.Data["tls.key"] = newKey
	_, err = client.CoreV1().Secrets(namespace).Update(context.TODO(), newSecret, metav1.UpdateOptions{})
	return err
}

// restart tcr-nginx-internal and tcr-nginx-vpc-xxx
// 因为 tcr-nginx-vpc-xxx 中是vpc-id，所以使用了list进行deploy查找
func restartDeployPod(client kubernetes.Interface, namespace, traceId string) error {
	updated, err := restartDeployV1Pod(client, namespace, traceId)
	if err != nil {
		return err
	}
	if updated {
		return nil
	}

	updated, err = restartDeployV1beta1Pod(client, namespace, traceId)
	if err != nil {
		return err
	}
	if !updated {
		return fmt.Errorf("restartDeployPod failed: %s", traceId)
	}

	return nil
}

func restartDeployV1Pod(client kubernetes.Interface, ns, traceId string) (bool, error) {
	labelSet := map[string]string{
		"component": "nginx",
		"release":   "tcr",
	}
	selectorStr := labels.SelectorFromSet(labelSet).String()
	dps, err := client.AppsV1().Deployments(ns).List(context.TODO(), metav1.ListOptions{
		ResourceVersion: "0",
		LabelSelector:   selectorStr,
	})
	if err != nil {
		return false, err
	}

	updated := false
	for _, dp := range dps.Items {
		dpCopy := dp.DeepCopy()
		if dpCopy.Spec.Template.Annotations == nil {
			dpCopy.Spec.Template.Annotations = make(map[string]string)
		}
		dpCopy.Spec.Template.Annotations["tke.cloud.tencent.com/tcr-cert-restartedAt"] = time.Now().Format(time.RFC3339)
		_, err = client.AppsV1().Deployments(ns).Update(context.TODO(), dpCopy, metav1.UpdateOptions{})
		if err != nil {
			return false, fmt.Errorf("Update deploy %s/%s failed: %v", ns, dp.Name, err)
		}
		updated = true
		klog.Infof("restart deployV1 %s/%s for %s", ns, dp.Name, traceId)
	}
	return updated, nil
}

func restartDeployV1beta1Pod(client kubernetes.Interface, ns, traceId string) (bool, error) {
	labelSet := map[string]string{
		"component": "nginx",
		"release":   "tcr",
	}
	selectorStr := labels.SelectorFromSet(labelSet).String()
	dps, err := client.AppsV1beta1().Deployments(ns).List(context.TODO(), metav1.ListOptions{
		ResourceVersion: "0",
		LabelSelector:   selectorStr,
	})
	if err != nil {
		return false, err
	}

	updated := false
	for _, dp := range dps.Items {
		dpCopy := dp.DeepCopy()
		if dpCopy.Spec.Template.Annotations == nil {
			dpCopy.Spec.Template.Annotations = make(map[string]string)
		}
		dpCopy.Spec.Template.Annotations["tke.cloud.tencent.com/tcr-cert-restartedAt"] = time.Now().Format(time.RFC3339)
		_, err = client.AppsV1beta1().Deployments(ns).Update(context.TODO(), dpCopy, metav1.UpdateOptions{})
		if err != nil {
			return false, fmt.Errorf("Update deploy %s/%s failed: %v", ns, dp.Name, err)
		}
		updated = true
		klog.Infof("restart deployV1beta1 %s/%s for %s", ns, dp.Name, traceId)
	}
	return updated, nil
}
