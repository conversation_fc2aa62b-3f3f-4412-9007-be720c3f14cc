package strategyprovider

import (
	"fmt"
	"sync"

	"k8s.io/klog/v2"
)

var (
	mutex                    sync.Mutex
	releaseStrategyProviders = make(map[string]ReleaseFactory)
)

type ReleaseConfig struct {
	Region        string
	ClusterId     string
	ProductName   string
	Strategy      string
	Component     string
	PluginVersion string
	ChangeId      string
	TaskId        int64
	SubTaskId     int64
	IanvsToken    string
	User          string
	ImageTag      string
	Action        string // precheck/postcheck/update
	Timeout       int32
}

type ReleaseFactory func(cfg *ReleaseConfig) (Strategy, error)

// RegisterReleaseStrategyFactory registers the specified release strategy provider
func RegisterReleaseStrategyFactory(name string, factory ReleaseFactory) {
	mutex.Lock()
	defer mutex.Unlock()

	if _, found := releaseStrategyProviders[name]; found {
		klog.V(5).Infof("release strategy provider:%s was registered twice", name)
	}

	klog.V(5).Infof("release strategy provider:%s", name)
	releaseStrategyProviders[name] = factory
}

// GetReleaseStrategyProvider gets the specified release Strategy provider
func GetReleaseStrategyProvider(name string, ctx *ReleaseConfig) (Strategy, error) {
	mutex.Lock()
	defer mutex.Unlock()
	f, found := releaseStrategyProviders[name]

	klog.V(5).Infof("get provider name %s,status:%t", name, found)
	if !found {
		return nil, fmt.Errorf("fatal error,release strategy provider not found, name:%s", name)
	}
	return f(ctx)
}

// ListReleaseStrategyProvider lists all Release Strategy provider
func ListReleaseStrategyProvider() []string {
	var healths []string
	mutex.Lock()
	defer mutex.Unlock()
	for Strategy := range releaseStrategyProviders {
		healths = append(healths, Strategy)
	}
	return healths
}
