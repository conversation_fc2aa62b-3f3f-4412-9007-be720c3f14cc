package strategyprovider

import (
	"context"

	"git.woa.com/kmetis/starship/pkg/model"
)

type TaskStrategyRequest struct {
	// Region表示用户集群所属的Region
	Region string
	// ClusterId表示用户集群ID
	ClusterId string
	// MetaClusterId表示用户集群所属的Meta集群ID
	MetaClusterId string
	// Compoent表示用户组件名称
	Component string
	// ImageTag 表示待更新的目标镜像Tag
	ImageTag string
	// 预检执行策略
	PreCheckStrategy string
	// 后检执行策略
	PostCheckStrategy string
	// Strategy 表示当前执行的部署策略
	Strategy string
	// PluginVersion 表示当前插件版本
	PluginVersion string
	// IanvsToken 表示当前用户的IanvsToken
	IanvsToken string
	// User 表示变更发起的用户名
	User string
	// Action表示部署位置，当前值支持：user,meta，通过七彩石配置
	Location string
	// WorkloadName 表示当前变更的Workload名称, 通过七彩石配置，支持按产品级配置，并支持集群ID通配符
	WorkloadName string
	// WorkloadType 类型，支持deployment、statefulset、ds, 通过七彩石配置
	WorkloadType string
	// healthcheck rules, 在七彩石配置的健康规则。在healthchecks.rules下面。
	HealthCheckRules []model.HealthCheckRule
	// Namespace 表示当前变更的Workload所属的Namespace, 通过七彩石配置，支持按产品级配置，并支持集群ID通配符
	Namespace string
	// CampApiEndPoint 如果策略是appFabricApplication，在config配置中需要制定apiEndpoint
	ApiEndpoint string
	// grpcServer 如果是grpc策略，在config配置中需要指定grpcServer地址：ip:port
	GrpcServer string
	// GrpcReportModel 结果推送模式，push（starship无需轮询，等tp主动推送结果）、pull（tp不会推送结果，starship需要进行轮询结果）
	GrpcReportModel string
	// Action表示当前插件执行的Action,详细参考util文件定义,当前支持如下四个：
	// TaskActionPreCheck：预检
	// TaskActionUpdate：更新
	// TaskActionPostCheck：后检
	// TaskActionRollback：回滚
	Action string
	// Timeout 表示当前变更策略执行的超时时间，单位秒
	Timeout int32
	// ChangeId 表示当前变更的唯一ID，用于记录变更历史
	ChangeId string
	// TaskName 表示当前变更的任务名称，用于记录变更历史
	TaskName string
	// TaskId 子任务ID，用于记录变更历史
	TaskId int64
	// SubTaskId 子任务ID，用于记录变更历史
	SubTaskId int64
	// tcrCert's crt
	TcrCertCrt string
	// tcrCert's key
	TcrCertKey string
	// tcrCert's precheckExpiredTime
	TcrPrecheckExpiredTime string
	// tcrCert's postcheckExpiredTime
	TcrPostcheckExpiredTime string

	// Trace-ID
	TraceId string

	// ProductName，表示是tke还是eks
	ProductName string
	// Extender参数
	Extend *model.ExtendInfo

	// 是否是rollback发布
	IsRollback bool

	// EkletAgentServer
	EkletAgentServer string
}

func (t *TaskStrategyRequest) GetHealthCheckRule(provider string) *model.HealthCheckRule {
	for _, rule := range t.HealthCheckRules {
		if rule.Name == provider {
			return &rule
		}
	}
	return nil
}

type TaskStrategyReply struct {
	HealthCheckName string `json:"HealthCheckName"` // 健康检查项名称
	RiskName        string `json:"Name"`            // 隐患名称
	Level           string `json:"Level"`           // 隐患级别

	Code     string `json:"Code"`     // 隐患错误码，可选值：PASS、FAIL、ERROR。检查通过上报为PASS，检查不通过上报为FAIL，检查异常（构造client失败，获取Deployment等信息失败，rpc调用失败等）上报为ERROR
	Detail   string `json:"Detail"`   // 隐患详情，检查不通过时，提示不通过原因，检查失败时，上报错误信息
	Solution string `json:"Solution"` // 解决方案，检查不通过时，提示对应的解决方案
}

type Strategy interface {
	Exec(ctx context.Context, request *TaskStrategyRequest) (*TaskStrategyReply, error)
	Rollback(ctx context.Context, request *TaskStrategyRequest) (*TaskStrategyReply, error)
}
