package strategyregister

import (
	// register k8s pod release strategy
	_ "git.woa.com/kmetis/starship/pkg/strategy/k8snative/pod"
	// register k8s deployment release strategy
	_ "git.woa.com/kmetis/starship/pkg/strategy/k8snative/deployment"
	// register k8s statefulset release strategy
	_ "git.woa.com/kmetis/starship/pkg/strategy/k8snative/statefulset"
	// register k8s daemonset release strategy
	_ "git.woa.com/kmetis/starship/pkg/strategy/k8snative/daemonset"
	// register k8s configmap release strategy
	_ "git.woa.com/kmetis/starship/pkg/strategy/k8snative/configmap"
	// register tke masterCRD release strategy
	_ "git.woa.com/kmetis/starship/pkg/strategy/mastercrd"
	// register plugin release strategy
	_ "git.woa.com/kmetis/starship/pkg/strategy/plugin"
	// register generic precheck release strategy
	_ "git.woa.com/kmetis/starship/pkg/genericstrategy/precheck"
	// register generic postcheck release strategy
	_ "git.woa.com/kmetis/starship/pkg/genericstrategy/postcheck"
	// register camp appFabricApplication release strategy
	_ "git.woa.com/kmetis/starship/pkg/strategy/appfabricapplication"
	// register grpc release strategy
	_ "git.woa.com/kmetis/starship/pkg/strategy/grpc"
	// register eklet release strategy
	_ "git.woa.com/kmetis/starship/pkg/strategy/mixresource/eklet"
	// register tcrCert release strategy
	_ "git.woa.com/kmetis/starship/pkg/strategy/tcrcert"
	// register clusterscaler release strategy
	_ "git.woa.com/kmetis/starship/pkg/strategy/clusterscaler"
	// register tke eklet agent release strategy
	_ "git.woa.com/kmetis/starship/pkg/strategy/ekletagent"
	// register addon strategy
	_ "git.woa.com/kmetis/starship/pkg/strategy/addon"
)
