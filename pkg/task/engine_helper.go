package task

import (
	"encoding/base64"
	"fmt"
	"strings"
	"time"

	"k8s.io/apimachinery/pkg/util/json"
	"k8s.io/klog/v2"
	"sigs.k8s.io/yaml"

	"git.woa.com/kmetis/starship/infra"
	healthpb "git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/model"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/task/util"
	pkgutil "git.woa.com/kmetis/starship/pkg/util"
)

type TaskStatus struct {
	Phase  string
	Status string
	Reason string
}

type HealthCheckResult struct {
	ComponentHealthyReply *healthpb.ComponentHealthyReply
	TaskStrategyReply     *strategyprovider.TaskStrategyReply
}

// task执行结果上报信息构建
func setTaskStatus(phase string, status string, reason string) *TaskStatus {
	taskStatus := &TaskStatus{}
	taskStatus.Phase = phase
	taskStatus.Status = status
	taskStatus.Reason = reason

	return taskStatus
}

// 构建调用执行任务的参数
func buildCommonParam(parentTask *infra.StarshipTask) (*strategyprovider.ReleaseConfig, *strategyprovider.TaskStrategyRequest, error) {
	if parentTask == nil {
		return nil, nil, fmt.Errorf("parent task is nil")
	}
	// NOTICE: TOKEN是base64编码过，需解码后再使用
	tokenBytes, err := base64.StdEncoding.DecodeString(parentTask.Token)
	if err != nil {
		klog.Errorf("base64 decode token failed: %v, token: %s", err, parentTask.Token)
		return nil, nil, err
	}

	componentsConfigContent := util.GetRainbowData(fmt.Sprintf("components/%s/config", parentTask.AppName))
	componentsConfig := make(map[string]interface{})
	if err := yaml.Unmarshal([]byte(componentsConfigContent), &componentsConfig); err != nil {
		klog.Errorf("unmarshal components config from %s failed: %v", componentsConfigContent, err)
		return nil, nil, err
	}

	// get workload name
	var workloadName string
	if parentTask.Strategy != pkgutil.ReleaseStrategyAppFabric {
		componentsConfigMap, err := util.ConvertToMap(componentsConfig, "workloadName")
		if err != nil {
			klog.Errorf("convert components config to map failed: %v", err)
			return nil, nil, err
		}
		workloadName = componentsConfigMap[parentTask.ProductName]
	}

	precheckStrategy, postcheckStrategy, err := decideStrategy(parentTask)
	if err != nil {
		klog.Errorf("decide strategy failed: %v", err)
		return nil, nil, err
	}

	var extendinfo *model.ExtendInfo
	if parentTask.ExtendInfo != "" {
		if err = json.Unmarshal([]byte(parentTask.ExtendInfo), &extendinfo); err != nil {
			klog.Errorf("unmarshal extend info failed: %v", err)
			return nil, nil, err
		}
	}

	ctx := &strategyprovider.ReleaseConfig{
		Region:        parentTask.Region,
		ClusterId:     parentTask.ClusterId,
		ProductName:   parentTask.ProductName,
		User:          parentTask.User,
		IanvsToken:    string(tokenBytes),
		ImageTag:      parentTask.ImageTag,
		PluginVersion: parentTask.PluginVersion,
		ChangeId:      parentTask.ChangeId,
		Strategy:      parentTask.Strategy,
		Component:     parentTask.AppName,
		Timeout:       int32(parentTask.Timeout),
		TaskId:        parentTask.ID,
	}

	req := &strategyprovider.TaskStrategyRequest{
		Region:                  parentTask.Region,
		ClusterId:               parentTask.ClusterId,
		MetaClusterId:           parentTask.MetaClusterId,
		Component:               parentTask.AppName,
		ImageTag:                parentTask.ImageTag,
		PreCheckStrategy:        precheckStrategy,
		Strategy:                parentTask.Strategy,
		PostCheckStrategy:       postcheckStrategy,
		PluginVersion:           parentTask.PluginVersion,
		IanvsToken:              string(tokenBytes),
		User:                    parentTask.User,
		Location:                util.GetComponentDataStr(componentsConfig, "location"),
		WorkloadName:            workloadName,
		Namespace:               util.GetComponentDataStr(componentsConfig, "namespace"),
		ApiEndpoint:             util.GetComponentDataStr(componentsConfig, "apiEndpoint"),
		GrpcServer:              util.GetComponentDataStr(componentsConfig, "grpcServer"),
		GrpcReportModel:         util.GetComponentDataStr(componentsConfig, "grpcReportModel"),
		TcrCertCrt:              util.GetComponentDataStr(componentsConfig, "basicCert.crt"),
		TcrCertKey:              util.GetComponentDataStr(componentsConfig, "basicCert.key"),
		TcrPrecheckExpiredTime:  util.GetComponentDataStr(componentsConfig, "precheckCertExpiredTime"),
		TcrPostcheckExpiredTime: util.GetComponentDataStr(componentsConfig, "postcheckCertExpiredTime"),
		EkletAgentServer:        util.GetComponentDataStr(componentsConfig, "EkletAgentServer"),
		Timeout:                 int32(parentTask.Timeout),
		ChangeId:                parentTask.ChangeId,
		TaskName:                parentTask.Name,
		TaskId:                  parentTask.ID,
		ProductName:             parentTask.ProductName,
		WorkloadType:            util.GetComponentDataStr(componentsConfig, "workloadType"),
		TraceId:                 parentTask.TraceId,
		Extend:                  extendinfo,
		IsRollback:              strings.Contains(parentTask.Type, pkgutil.TaskActionRollback),
	}
	return ctx, req, nil
}

// 构建调用执行任务的参数
func buildStrategyProviderParam(ctx *strategyprovider.ReleaseConfig, req *strategyprovider.TaskStrategyRequest, subTask *infra.StarshipSubtask) (*strategyprovider.ReleaseConfig, *strategyprovider.TaskStrategyRequest) {
	ctx.Action = subTask.Action
	req.Action = subTask.Action

	ctx.SubTaskId = subTask.ID
	req.SubTaskId = subTask.ID

	return ctx, req
}

// 判断是否需要执行预检
func needPrecheck(subTasks []*infra.StarshipSubtask) bool {
	if subTasks == nil {
		return false
	}

	for _, task := range subTasks {
		if task.Status == pkgutil.TaskStatusPending && time.Now().After(task.StartTime) {
			// 检查当前时间是否在任务时间窗口内
			return true
		}
	}
	return false
}

// 判断是否需要执行升级
func needUpgrade(taskStrategy string, subTaskMap map[string][]*infra.StarshipSubtask) bool {
	if subTaskMap == nil || len(subTaskMap[pkgutil.TaskActionUpgrade]) == 0 {
		return false
	}

	// 对于plugin/grpc这类异步任务，需要等预检完成且没有失败才能执行upgrade
	if pkgutil.IsAsyncStrategy(taskStrategy) && len(subTaskMap[pkgutil.TaskActionPreCheck]) > 0 {
		precheckTaskId := subTaskMap[pkgutil.TaskActionPreCheck][0].ID
		preTaskInfo, err := infra.GetSubTask(precheckTaskId)
		if err != nil {
			klog.Errorf("get precheck task error: %s, id:%d", err.Error(), precheckTaskId)
			return false
		}
		if preTaskInfo.Status != pkgutil.TaskStatusDone {
			klog.Infof("precheck task is not done, id:%d", precheckTaskId)
			return false
		}
		if preTaskInfo.Status == pkgutil.TaskStatusDone && preTaskInfo.Reason != "" {
			klog.Infof("precheck task is done, but it has error: %s, id:%d", preTaskInfo.Reason, precheckTaskId)
			return false
		}
	}

	// 获取第一个pending状态的升级任务
	for _, task := range subTaskMap[pkgutil.TaskActionUpgrade] {
		if task.Status == pkgutil.TaskStatusPending && time.Now().After(task.StartTime) {
			return true
		}
	}

	return false
}

// rollback与upgrade类似：参考升级的逻辑进行处理
func needRollback(taskStrategy string, subTaskMap map[string][]*infra.StarshipSubtask) bool {
	if subTaskMap == nil || len(subTaskMap[pkgutil.TaskActionRollback]) == 0 {
		return false
	}

	// 对于plugin/grpc这类异步任务，需要等预检完成且没有失败才能执行upgrade
	if pkgutil.IsAsyncStrategy(taskStrategy) && len(subTaskMap[pkgutil.TaskActionPreCheck]) > 0 {
		precheckTaskId := subTaskMap[pkgutil.TaskActionPreCheck][0].ID
		preTaskInfo, err := infra.GetSubTask(precheckTaskId)
		if err != nil {
			klog.Errorf("get precheck task error: %s, id:%d", err.Error(), precheckTaskId)
			return false
		}
		if preTaskInfo.Status != pkgutil.TaskStatusDone {
			klog.Infof("precheck task is not done, id:%d", precheckTaskId)
			return false
		}
		if preTaskInfo.Status == pkgutil.TaskStatusDone && preTaskInfo.Reason != "" {
			klog.Infof("precheck task is done, but it has error: %s, id:%d", preTaskInfo.Reason, precheckTaskId)
			return false
		}
	}

	// 获取第一个pending状态的回滚任务
	for _, task := range subTaskMap[pkgutil.TaskActionRollback] {
		if task.Status == pkgutil.TaskStatusPending && time.Now().After(task.StartTime) {
			return true
		}
	}

	return false
}

func isHealthCheckHitRisk(subTaskId int64) (bool, error) {
	risks, err := infra.GetAllRisksBySubTask(subTaskId, "")
	if err != nil {
		klog.Errorf("get risks by precheck task id failed: %v", err)
		return false, err
	}

	for _, v := range risks {
		if v.Code != healthcheck.HEALTHCHECK_CODE_PASS &&
			(v.Level == healthcheck.RISK_LEVEL_FATAL || v.Level == healthcheck.RISK_LEVEL_WARNING) {
			return true, nil
		}
	}
	return false, nil
}

// 判断是否需要执行后检
func needPostcheck(taskStrategy string, subTaskMap map[string][]*infra.StarshipSubtask) bool {
	if subTaskMap == nil || len(subTaskMap[pkgutil.TaskActionPostCheck]) == 0 {
		return false
	}

	// 对于plugin/grpc和addon的方式不能立即执行后检, 需要等到升级完成后再执行
	if pkgutil.IsAsyncStrategy(taskStrategy) || taskStrategy == pkgutil.ReleaseStrategyDaemonSet || taskStrategy == pkgutil.ReleaseStrategyEkletagent {
		// 确保升级阶段存在，有的任务只有一个单独的阶段
		if len(subTaskMap[pkgutil.TaskActionUpgrade]) > 0 {
			upgradeTask := subTaskMap[pkgutil.TaskActionUpgrade][0]
			// 检查任务是否已完成
			taskInfo, err := infra.GetSubTask(upgradeTask.ID)
			if err != nil {
				klog.Errorf("get upgrade task error: %s", err.Error())
				return false
			}
			if taskInfo.Status != pkgutil.TaskStatusDone {
				klog.Infof("upgrade task is not done")
				return false
			}
		}
	}

	// 如果有符合pending，且在时间窗口内的后检任务
	for _, task := range subTaskMap[pkgutil.TaskActionPostCheck] {
		if task.Status == pkgutil.TaskStatusPending && time.Now().After(task.StartTime) {
			return true
		}
	}

	return false
}

// 判断任务是否过期
func isExpiredTask(parentTask *infra.StarshipTask, subTasks []*infra.StarshipSubtask) (bool, string) {
	// 满足任意一个标准，判定任务“已过期”
	// 标准1：【超时未完成】parentTask是processing状态，从开始执行到现在超过配置的超时时间，认为任务过期
	// 标准2：【超时未完成】parentTask是pending状态，存在不是pending状态的子任务，从开始执行到现在超过配置的超时时间，认为任务过期
	parentUpdateTimeUTC := parentTask.UpdateTime.UTC()
	timeNowUTC := time.Now().UTC()

	// 获取基于组件配置的超时时间
	timeout := getComponentTimeout(parentTask)

	if parentTask.Status == pkgutil.TaskStatusProcessing && parentUpdateTimeUTC.Add(timeout).Before(timeNowUTC) {
		klog.Errorf("task %d expired, phase:%s, parent task update time by utc:%s, current time by utc:%s, timeout:%v",
			parentTask.ID, parentTask.Phase, parentUpdateTimeUTC, timeNowUTC, timeout)
		return true, parentTask.Phase
	}

	for _, v := range subTasks {
		if v.Status == pkgutil.TaskStatusProcessing && v.UpdateTime.UTC().Add(timeout).Before(timeNowUTC) {
			klog.Errorf("task %d expired, phase:%s. subtask update time by utc:%s, current time by utc:%s, timeout:%v",
				parentTask.ID, v.Action, v.UpdateTime.UTC(), timeNowUTC, timeout)
			return true, v.Action
		}
	}
	return false, ""
}

// getComponentTimeout 获取基于组件配置的超时时间
func getComponentTimeout(parentTask *infra.StarshipTask) time.Duration {
	// 默认超时时间（保持向后兼容）
	defaultTimeout := 40 * time.Minute

	// 基于策略的默认超时时间（保持现有逻辑）
	strategyTimeout := defaultTimeout
	if parentTask.Strategy == pkgutil.ReleaseStrategyDaemonSet {
		strategyTimeout = 48 * time.Hour
	}
	if parentTask.Strategy == pkgutil.ReleaseStrategyGRPC {
		strategyTimeout = 2 * time.Hour
	}

	// 尝试从组件配置中读取超时时间
	componentsConfigContent := util.GetRainbowData(fmt.Sprintf("components/%s/config", parentTask.AppName))
	if componentsConfigContent == "" {
		klog.V(5).Infof("component config not found for %s, using strategy-based timeout: %v", parentTask.AppName, strategyTimeout)
		return strategyTimeout
	}

	componentsConfig := make(map[string]interface{})
	if err := yaml.Unmarshal([]byte(componentsConfigContent), &componentsConfig); err != nil {
		klog.Warningf("failed to unmarshal component config for %s: %v, using strategy-based timeout: %v", parentTask.AppName, err, strategyTimeout)
		return strategyTimeout
	}

	// 解析超时时间配置
	timeoutMinutes := util.GetComponentDataInt(componentsConfig, "timeout")
	if timeoutMinutes <= 0 {
		klog.Warningf("invalid timeout configuration for component %s: %d minutes, using strategy-based timeout: %v", parentTask.AppName, timeoutMinutes, strategyTimeout)
		return strategyTimeout
	}

	configuredTimeout := time.Duration(timeoutMinutes) * time.Minute
	klog.V(5).Infof("using configured timeout for component %s: %v", parentTask.AppName, configuredTimeout)
	return configuredTimeout
}

func decideStrategy(parentTask *infra.StarshipTask) (string, string, error) {
	componentsStrategyContent := util.GetRainbowData(fmt.Sprintf("components/%s/strategy/strategy", parentTask.AppName))
	componentsStrategy := make(map[string]interface{})
	if err := yaml.Unmarshal([]byte(componentsStrategyContent), &componentsStrategy); err != nil {
		return "", "", fmt.Errorf("unmarshal components strategy from %s failed: %v", componentsStrategyContent, err)
	}
	precheckStrategy := util.GetComponentDataStr(componentsStrategy, "defaultPrecheckStrategy")
	if precheckStrategy == "" {
		precheckStrategy = parentTask.Strategy
	}
	postcheckStrategy := util.GetComponentDataStr(componentsStrategy, "defaultPostcheckStrategy")
	if postcheckStrategy == "" {
		postcheckStrategy = parentTask.Strategy
	}
	return precheckStrategy, postcheckStrategy, nil
}
