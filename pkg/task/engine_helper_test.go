package task

import (
	"fmt"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"

	"git.woa.com/kmetis/starship/infra"
	taskutil "git.woa.com/kmetis/starship/pkg/task/util"
	pkgutil "git.woa.com/kmetis/starship/pkg/util"
)

func TestGetComponentTimeout(t *testing.T) {
	testCases := []struct {
		name            string
		parentTask      *infra.StarshipTask
		mockConfigData  string
		expectedTimeout time.Duration
		description     string
	}{
		{
			name: "default timeout when no config",
			parentTask: &infra.StarshipTask{
				AppName:  "test-component",
				Strategy: "deployment",
			},
			mockConfigData:  "",
			expectedTimeout: 40 * time.Minute,
			description:     "应该使用默认超时时间当没有配置时",
		},
		{
			name: "strategy-based timeout for daemonset",
			parentTask: &infra.StarshipTask{
				AppName:  "test-component",
				Strategy: pkgutil.ReleaseStrategyDaemonSet,
			},
			mockConfigData:  "",
			expectedTimeout: 48 * time.Hour,
			description:     "DaemonSet策略应该使用48小时超时",
		},
		{
			name: "strategy-based timeout for grpc",
			parentTask: &infra.StarshipTask{
				AppName:  "test-component",
				Strategy: pkgutil.ReleaseStrategyGRPC,
			},
			mockConfigData:  "",
			expectedTimeout: 2 * time.Hour,
			description:     "GRPC策略应该使用2小时超时",
		},
		{
			name: "configured timeout overrides strategy",
			parentTask: &infra.StarshipTask{
				AppName:  "test-component",
				Strategy: pkgutil.ReleaseStrategyDaemonSet,
			},
			mockConfigData: `
timeout: 120
location: user
namespace: kube-system
`,
			expectedTimeout: 120 * time.Minute,
			description:     "配置的超时时间应该覆盖策略默认值",
		},
		{
			name: "invalid timeout falls back to strategy",
			parentTask: &infra.StarshipTask{
				AppName:  "test-component",
				Strategy: pkgutil.ReleaseStrategyGRPC,
			},
			mockConfigData: `
timeout: -10
location: user
namespace: kube-system
`,
			expectedTimeout: 2 * time.Hour,
			description:     "无效的超时配置应该回退到策略默认值",
		},
		{
			name: "zero timeout falls back to strategy",
			parentTask: &infra.StarshipTask{
				AppName:  "test-component",
				Strategy: "deployment",
			},
			mockConfigData: `
timeout: 0
location: user
namespace: kube-system
`,
			expectedTimeout: 40 * time.Minute,
			description:     "零超时配置应该回退到默认值",
		},
		{
			name: "custom timeout for regular strategy",
			parentTask: &infra.StarshipTask{
				AppName:  "test-component",
				Strategy: "deployment",
			},
			mockConfigData: `
timeout: 60
location: user
namespace: kube-system
workloadType: deployment
`,
			expectedTimeout: 60 * time.Minute,
			description:     "普通策略应该使用配置的自定义超时时间",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 模拟七彩石配置数据
			if tc.mockConfigData != "" {
				taskutil.SetRainbowData(fmt.Sprintf("components/%s/config", tc.parentTask.AppName), tc.mockConfigData)
			} else {
				// 清除配置数据
				taskutil.SetRainbowData(fmt.Sprintf("components/%s/config", tc.parentTask.AppName), "")
			}

			// 调用函数
			actualTimeout := getComponentTimeout(tc.parentTask)

			// 验证结果
			assert.Equal(t, tc.expectedTimeout, actualTimeout, tc.description)
		})
	}
}

func TestIsExpiredTask_WithConfiguredTimeout(t *testing.T) {
	// 创建测试任务
	parentTask := &infra.StarshipTask{
		ID:         1,
		AppName:    "test-component",
		Strategy:   "deployment",
		Status:     pkgutil.TaskStatusProcessing,
		Phase:      "upgrade",
		UpdateTime: time.Now().UTC().Add(-70 * time.Minute), // 70分钟前更新
	}

	subTasks := []*infra.StarshipSubtask{
		{
			ID:         1,
			Status:     pkgutil.TaskStatusProcessing,
			Action:     "precheck",
			UpdateTime: time.Now().UTC().Add(-50 * time.Minute), // 50分钟前更新
		},
	}

	testCases := []struct {
		name            string
		configData      string
		expectedExpired bool
		description     string
	}{
		{
			name: "not expired with default timeout",
			configData: `
location: user
namespace: kube-system
`, // 没有配置timeout，使用默认40分钟
			expectedExpired: false,
			description:     "使用默认40分钟超时，70分钟前的任务应该过期",
		},
		{
			name: "expired with custom short timeout",
			configData: `
timeout: 30
location: user
namespace: kube-system
`,
			expectedExpired: true,
			description:     "使用30分钟超时，70分钟前的任务应该过期",
		},
		{
			name: "not expired with custom long timeout",
			configData: `
timeout: 120
location: user
namespace: kube-system
`,
			expectedExpired: false,
			description:     "使用120分钟超时，70分钟前的任务不应该过期",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// 设置配置数据
			taskutil.SetRainbowData(fmt.Sprintf("components/%s/config", parentTask.AppName), tc.configData)

			// 调用函数
			expired, phase := isExpiredTask(parentTask, subTasks)

			// 验证结果
			assert.Equal(t, tc.expectedExpired, expired, tc.description)
			if expired {
				assert.Equal(t, parentTask.Phase, phase)
			}
		})
	}
}

func TestIsExpiredTask_BackwardCompatibility(t *testing.T) {
	// 测试向后兼容性：没有配置时应该使用原有的策略逻辑
	testCases := []struct {
		name            string
		strategy        string
		updateTime      time.Time
		expectedExpired bool
		description     string
	}{
		{
			name:            "default strategy not expired",
			strategy:        "deployment",
			updateTime:      time.Now().UTC().Add(-30 * time.Minute),
			expectedExpired: false,
			description:     "默认策略30分钟前的任务不应该过期",
		},
		{
			name:            "default strategy expired",
			strategy:        "deployment",
			updateTime:      time.Now().UTC().Add(-50 * time.Minute),
			expectedExpired: true,
			description:     "默认策略50分钟前的任务应该过期",
		},
		{
			name:            "daemonset strategy not expired",
			strategy:        pkgutil.ReleaseStrategyDaemonSet,
			updateTime:      time.Now().UTC().Add(-24 * time.Hour),
			expectedExpired: false,
			description:     "DaemonSet策略24小时前的任务不应该过期",
		},
		{
			name:            "grpc strategy not expired",
			strategy:        pkgutil.ReleaseStrategyGRPC,
			updateTime:      time.Now().UTC().Add(-1 * time.Hour),
			expectedExpired: false,
			description:     "GRPC策略1小时前的任务不应该过期",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			parentTask := &infra.StarshipTask{
				ID:         1,
				AppName:    "test-component",
				Strategy:   tc.strategy,
				Status:     pkgutil.TaskStatusProcessing,
				Phase:      "upgrade",
				UpdateTime: tc.updateTime,
			}

			// 清除配置数据以测试向后兼容性
			taskutil.SetRainbowData(fmt.Sprintf("components/%s/config", parentTask.AppName), "")

			expired, _ := isExpiredTask(parentTask, nil)
			assert.Equal(t, tc.expectedExpired, expired, tc.description)
		})
	}
}
