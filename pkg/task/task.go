package task

import (
	"context"
	"errors"
	"fmt"
	"strings"
	"sync"
	"time"

	"gorm.io/gorm"
	"k8s.io/klog/v2"

	ec "git.woa.com/kmetis/starship/errcode"
	"git.woa.com/kmetis/starship/infra"
	"git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/metrics"
	pkgutil "git.woa.com/kmetis/starship/pkg/util"
)

const (
	TIMEOUT = 60 // 单位：秒
)

// 创建任务，将任务信息写入DB
func CreateTask(ctx context.Context, req *pb.CreateTaskRequest) (*pb.CreateTaskReply, error) {
	rsp := &pb.CreateTaskReply{}
	defer func() {
		if rsp.Code != 0 {
			metrics.TaskCreateFailedTotal.WithLabelValues().Inc()
		}
	}()
	if !isCorrectSubTaskType(req.GetType()) {
		errMsg := fmt.Sprintf("task type %s is not supported, clusterId:%s", req.GetType(), req.GetClusterId())
		klog.Errorf("%s", errMsg)
		rsp.Code = ec.EC_LOGIC_PARAM_ERROR
		rsp.Reason = errMsg
		return rsp, nil
	}

	// 根据request构建DB task
	task := BuildDBTaskRecord(req)
	// 将task信息写入DB
	taskId, err := infra.CreateTaskAndSubTasks(task)
	if err != nil {
		klog.Errorf("create task err:%v, cluster-id:%s, app-name:%s", err, req.GetClusterId(), req.GetAppName())
		rsp.Code = ec.EC_SYS_DB_ERROR
		rsp.Reason = fmt.Sprintf("create task err:%v", err)
		return rsp, nil
	}

	// 构造返回值
	rsp.TaskId = taskId
	klog.Infof("add new task success, create task response:%+v", rsp)

	return rsp, nil
}

// 更新DB中子任务运行状态
func ReportTaskResult(ctx context.Context, req *pb.ReportTaskResultRequest) (*pb.ReportTaskResultReply, error) {
	if req == nil {
		errMsg := "request is nil"
		klog.Errorf("%s", errMsg)
		return nil, fmt.Errorf("%s", errMsg)
	}
	klog.Infof("report task result request:%+v", req)
	rsp := &pb.ReportTaskResultReply{
		SubtaskId: req.GetSubtaskId(),
	}
	defer func() {
		if rsp.Code != 0 {
			metrics.TaskReportFailedTotal.WithLabelValues().Inc()
		}
	}()
	// 前置检查：如果任务已经完成，则不需要任何更新
	// 因为plugin的任务，会有通用预检和plugin自带的预检。如果通用预检失败，会更新task表为done，此时如果有plugin的预检结果上报，
	// 则丢弃该task（通用预检失败，不会走到plugin的预检里面去）。
	task, err := infra.GetTask(req.GetTaskId())
	if err != nil {
		klog.Errorf("get task err:%v, task-id:%d", err, req.GetTaskId())
		rsp.Code = ec.EC_SYS_DB_ERROR
		rsp.Reason = fmt.Sprintf("get task err:%v", err)
		return rsp, nil
	}
	if task.Status == pkgutil.TaskStatusDone {
		rsp.Code = ec.EC_LOGIC_UPDATE_FINISHED_JOB_ERROR
		rsp.Reason = "task is already done"
		klog.Infof("task-id:%d, %s", req.GetTaskId(), rsp.Reason)
		return rsp, nil
	}

	// 更新子任务表，新建risk表
	// step1： 更新子任务表
	now := time.Now()
	newSubTask := &infra.StarshipSubtask{
		ID:       req.GetSubtaskId(),
		Status:   req.GetStatus(),
		Reason:   req.GetReason(),
		CostTime: int(req.GetCostTime()),
		EndTime:  &now,
	}
	err = infra.UpdateSubTask(newSubTask)
	if err != nil {
		klog.Errorf("update sub task err:%v, subTask-id:%d, app-name:%s", err, req.GetSubtaskId(), req.GetAppName())
		rsp.Code = ec.EC_SYS_DB_ERROR
		rsp.Reason = fmt.Sprintf("update sub task err:%v", err)
		return rsp, nil
	}
	// step2: 新建risk表
	err = CreateRisksInDB(req)
	if err != nil {
		klog.Errorf("create risk err:%v, subTask-id:%d, app-name:%s", err, req.GetSubtaskId(), req.GetAppName())
		rsp.Code = ec.EC_SYS_DB_ERROR
		rsp.Reason = fmt.Sprintf("create risk err:%v", err)
		return rsp, nil
	}

	// step3: 如果子任务有报错，则需要更新父任务表为done, 让任务不再重新调度。
	// NOTICE: 子任务有报错时，Status是done，但是Reason不为空。
	// NOTICE: 对于异步上报任务，失败、后检完成 或者 只有预检且完成时，都要更新父任务为完成
	if req.GetReason() != "" || (req.Action == pkgutil.TaskActionPostCheck && req.GetStatus() == pkgutil.TaskStatusDone) ||
		(task.Type == pkgutil.TaskActionPreCheck && req.Action == pkgutil.TaskActionPreCheck && req.GetStatus() == pkgutil.TaskStatusDone) {
		task.Status = pkgutil.TaskStatusDone
		task.Reason = req.GetReason()
		err = infra.UpdateTask(task)
		if err != nil {
			klog.Errorf("update task err:%v, task-id:%d", err, req.GetTaskId())
			rsp.Code = ec.EC_SYS_DB_ERROR
			rsp.Reason = fmt.Sprintf("update task err:%v", err)
			return rsp, nil
		}
		return rsp, nil
	}
	return rsp, nil
}

// 查询DB中的任务，返回给cloud-gw
func DescribeTask(ctx context.Context, req *pb.DescribeTaskRequest) (*pb.DescribeTaskReply, error) {
	klog.Infof("describe task request:%+v", req)
	// 从DB查询任务记录
	var task *infra.StarshipTask
	var err error
	rsp := &pb.DescribeTaskReply{}
	defer func() {
		if err != nil {
			metrics.TaskListFailedTotal.WithLabelValues().Inc()
		}
	}()
	if req.GetTaskId() != 0 {
		task, err = infra.GetTask(req.GetTaskId())
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				klog.Errorf("get task err:%v, task-id:%d", err, req.GetTaskId())
				return rsp, nil
			}
			klog.Errorf("get task err:%v, task-id:%d", err, req.GetTaskId())
			return nil, err
		}
	} else {
		// 根据ClusterId, AppName, Type, ChangeId（非必填，为空时返回最新的任务）查询任务记录
		task, err = infra.GetTaskByCondition(req.GetClusterId(), req.GetAppName(), req.GetType(), req.GetChangeId())
		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				klog.Errorf("get task err:%v, cluster-id:%s, app-name:%s", err, req.GetClusterId(), req.GetAppName())
				return rsp, nil
			}
			klog.Errorf("filter task by condition err:%v, cluster-id:%s, app-name:%s", err, req.GetClusterId(), req.GetAppName())
			return nil, err
		}
	}

	// 构造返回值
	rsp, err = BuildPBTaskReply(task)
	if err != nil {
		klog.Errorf("build task pb response err:%v, cluster-id:%s, app-name:%s", err, req.GetClusterId(), req.GetAppName())
		return nil, err
	}

	return rsp, nil
}

func CancelTask(ctx context.Context, req *pb.CancelTaskRequest) (*pb.CancelTaskReply, error) {
	return &pb.CancelTaskReply{}, nil
}

func RunTask() {
	// start internal task handle
	go func() {
		ticker := time.NewTicker(1 * 60 * time.Second)
		for {
			select {
			case <-ticker.C:
				// 1.找到未完成的任务
				tasks, err := infra.GetUncompletedTask()
				if err != nil {
					klog.Errorf("GetUncompletedTask failed: %v", err)
					continue
				}

				// 2. 并行处理
				if len(tasks) == 0 {
					continue
				}

				taskCh := make(chan *infra.StarshipTask)
				var wg sync.WaitGroup

				// 启动10个worker
				numWorkers := 10
				for i := 0; i < numWorkers; i++ {
					wg.Add(1)
					go worker(i, taskCh, &wg)
				}

				// 将任务发送到通道
				for _, task := range tasks {
					taskCh <- task
				}
				close(taskCh)

				// 等待所有worker完成任务
				wg.Wait()
				klog.Infof("all tasks done")
			}
		}
	}()

	// 定时清理risk表
	go func() {
		now := time.Now()
		// 凌晨时运行清理任务
		nextMidnight := time.Date(now.Year(), now.Month(), now.Day()+1, 0, 0, 0, 0, now.Location())
		duration := nextMidnight.Sub(now)
		timer := time.NewTimer(duration)
		for {
			<-timer.C

			// 重新设置定时器
			timer.Reset(7 * 24 * time.Hour)

			// 执行 MySQL 任务清理任务
			infra.DeleteExpiredRisks()
		}
	}()
}

// Worker 处理任务
func worker(id int, taskCh <-chan *infra.StarshipTask, wg *sync.WaitGroup) {
	defer wg.Done()

	for task := range taskCh {
		klog.Infof("worker %d start process task %d, clusterId:%s, appName:%s, changeId:%s, strategy:%s, traceId:%s", id, task.ID, task.ClusterId, task.AppName, task.ChangeId, task.Strategy, task.TraceId)
		err := processTask(task)
		if err != nil {
			metrics.TaskExecFailedTotal.WithLabelValues().Inc()
			klog.Errorf("processTask err: %v, traceId:%s", err, task.TraceId)
		}
	}
}

func RollbackTask(ctx context.Context, req *pb.RollbackTaskRequest) (*pb.RollbackTaskReply, error) {
	_, err := infra.GetRevisionByTask(req.TaskId)
	if err != nil {
		klog.Errorf("failed to get revision for task %d: %v", req.TaskId, err)
		return nil, err
	}

	task, err := infra.GetTask(req.TaskId)
	if err != nil {
		klog.Errorf("failed to get task %d: %v", req.TaskId, err)
		return nil, err
	}

	rollbackTaskReq := &pb.CreateTaskRequest{
		Name:          task.Name,
		ClusterId:     task.ClusterId,
		AppName:       task.AppName,
		ChangeId:      task.ChangeId,
		Strategy:      task.Strategy,
		Namespace:     task.Namespace,
		MetaclusterId: task.MetaClusterId,
		Region:        task.Region,
		ProductName:   task.ProductName,
		// change upgrade to rollback
		Type: strings.Replace(task.Type, pkgutil.TaskActionUpgrade, pkgutil.TaskActionRollback, 1),
		// use extend info to store task id
		ExtendInfo: fmt.Sprintf("{\"Rollback\":{\"TaskId\":\"%d\"}}", req.TaskId),
	}

	rsp, err := CreateTask(ctx, rollbackTaskReq)
	if err != nil {
		metrics.TaskCreateFailedTotal.WithLabelValues().Inc()
		klog.Errorf("create rollback task err:%v, cluster-id:%s, region:%s", err, req.GetTaskId(), req.GetRegion())
		return &pb.RollbackTaskReply{
			TaskId:     rsp.TaskId,
			ErrMessage: err.Error(),
		}, nil
	}

	klog.Infof("rollback task %d, traceId:%s", rsp.TaskId, task.TraceId)
	return &pb.RollbackTaskReply{
		TaskId: rsp.TaskId,
	}, nil
}
