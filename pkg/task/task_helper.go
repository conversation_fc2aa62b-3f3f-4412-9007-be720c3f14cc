package task

import (
	"encoding/base64"
	"fmt"
	"strings"

	"github.com/google/uuid"
	"k8s.io/klog/v2"

	"git.woa.com/kmetis/starship/infra"
	"git.woa.com/kmetis/starship/pb"
	pkgutil "git.woa.com/kmetis/starship/pkg/util"
)

const (
	TIME_FORMAT     = "2006-01-02 15:04:05"
	TASK_BATCH_SIZE = 100
)

// 根据pb Task构建DB Task信息
func BuildDBTaskRecord(pbTask *pb.CreateTaskRequest) *infra.StarshipTask {
	traceId := pbTask.GetTraceId()
	if traceId == "" {
		traceId = uuid.NewString()
	}
	task := &infra.StarshipTask{
		Name:          pbTask.GetName(),          // 任务名称
		AppName:       pbTask.GetAppName(),       // app名称
		Type:          pbTask.GetType(),          // 任务类型
		Phase:         pbTask.GetPhase(),         // 任务阶段
		ClusterId:     pbTask.GetClusterId(),     // 集群id
		Namespace:     pbTask.GetNamespace(),     // 负载的namespace
		MetaClusterId: pbTask.GetMetaclusterId(), // meta集群id
		Region:        pbTask.GetRegion(),        // 地域信息
		ProductName:   pbTask.GetProductName(),   // ProductName
		ImageTag:      pbTask.GetImageTag(),      // 镜像tag
		Envs:          pbTask.GetEnvs(),          // 环境变量列表
		User:          pbTask.GetUser(),          // 变更执行人
		ExtendInfo:    pbTask.GetExtendInfo(),    // 扩展信息
		Strategy:      pbTask.GetStrategy(),      // 发布策略
		PluginVersion: pbTask.GetPluginVersion(), // 插件版本
		Timeout:       int(pbTask.GetTimeout()),  // 超时时间
		ChangeId:      pbTask.GetChangeId(),      // 发布单id
		BatchId:       int(pbTask.GetBatchId()),  // 变更批次id
		TraceId:       traceId,                   // TraceId,
	}

	// NOTICE: token信息base64编码后再存储
	task.Token = base64.StdEncoding.EncodeToString([]byte(pbTask.GetToken()))

	// 当从app-controller过来的请求没有带changeID时，用clientToken代替
	if task.ChangeId == "" {
		task.ChangeId = pbTask.GetClientToken()
	}

	// 任务初始创建时,赋值为指定的status
	task.Status = pkgutil.TaskStatusPending // 新任务

	return task
}

// 根据DB task构建pb taskReply信息
func BuildPBTaskReply(dbTask *infra.StarshipTask) (*pb.DescribeTaskReply, error) {
	rsp := &pb.DescribeTaskReply{
		TaskId:     dbTask.ID,                             // 任务id
		Name:       dbTask.Name,                           // 任务名称
		AppName:    dbTask.AppName,                        // app名称
		Stage:      dbTask.Phase,                          // 任务阶段
		Envs:       dbTask.Envs,                           // 环境变量列表
		ExtendInfo: dbTask.ExtendInfo,                     // 扩展信息，比如镜像tag等
		Region:     dbTask.Region,                         // 地域信息
		ClusterId:  dbTask.ClusterId,                      // 集群id
		Timeout:    int32(dbTask.Timeout),                 // 任务超时控制,
		ChangeId:   dbTask.ChangeId,                       // 发布单id
		Batch:      int32(dbTask.BatchId),                 // 变更批次
		Status:     dbTask.Status,                         // 任务状态
		Reason:     dbTask.Reason,                         // 任务失败原因
		UpdateTime: dbTask.UpdateTime.Format(TIME_FORMAT), // 更新时间
		CreateTime: dbTask.CreateTime.Format(TIME_FORMAT), // 创建时间
	}

	subTasks, err := BuildPBSubTasks(dbTask.ID)
	if err != nil {
		klog.Errorf("build pb sub task error: %s", err.Error())
		return nil, err
	}
	rsp.Subtasks = append(rsp.Subtasks, subTasks...)

	return rsp, nil
}

// 根据DB task构建pb SubTask列表信息
func BuildPBSubTasks(parentTaskId int64) ([]*pb.SubTask, error) {
	if parentTaskId == 0 {
		return nil, fmt.Errorf("parent task id is empty")
	}
	subTasks, err := infra.GetSubTaskByParent(parentTaskId, "")
	if err != nil {
		klog.Errorf("get sub task by parent error: %s, parentTaskId:%d", err.Error(), parentTaskId)
		return nil, err
	}

	// 将子任务的信息转换为pb结构体
	var respSubTasks []*pb.SubTask
	for _, v := range subTasks {
		subTask, err := BuildSinglePBSubTask(v)
		if err != nil {
			klog.Errorf("build single pb subtask error: %s, db subTask info:%+v", err.Error(), v)
			return nil, err
		}
		respSubTasks = append(respSubTasks, subTask)
	}

	return respSubTasks, nil
}

// 根据DB SubTask构建pb SubTask信息
func BuildSinglePBSubTask(subTask *infra.StarshipSubtask) (*pb.SubTask, error) {
	respSubTask := &pb.SubTask{
		SubtaskId:    subTask.ID,                             // 子任务id
		ParentTaskId: subTask.ParentTaskId,                   // 子任务id
		AppName:      subTask.AppName,                        // app名称
		Action:       subTask.Action,                         // 操作类型
		CostTime:     int32(subTask.CostTime),                // 发布耗时
		Status:       subTask.Status,                         // 任务状态,
		Reason:       subTask.Reason,                         // 任务失败原因
		UpdateTime:   subTask.UpdateTime.Format(TIME_FORMAT), // 更新时间
		CreateTime:   subTask.CreateTime.Format(TIME_FORMAT), // 创建时间

	}
	// 前端不允许返回null数据，所以这里需要初始化
	respSubTask.Risks = make([]*pb.Risk, 0)

	risks, err := BuildPBRisks(subTask.ID)
	if err != nil {
		klog.Errorf("build pb risk error: %s, subTask id:%d", err.Error(), subTask.ID)
		return nil, err
	}
	respSubTask.Risks = append(respSubTask.Risks, risks...)
	return respSubTask, nil
}

// 根据DB SubTask构建pb Risk列表信息
func BuildPBRisks(subTaskId int64) ([]*pb.Risk, error) {
	risks, err := infra.GetAllRisksBySubTask(subTaskId, "")
	if err != nil {
		klog.Errorf("get risks by sub task error: %s, subTaskId:%d", err.Error(), subTaskId)
		return nil, err
	}

	// 将DB中的子任务的信息转换为pb结构体
	var respRisks []*pb.Risk
	for _, v := range risks {
		respRisks = append(respRisks, &pb.Risk{
			Id:              v.ID,
			SubtaskId:       v.SubTaskId,
			AppName:         v.AppName,
			Name:            v.Name,
			Code:            v.Code,
			Detail:          v.Detail,
			Level:           v.Level,
			Solution:        v.Solution,
			HealthCheckName: v.HealthCheckName,
			CreateTime:      v.CreateTime.Format(TIME_FORMAT),
		})
	}

	return respRisks, nil
}

// 创建DB risk记录
func CreateRisksInDB(req *pb.ReportTaskResultRequest) error {
	var risks []*infra.StarshipRisks
	for _, v := range req.GetRisks() {
		// 空risk不允许上报
		if v.Code == "" || v.AppName == "" {
			klog.Errorf("empty risk, risk:%+v, subTask-id:%d", v, req.GetSubtaskId())
			continue
		}
		newRisk := &infra.StarshipRisks{
			SubTaskId:       req.GetSubtaskId(),
			AppName:         req.GetAppName(),
			Name:            v.GetName(),
			Code:            v.GetCode(),
			Detail:          v.GetDetail(),
			Level:           v.GetLevel(),
			Solution:        v.GetSolution(),
			HealthCheckName: v.GetHealthCheckName(),
		}
		risks = append(risks, newRisk)
	}

	if len(risks) > 0 {
		// 对于app-controller过来的请求，会只更新upgrade任务状态，没有risk信息。
		err := infra.CreateRisk(risks)
		if err != nil {
			klog.Errorf("create risk err:%v, subTask-id:%d", err, req.GetSubtaskId())
			return err
		}
	}

	return nil
}

// 检查子任务类型是否合法
func isCorrectSubTaskType(taskType string) bool {
	types := strings.Split(taskType, ",")
	counts := make(map[string]int)
	for _, value := range types {
		counts[value]++
	}

	for value, count := range counts {
		if count > 1 {
			klog.Infof("repeated task type: %s\n", value)
			return false
		}

		switch value {
		case pkgutil.TaskActionPreCheck, pkgutil.TaskActionUpgrade, pkgutil.TaskActionPostCheck, pkgutil.TaskActionRollback:
			// 枚举值有效
		default:
			klog.Infof("invalid task type: %s\n", value)
			return false
		}
	}

	return true
}

// 将任务分多个批次，每一批会并行处理
func SplitTasksForMultipleBatch(tasks []*infra.StarshipTask, batchSize int) [][]*infra.StarshipTask {
	var taskSplit [][]*infra.StarshipTask
	taskTotal := len(tasks)
	if batchSize <= 0 {
		batchSize = TASK_BATCH_SIZE
	}
	if batchSize > TASK_BATCH_SIZE {
		batchSize = TASK_BATCH_SIZE
	}
	for i := 0; i < taskTotal; i += batchSize {
		batch := tasks[i:min(i+batchSize, taskTotal)]
		taskSplit = append(taskSplit, batch)
	}
	return taskSplit
}
