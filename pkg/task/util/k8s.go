package util

import (
	"context"
	"k8s.io/client-go/kubernetes"

	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/apis/meta/v1/unstructured"
	"k8s.io/apimachinery/pkg/runtime/schema"
	"k8s.io/client-go/dynamic"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
)

const jobNamespace = "healthcheck"
const jobLimit = 50

var internalK8sClient *rest.Config

func CreateOrUpdateResource(ctx context.Context, resource []byte) error {
	unstructuredObj := &unstructured.Unstructured{}
	if err := unstructuredObj.UnmarshalJSON(resource); err != nil {
		return err
	}

	gvr := schema.GroupVersionResource{
		Group:    "batch",
		Version:  "v1",
		Resource: "jobs",
	}
	dynamicClient, err := dynamic.NewForConfig(internalK8sClient)
	if err != nil {
		return err
	}

	unstructuredObj.SetNamespace(jobNamespace)
	_, err = dynamicClient.Resource(gvr).Namespace(jobNamespace).Create(ctx, unstructuredObj, metav1.CreateOptions{})
	if err != nil {
		if !errors.IsAlreadyExists(err) {
			return err
		}
	}

	return nil
}

// TODO: 后续优化，使用QPS做限速，而不是使用总的job树木
func IsJobExceedLimit() (bool, error) {
	client, err := getK8sClient()
	if err != nil {
		return true, err
	}
	job, err := client.BatchV1().Jobs(jobNamespace).List(context.TODO(), metav1.ListOptions{
		ResourceVersion: "0",
	})
	if err != nil {
		return true, err
	}
	return len(job.Items) > jobLimit, nil
}

func getK8sRestConfig() (*rest.Config, error) {
	return clientcmd.BuildConfigFromFlags("", "")
}

func getK8sClient() (kubernetes.Interface, error) {
	return kubernetes.NewForConfig(internalK8sClient)
}
