package util

import (
	"encoding/json"
	"fmt"
	"sync"

	"git.code.oa.com/rainbow/golang-sdk/types"
	"git.code.oa.com/rainbow/golang-sdk/v3/confapi"
	"git.code.oa.com/rainbow/golang-sdk/v3/watch"
	v3 "git.code.oa.com/rainbow/proto/api/configv3"
	"git.woa.com/kmetis/starship/pkg/config"
	"k8s.io/klog"
)

type rainbowClient struct {
	data map[string]string
	lock sync.RWMutex
}

func GetRainbowData(key string) string {
	rbData.lock.RLock()
	defer rbData.lock.RUnlock()

	return rbData.data[key]
}

func SetRainbowData(key, value string) {
	rbData.lock.Lock()
	defer rbData.lock.Unlock()

	rbData.data[key] = value
}

var rbData = rainbowClient{
	data: make(map[string]string),
}

func callback(oldVal watch.Result, newVal []*v3.Item) error {
	oldData, _ := json.Marshal(oldVal)
	newData, _ := json.Marshal(newVal)
	klog.Infof("rainbow watch old value: %s \n", oldData)
	klog.Infof("rainbow watch new value: %s \n", newData)

	kvs, err := rainbow.GetGroup(getOpts...)
	if err != nil {
		klog.Errorf("GetFileData failed: %v \n", err)
		return err
	}

	for k, v := range kvs {
		if str, ok := v.(string); ok {
			klog.Infof("SetRainbowData: %v %v \n", k, str)
			SetRainbowData(k, str)
		} else {
			klog.Errorf("invalid type for key %v: expected string, got %T", k, v)
		}
	}
	return nil
}

var rainbow *confapi.ConfAPIV2
var getOpts []types.AssignGetOption
var once sync.Once

// func InitRainbow(userId, userKey, region, appId, group, env string) error {
func initRainbow(rb config.Rainbow) error {
	var err error
	rainbow, err = confapi.NewV2(types.CarryClientInfoV2("region", rb.Region))
	if err != nil {
		klog.Errorf("NewV2 failed: %v \n", err)
		return err
	}

	getOpts = append(getOpts, types.WithAppID(rb.AppId))
	getOpts = append(getOpts, types.WithGroup(rb.Group))
	getOpts = append(getOpts, types.WithEnvName(rb.Env))
	getOpts = append(getOpts, types.WithHmacWay("sha1"))
	getOpts = append(getOpts, types.WithUserID(rb.UserId))
	getOpts = append(getOpts, types.WithUserKey(rb.UserKey))

	err = rainbow.PreloadGroups([][]types.AssignGetOption{getOpts}...)
	if err != nil {
		klog.Errorf("PreloadGroups failed: %v \n", err)
		return err
	}

	kvs, err := rainbow.GetGroup(getOpts...)
	if err != nil {
		klog.Errorf("GetFileData failed: %v \n", err)
		return err
	}

	// init rainbow data
	for k, v := range kvs {
		if str, ok := v.(string); ok {
			klog.Infof("SetRainbowData: %v %v \n", k, str)
			SetRainbowData(k, str)
		} else {
			return fmt.Errorf("invalid type for key %v: expected string, got %T", k, v)
		}
	}

	err = rainbow.AddWatcher(watch.Watcher{CB: callback}, getOpts...)
	if err != nil {
		klog.Errorf("AddWatcher failed: %v \n", err)
		return err
	}

	return nil
}

func GetComponentDataStr(configMap map[string]interface{}, key string) string {
	if configMapData, ok := configMap[key]; ok {
		return configMapData.(string)
	}
	return ""
}

func GetComponentDataInt(configMap map[string]interface{}, key string) int {
	if configMapData, ok := configMap[key]; ok {
		switch val := configMapData.(type) {
		case float64:
			return int(val)
		case int:
			return val
		default:
			return 0
		}
	}
	return 0
}

func ConvertToMap(configMap map[string]interface{}, key string) (map[string]string, error) {
	result := make(map[string]string)
	if configMapData, ok := configMap[key]; ok {
		dataMap, ok := configMapData.(map[string]interface{})
		if !ok {
			return nil, fmt.Errorf("failed to convert interface{} to map[string]interface{}")
		}

		// 遍历 map[string]interface{}，将值转换为 string 并存入 map[string]string
		for key, value := range dataMap {
			stringValue, ok := value.(string)
			if !ok {
				return nil, fmt.Errorf("failed to convert value to string for key: %s", key)
			}
			result[key] = stringValue
		}
	} else {
		return nil, fmt.Errorf("failed to convert value to string for key: %s", key)
	}
	return result, nil
}
