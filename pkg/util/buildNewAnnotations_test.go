package util

import (
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestBuildNewAnnotations(t *testing.T) {
	tests := []struct {
		name        string
		oldAnnos    map[string]string
		items       []map[string]string
		wantAnnos   map[string]string
		wantErr     bool
		errContains string
	}{
		// 正常添加新注解
		{
			name:     "Add new annotation",
			oldAnnos: map[string]string{"k1": "v1"},
			items: []map[string]string{
				{"type": "add", "key": "k2", "value": "v2"},
			},
			wantAnnos: map[string]string{"k1": "v1", "k2": "v2"},
		},

		// 更新现有注解
		{
			name:     "Update existing annotation",
			oldAnnos: map[string]string{"k1": "v1"},
			items: []map[string]string{
				{"type": "add", "key": "k1", "value": "new_v1"},
			},
			wantAnnos: map[string]string{"k1": "new_v1"},
		},

		// 删除存在的注解
		{
			name:     "Remove existing annotation",
			oldAnnos: map[string]string{"k1": "v1", "k2": "v2"},
			items: []map[string]string{
				{"type": "remove", "key": "k1"},
			},
			wantAnnos: map[string]string{"k2": "v2"},
		},

		// 删除不存在的注解
		{
			name:     "Remove non-existent annotation",
			oldAnnos: map[string]string{"k1": "v1"},
			items: []map[string]string{
				{"type": "remove", "key": "not_exist"},
			},
			wantAnnos: map[string]string{"k1": "v1"},
		},

		// 空旧注解测试
		{
			name:     "Empty old annotations",
			oldAnnos: map[string]string{},
			items: []map[string]string{
				{"type": "add", "key": "k1", "value": "v1"},
			},
			wantAnnos: map[string]string{"k1": "v1"},
		},

		// 无效参数测试
		{
			name: "Invalid args - missing type",
			items: []map[string]string{
				{"key": "k1", "value": "v1"},
			},
			wantErr:     true,
			errContains: "invalid args",
		},
		{
			name: "Invalid args - missing key",
			items: []map[string]string{
				{"type": "add", "value": "v1"},
			},
			wantErr:     true,
			errContains: "invalid args",
		},
		{
			name: "Invalid args - add without value",
			items: []map[string]string{
				{"type": "add", "key": "k1"},
			},
			wantErr:     true,
			errContains: "invalid args",
		},

		// 非法操作类型测试
		{
			name: "Invalid action type",
			items: []map[string]string{
				{"type": "invalid", "key": "k1", "value": "v1"},
			},
			wantErr:     true,
			errContains: "invalid action type",
		},

		// 带空格的参数处理测试
		{
			name:     "Trim whitespace",
			oldAnnos: map[string]string{"k1": "v1"},
			items: []map[string]string{
				{"type": " add ", "key": "  k2  ", "value": "  v2  "},
			},
			wantAnnos: map[string]string{"k1": "v1", "k2": "v2"},
		},

		// 混合操作测试
		{
			name:     "Multiple operations",
			oldAnnos: map[string]string{"k1": "v1", "k2": "v2"},
			items: []map[string]string{
				{"type": "add", "key": "k1", "value": "new_v1"},
				{"type": "remove", "key": "k2"},
				{"type": "add", "key": "k3", "value": "v3"},
			},
			wantAnnos: map[string]string{"k1": "new_v1", "k3": "v3"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := BuildNewAnnotations(tt.oldAnnos, tt.items)

			if tt.wantErr {
				assert.Error(t, err)
				if tt.errContains != "" {
					assert.Contains(t, err.Error(), tt.errContains)
				}
				return
			}

			assert.NoError(t, err)
			assert.Equal(t, tt.wantAnnos, got)
		})
	}
}
