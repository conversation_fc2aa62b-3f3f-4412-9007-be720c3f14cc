package util

import (
	"reflect"
	"sort"
	"testing"

	corev1 "k8s.io/api/core/v1"
)

func TestAllowUpgrade(t *testing.T) {
	oldArgs := []string{
		"--arg1=val1",
		"--arg2=val2",
		"--arg3=val3=plus1",
		"--arg4",
		"--arg5",
	}
	tests := []struct {
		name           string
		oldArgs        []string
		updateArgItems []map[string]string
		expectedResult []string
		expectedError  bool
	}{
		{
			name:    "通过update添加新args",
			oldArgs: oldArgs,
			updateArgItems: []map[string]string{
				{
					"type":  "update",
					"value": "--arg6=val6",
				},
				{
					"type":  "update",
					"value": "--arg7",
				},
			},
			expectedResult: []string{
				"--arg1=val1",
				"--arg2=val2",
				"--arg3=val3=plus1",
				"--arg4",
				"--arg5",
				"--arg6=val6",
				"--arg7",
			},
			expectedError: false,
		},
		{
			name:    "通过update更新已存在args",
			oldArgs: oldArgs,
			updateArgItems: []map[string]string{
				{
					"type":  "update",
					"value": "--arg1=val5",
				},
				{
					"type":  "update",
					"value": "--arg3=val6",
				},
			},
			expectedResult: []string{
				"--arg1=val5",
				"--arg2=val2",
				"--arg3=val6",
				"--arg4",
				"--arg5",
			},
			expectedError: false,
		},
		{
			name:    "正常更新args",
			oldArgs: oldArgs,
			updateArgItems: []map[string]string{
				{
					"type":  "update",
					"value": "--arg1=val5",
				},
				{
					"type":  "update",
					"value": "--arg3=val6",
				},
			},
			expectedResult: []string{
				"--arg1=val5",
				"--arg2=val2",
				"--arg3=val6",
				"--arg4",
				"--arg5",
			},
			expectedError: false,
		},
		{
			name:    "正常更新args",
			oldArgs: oldArgs,
			updateArgItems: []map[string]string{
				{
					"type":  "update",
					"value": "--arg1=val5",
				},
				{
					"type":  "update",
					"value": "--arg4",
				},
			},
			expectedResult: []string{
				"--arg1=val5",
				"--arg2=val2",
				"--arg3=val3=plus1",
				"--arg4",
				"--arg5",
			},
			expectedError: false,
		},
		{
			name:    "同时新增和修改args",
			oldArgs: oldArgs,
			updateArgItems: []map[string]string{
				{
					"type":  "update",
					"value": "--arg1=val5",
				},
				{
					"type":  "update",
					"value": "--arg6=val6",
				},
				{
					"type":  "update",
					"value": "--arg7",
				},
			},
			expectedResult: []string{
				"--arg1=val5",
				"--arg2=val2",
				"--arg3=val3=plus1",
				"--arg4",
				"--arg5",
				"--arg6=val6",
				"--arg7",
			},
			expectedError: false,
		},
		{
			name:    "禁止更新类型冲突的args",
			oldArgs: oldArgs,
			updateArgItems: []map[string]string{
				{
					"type":  "update",
					"value": "--arg1=val5",
				},
				{
					"type":  "update",
					"value": "--arg4=val6",
				},
			},
			expectedResult: nil,
			expectedError:  true,
		},
		{
			name:    "正常删除args",
			oldArgs: oldArgs,
			updateArgItems: []map[string]string{
				{
					"type":  "remove",
					"value": "--arg1",
				},
				{
					"type":  "remove",
					"value": "--arg4",
				},
			},
			expectedResult: []string{
				"--arg2=val2",
				"--arg3=val3=plus1",
				"--arg5",
			},
			expectedError: false,
		},
		{
			name:    "正常删除args",
			oldArgs: oldArgs,
			updateArgItems: []map[string]string{
				{
					"type":  "remove",
					"value": "--arg1=val1",
				},
				{
					"type":  "remove",
					"value": "--arg3=val3=plus1",
				},
			},
			expectedResult: []string{
				"--arg2=val2",
				"--arg4",
				"--arg5",
			},
			expectedError: false,
		},
		{
			name:    "正常删除args",
			oldArgs: oldArgs,
			updateArgItems: []map[string]string{
				{
					"type":  "remove",
					"value": "--arg1=val2",
				},
				{
					"type":  "remove",
					"value": "--arg3",
				},
			},
			expectedResult: []string{
				"--arg1=val1",
				"--arg2=val2",
				"--arg4",
				"--arg5",
			},
			expectedError: false,
		},
		{
			name:    "删除不存在的args",
			oldArgs: oldArgs,
			updateArgItems: []map[string]string{
				{
					"type":  "remove",
					"value": "--arg1",
				},
				{
					"type":  "remove",
					"value": "--arg10",
				},
			},
			expectedResult: []string{
				"--arg2=val2",
				"--arg3=val3=plus1",
				"--arg4",
				"--arg5",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			newArgs, err := BuildNewArgs(tt.oldArgs, tt.updateArgItems)
			if (tt.expectedError && err == nil) || (!tt.expectedError && err != nil) {
				t.Errorf("BuildNewArgs() error = %v, wantErr %v", err != nil, tt.expectedError)
			} else if !reflect.DeepEqual(newArgs, tt.expectedResult) {
				t.Errorf("BuildNewArgs() result = %v, want %v", newArgs, tt.expectedResult)
			}
		})
	}
}

func TestBuildNewHostAliases(t *testing.T) {
	oldAlias := []corev1.HostAlias{
		{
			IP:        "*******",
			Hostnames: []string{"host1.1", "host1.2"},
		},
		{
			IP:        "*******",
			Hostnames: []string{"host2.1", "host2.2"},
		},
	}
	tests := []struct {
		name             string
		oldAlias         []corev1.HostAlias
		updateAliasItems map[string][]string
		expectedResult   []corev1.HostAlias
		expectedError    bool
	}{
		{
			name:     "正常添加alias",
			oldAlias: oldAlias,
			updateAliasItems: map[string][]string{
				"*******": {
					"host1.3",
				},
			},
			expectedResult: []corev1.HostAlias{
				{
					IP:        "*******",
					Hostnames: []string{"host1.1", "host1.2", "host1.3"},
				},
				{
					IP:        "*******",
					Hostnames: []string{"host2.1", "host2.2"},
				},
			},
			expectedError: false,
		},
		{
			name:     "添加已存在的alias",
			oldAlias: oldAlias,
			updateAliasItems: map[string][]string{
				"*******": {
					"host1.1", "host1.2",
				},
				"*******": {
					"host2.3",
				},
			},
			expectedResult: []corev1.HostAlias{
				{
					IP:        "*******",
					Hostnames: []string{"host1.1", "host1.2"},
				},
				{
					IP:        "*******",
					Hostnames: []string{"host2.1", "host2.2", "host2.3"},
				},
			},
			expectedError: false,
		},
		{
			name:     "添加已新的hostsalias",
			oldAlias: oldAlias,
			updateAliasItems: map[string][]string{
				"*******": {
					"host1.4",
				},
				"*******": {
					"host3.1",
				},
			},
			expectedResult: []corev1.HostAlias{
				{
					IP:        "*******",
					Hostnames: []string{"host1.1", "host1.2", "host1.4"},
				},
				{
					IP:        "*******",
					Hostnames: []string{"host2.1", "host2.2"},
				},
				{
					IP:        "*******",
					Hostnames: []string{"host3.1"},
				},
			},
			expectedError: false,
		},
		{
			name:     "添加已新的hostsalias",
			oldAlias: oldAlias,
			updateAliasItems: map[string][]string{
				"*******": nil,
				"*******": {
					"host3.1",
				},
			},
			expectedResult: []corev1.HostAlias{
				{
					IP:        "*******",
					Hostnames: []string{"host1.1", "host1.2"},
				},
				{
					IP:        "*******",
					Hostnames: []string{"host2.1", "host2.2"},
				},
				{
					IP:        "*******",
					Hostnames: []string{"host3.1"},
				},
			},
			expectedError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			newAlias := BuildNewHostAliases(tt.oldAlias, tt.updateAliasItems)
			sort.Slice(newAlias, func(i, j int) bool {
				return newAlias[i].IP < newAlias[j].IP
			})
			if !reflect.DeepEqual(newAlias, tt.expectedResult) {
				t.Errorf("BuildNewHostAliases() result = %v, want %v", newAlias, tt.expectedResult)
			}
		})
	}
}
