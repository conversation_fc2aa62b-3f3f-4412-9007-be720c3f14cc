package util

import (
	"reflect"
	"testing"

	"git.woa.com/kmetis/starship/pkg/model"
)

func TestBuildNewConfigMapData(t *testing.T) {
	tests := []struct {
		name         string
		originalData map[string]string
		operations   []model.ConfigMapDataItem
		expected     map[string]string
		expectError  bool
	}{
		{
			name: "successful update operation",
			originalData: map[string]string{
				"config.yaml": "old-value",
				"app.conf":    "existing-config",
			},
			operations: []model.ConfigMapDataItem{
				{Type: "update", Key: "config.yaml", Value: "new-value"},
			},
			expected: map[string]string{
				"config.yaml": "new-value",
				"app.conf":    "existing-config",
			},
			expectError: false,
		},
		{
			name: "successful add new key operation",
			originalData: map[string]string{
				"existing.conf": "value1",
			},
			operations: []model.ConfigMapDataItem{
				{Type: "update", Key: "new.conf", Value: "value2"},
			},
			expected: map[string]string{
				"existing.conf": "value1",
				"new.conf":      "value2",
			},
			expectError: false,
		},
		{
			name: "successful remove operation",
			originalData: map[string]string{
				"config.yaml": "value1",
				"app.conf":    "value2",
				"remove.conf": "value3",
			},
			operations: []model.ConfigMapDataItem{
				{Type: "remove", Key: "remove.conf"},
			},
			expected: map[string]string{
				"config.yaml": "value1",
				"app.conf":    "value2",
			},
			expectError: false,
		},
		{
			name: "remove non-existent key should not error",
			originalData: map[string]string{
				"config.yaml": "value1",
			},
			operations: []model.ConfigMapDataItem{
				{Type: "remove", Key: "non-existent.conf"},
			},
			expected: map[string]string{
				"config.yaml": "value1",
			},
			expectError: false,
		},
		{
			name: "multiple operations",
			originalData: map[string]string{
				"config.yaml":  "old-value",
				"app.conf":     "existing-config",
				"remove.conf":  "to-be-removed",
				"update.conf":  "old-update-value",
			},
			operations: []model.ConfigMapDataItem{
				{Type: "update", Key: "config.yaml", Value: "new-value"},
				{Type: "remove", Key: "remove.conf"},
				{Type: "update", Key: "new.conf", Value: "new-config"},
				{Type: "update", Key: "update.conf", Value: "new-update-value"},
			},
			expected: map[string]string{
				"config.yaml": "new-value",
				"app.conf":    "existing-config",
				"new.conf":    "new-config",
				"update.conf": "new-update-value",
			},
			expectError: false,
		},
		{
			name:         "nil original data should work",
			originalData: nil,
			operations: []model.ConfigMapDataItem{
				{Type: "update", Key: "new.conf", Value: "new-value"},
			},
			expected: map[string]string{
				"new.conf": "new-value",
			},
			expectError: false,
		},
		{
			name: "empty original data should work",
			originalData: map[string]string{},
			operations: []model.ConfigMapDataItem{
				{Type: "update", Key: "new.conf", Value: "new-value"},
			},
			expected: map[string]string{
				"new.conf": "new-value",
			},
			expectError: false,
		},
		{
			name: "invalid operation type should error",
			originalData: map[string]string{
				"config.yaml": "value",
			},
			operations: []model.ConfigMapDataItem{
				{Type: "invalid", Key: "config.yaml", Value: "new-value"},
			},
			expected:    nil,
			expectError: true,
		},
		{
			name: "empty key should error",
			originalData: map[string]string{
				"config.yaml": "value",
			},
			operations: []model.ConfigMapDataItem{
				{Type: "update", Key: "", Value: "new-value"},
			},
			expected:    nil,
			expectError: true,
		},
		{
			name: "empty value for update should error",
			originalData: map[string]string{
				"config.yaml": "value",
			},
			operations: []model.ConfigMapDataItem{
				{Type: "update", Key: "config.yaml", Value: ""},
			},
			expected:    nil,
			expectError: true,
		},
		{
			name: "invalid key format should error",
			originalData: map[string]string{
				"config.yaml": "value",
			},
			operations: []model.ConfigMapDataItem{
				{Type: "update", Key: "-invalid-key", Value: "new-value"},
			},
			expected:    nil,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, err := BuildNewConfigMapData(tt.originalData, tt.operations)

			if tt.expectError {
				if err == nil {
					t.Errorf("expected error but got none")
				}
				return
			}

			if err != nil {
				t.Errorf("unexpected error: %v", err)
				return
			}

			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("expected %v, got %v", tt.expected, result)
			}
		})
	}
}

func TestValidateConfigMapData(t *testing.T) {
	tests := []struct {
		name        string
		data        []model.ConfigMapDataItem
		expectError bool
	}{
		{
			name: "valid data with update operation",
			data: []model.ConfigMapDataItem{
				{Type: "update", Key: "config.yaml", Value: "new-value"},
			},
			expectError: false,
		},
		{
			name: "valid data with remove operation",
			data: []model.ConfigMapDataItem{
				{Type: "remove", Key: "config.yaml"},
			},
			expectError: false,
		},
		{
			name: "valid data with multiple operations",
			data: []model.ConfigMapDataItem{
				{Type: "update", Key: "config.yaml", Value: "new-value"},
				{Type: "remove", Key: "old.conf"},
				{Type: "update", Key: "app.properties", Value: "app-config"},
			},
			expectError: false,
		},
		{
			name:        "empty data should error",
			data:        []model.ConfigMapDataItem{},
			expectError: true,
		},
		{
			name: "invalid operation type should error",
			data: []model.ConfigMapDataItem{
				{Type: "invalid", Key: "config.yaml", Value: "new-value"},
			},
			expectError: true,
		},
		{
			name: "empty key should error",
			data: []model.ConfigMapDataItem{
				{Type: "update", Key: "", Value: "new-value"},
			},
			expectError: true,
		},
		{
			name: "empty value for update should error",
			data: []model.ConfigMapDataItem{
				{Type: "update", Key: "config.yaml", Value: ""},
			},
			expectError: true,
		},
		{
			name: "whitespace-only key should error",
			data: []model.ConfigMapDataItem{
				{Type: "update", Key: "   ", Value: "new-value"},
			},
			expectError: true,
		},
		{
			name: "whitespace-only value for update should error",
			data: []model.ConfigMapDataItem{
				{Type: "update", Key: "config.yaml", Value: "   "},
			},
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ValidateConfigMapData(tt.data)

			if tt.expectError {
				if err == nil {
					t.Errorf("expected error but got none")
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
			}
		})
	}
}

func TestValidateConfigMapDataItem(t *testing.T) {
	tests := []struct {
		name        string
		item        model.ConfigMapDataItem
		expectError bool
	}{
		{
			name:        "valid update operation",
			item:        model.ConfigMapDataItem{Type: "update", Key: "config.yaml", Value: "new-value"},
			expectError: false,
		},
		{
			name:        "valid remove operation",
			item:        model.ConfigMapDataItem{Type: "remove", Key: "config.yaml"},
			expectError: false,
		},
		{
			name:        "valid key with dots and hyphens",
			item:        model.ConfigMapDataItem{Type: "update", Key: "app.config-file.yaml", Value: "content"},
			expectError: false,
		},
		{
			name:        "valid key with underscores",
			item:        model.ConfigMapDataItem{Type: "update", Key: "app_config_file", Value: "content"},
			expectError: false,
		},
		{
			name:        "invalid operation type",
			item:        model.ConfigMapDataItem{Type: "invalid", Key: "config.yaml", Value: "new-value"},
			expectError: true,
		},
		{
			name:        "empty key",
			item:        model.ConfigMapDataItem{Type: "update", Key: "", Value: "new-value"},
			expectError: true,
		},
		{
			name:        "whitespace-only key",
			item:        model.ConfigMapDataItem{Type: "update", Key: "   ", Value: "new-value"},
			expectError: true,
		},
		{
			name:        "empty value for update",
			item:        model.ConfigMapDataItem{Type: "update", Key: "config.yaml", Value: ""},
			expectError: true,
		},
		{
			name:        "whitespace-only value for update",
			item:        model.ConfigMapDataItem{Type: "update", Key: "config.yaml", Value: "   "},
			expectError: true,
		},
		{
			name:        "empty value for remove is ok",
			item:        model.ConfigMapDataItem{Type: "remove", Key: "config.yaml", Value: ""},
			expectError: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := validateConfigMapDataItem(tt.item)

			if tt.expectError {
				if err == nil {
					t.Errorf("expected error but got none")
				}
			} else {
				if err != nil {
					t.Errorf("unexpected error: %v", err)
				}
			}
		})
	}
}

func TestIsValidConfigMapKey(t *testing.T) {
	tests := []struct {
		name     string
		key      string
		expected bool
	}{
		// Valid keys
		{
			name:     "simple alphanumeric key",
			key:      "config",
			expected: true,
		},
		{
			name:     "key with dots",
			key:      "app.config",
			expected: true,
		},
		{
			name:     "key with hyphens",
			key:      "app-config",
			expected: true,
		},
		{
			name:     "key with underscores",
			key:      "app_config",
			expected: true,
		},
		{
			name:     "complex valid key",
			key:      "app.config-file_v1.yaml",
			expected: true,
		},
		{
			name:     "key starting and ending with numbers",
			key:      "1config1",
			expected: true,
		},
		{
			name:     "single character key",
			key:      "a",
			expected: true,
		},
		{
			name:     "single number key",
			key:      "1",
			expected: true,
		},
		{
			name:     "key with mixed case",
			key:      "AppConfig",
			expected: true,
		},

		// Invalid keys
		{
			name:     "empty key",
			key:      "",
			expected: false,
		},
		{
			name:     "key starting with hyphen",
			key:      "-config",
			expected: false,
		},
		{
			name:     "key ending with hyphen",
			key:      "config-",
			expected: false,
		},
		{
			name:     "key starting with dot",
			key:      ".config",
			expected: false,
		},
		{
			name:     "key ending with dot",
			key:      "config.",
			expected: false,
		},
		{
			name:     "key starting with underscore",
			key:      "_config",
			expected: false,
		},
		{
			name:     "key ending with underscore",
			key:      "config_",
			expected: false,
		},
		{
			name:     "key with spaces",
			key:      "app config",
			expected: false,
		},
		{
			name:     "key with special characters",
			key:      "app@config",
			expected: false,
		},
		{
			name:     "key with forward slash",
			key:      "app/config",
			expected: false,
		},
		{
			name:     "key with backslash",
			key:      "app\\config",
			expected: false,
		},
		{
			name:     "single hyphen",
			key:      "-",
			expected: false,
		},
		{
			name:     "single dot",
			key:      ".",
			expected: false,
		},
		{
			name:     "single underscore",
			key:      "_",
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := IsValidConfigMapKey(tt.key)
			if result != tt.expected {
				t.Errorf("IsValidConfigMapKey(%q) = %v, expected %v", tt.key, result, tt.expected)
			}
		})
	}
}

func TestIsValidConfigMapKey_LengthLimit(t *testing.T) {
	// Test length limit (253 characters)
	validLongKey := string(make([]byte, 253))
	for i := range validLongKey {
		if i == 0 || i == 252 {
			validLongKey = validLongKey[:i] + "a" + validLongKey[i+1:]
		} else {
			validLongKey = validLongKey[:i] + "b" + validLongKey[i+1:]
		}
	}

	if !IsValidConfigMapKey(validLongKey) {
		t.Errorf("expected 253-character key to be valid")
	}

	// Test key that's too long (254 characters)
	tooLongKey := validLongKey + "a"
	if IsValidConfigMapKey(tooLongKey) {
		t.Errorf("expected 254-character key to be invalid")
	}
}