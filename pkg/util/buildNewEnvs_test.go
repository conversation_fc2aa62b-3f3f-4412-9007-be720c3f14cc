package util

import (
	"fmt"
	"strings"
	"testing"

	corev1 "k8s.io/api/core/v1"
)

func TestBuildNewEnvs(t *testing.T) {
	// 基础测试数据
	baseEnvs := []corev1.EnvVar{
		{Name: "EXISTING_KEY", Value: "original_value"},
		{Name: "TO_REMOVE", Value: "remove_me"},
	}

	tests := []struct {
		name        string
		oldEnvs     []corev1.EnvVar
		addEnvItems []map[string]string
		want        []corev1.EnvVar
		wantErr     bool
	}{
		{
			name:    "添加新环境变量",
			oldEnvs: baseEnvs,
			addEnvItems: []map[string]string{
				{"type": "update", "key": "NEW_KEY", "value": "new_value"},
			},
			want: append(baseEnvs, corev1.EnvVar{Name: "NEW_KEY", Value: "new_value"}),
		},
		{
			name:    "更新现有环境变量",
			oldEnvs: baseEnvs,
			addEnvItems: []map[string]string{
				{"type": "update", "key": "EXISTING_KEY", "value": "updated_value"},
			},
			want: []corev1.EnvVar{
				{Name: "EXISTING_KEY", Value: "updated_value"},
				{Name: "TO_REMOVE", Value: "remove_me"},
			},
		},
		{
			name:    "添加相同值不更新",
			oldEnvs: baseEnvs,
			addEnvItems: []map[string]string{
				{"type": "update", "key": "EXISTING_KEY", "value": "original_value"},
			},
			want: baseEnvs,
		},
		{
			name:    "删除存在的环境变量",
			oldEnvs: baseEnvs,
			addEnvItems: []map[string]string{
				{"type": "remove", "key": "TO_REMOVE"},
			},
			want: []corev1.EnvVar{{Name: "EXISTING_KEY", Value: "original_value"}},
		},
		{
			name:    "删除不存在的环境变量",
			oldEnvs: baseEnvs,
			addEnvItems: []map[string]string{
				{"type": "remove", "key": "NON_EXISTENT"},
			},
			want: baseEnvs,
		},
		{
			name:    "无效操作类型",
			oldEnvs: baseEnvs,
			addEnvItems: []map[string]string{
				{"type": "invalid", "key": "KEY", "value": "value"},
			},
			wantErr: true,
		},
		{
			name:    "参数校验失败-add缺少value",
			oldEnvs: baseEnvs,
			addEnvItems: []map[string]string{
				{"type": "update", "key": "BAD_KEY"},
			},
			wantErr: true,
		},
		{
			name:    "参数校验失败-remove多余value",
			oldEnvs: baseEnvs,
			addEnvItems: []map[string]string{
				{"type": "remove", "key": "TO_REMOVE", "value": "unexpected"},
			},
			want: []corev1.EnvVar{
				{Name: "EXISTING_KEY", Value: "original_value"},
			},
			wantErr: false,
		},
		{
			name:    "混合操作测试",
			oldEnvs: baseEnvs,
			addEnvItems: []map[string]string{
				{"type": "update", "key": "NEW_KEY1", "value": "v1"},
				{"type": "remove", "key": "TO_REMOVE"},
				{"type": "update", "key": "EXISTING_KEY", "value": "new"},
			},
			want: []corev1.EnvVar{
				{Name: "EXISTING_KEY", Value: "new"},
				{Name: "NEW_KEY1", Value: "v1"},
			},
		},
		{
			name:    "空初始环境变量",
			oldEnvs: []corev1.EnvVar{},
			addEnvItems: []map[string]string{
				{"type": "update", "key": "INIT_KEY", "value": "init"},
			},
			want: []corev1.EnvVar{{Name: "INIT_KEY", Value: "init"}},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := BuildNewEnvs(tt.oldEnvs, tt.addEnvItems)
			if (err != nil) != tt.wantErr {
				t.Errorf("BuildNewEnvs() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !envVarsEqual(got, tt.want) {
				t.Errorf("BuildNewEnvs() = %v, want %v", envVarsToString(got), envVarsToString(tt.want))
			}
		})
	}
}

// 辅助函数：比较两个EnvVar数组是否相等
func envVarsEqual(a, b []corev1.EnvVar) bool {
	if len(a) != len(b) {
		return false
	}
	envMap := make(map[string]string)
	for _, env := range a {
		envMap[env.Name] = env.Value
	}
	for _, env := range b {
		if val, ok := envMap[env.Name]; !ok || val != env.Value {
			return false
		}
	}
	return true
}

// 辅助函数：将EnvVar数组转换为可读字符串
func envVarsToString(envs []corev1.EnvVar) string {
	var sb strings.Builder
	sb.WriteString("[")
	for i, e := range envs {
		if i > 0 {
			sb.WriteString(", ")
		}
		sb.WriteString(fmt.Sprintf("%s=%s", e.Name, e.Value))
	}
	sb.WriteString("]")
	return sb.String()
}
