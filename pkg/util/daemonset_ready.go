package util

import (
	"context"
	"fmt"
	"math"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/klog/v2"

	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
)

// CheckDaemonSetReadyWithRetry 检查DaemonSet就绪状态，支持重试
func CheckDaemonSetReadyWithRetry(ctx context.Context, client kubernetes.Interface, namespace, workloadName, traceId string, maxRetries int, name, risk, level string) (*strategyprovider.TaskStrategyReply, error) {
	baseLog := fmt.Sprintf("traceId:%s", traceId)
	rsp := &strategyprovider.TaskStrategyReply{
		HealthCheckName: name,
		RiskName:        risk,
		Level:           level,
	}

	count := 1
	for {
		daemonSet, err := client.AppsV1().DaemonSets(namespace).Get(ctx, workloadName, metav1.GetOptions{})
		if err != nil {
			return HandleError(rsp, "get daemonset failed", err, baseLog)
		}

		// 检查副本状态
		if daemonSet.Status.NumberReady == daemonSet.Status.DesiredNumberScheduled &&
			daemonSet.Status.UpdatedNumberScheduled == daemonSet.Status.DesiredNumberScheduled &&
			daemonSet.Status.ObservedGeneration == daemonSet.Generation {
			klog.Infof("daemonset ready check pass, %s", baseLog)
			rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
			return rsp, nil
		}

		klog.Infof("daemonset not ready, current round:%d, %s", count, baseLog)
		count++
		if count > maxRetries {
			break
		}

		backoff := time.Duration(count*count) * 10 * time.Second
		if backoff > 180*time.Second {
			backoff = 180 * time.Second
		}
		time.Sleep(backoff)
	}

	// 超过最大重试次数，返回失败
	errMsg := fmt.Sprintf("daemonset %s/%s not ready after %d retries", namespace, workloadName, maxRetries)
	solution := fmt.Sprintf("请检查daemonset %s/%s的状态", namespace, workloadName)
	return HandleFailed(rsp, errMsg, solution, baseLog)
}

var RetryAbleErr = fmt.Errorf("retry")
var TimeoutErr = fmt.Errorf("timeout")

func RetryUntilTimeout(ctx context.Context, interval time.Duration, timeout time.Duration, do func() error) error {
	err := do()
	if err == nil {
		return nil
	}

	if err != RetryAbleErr {
		return err
	}

	if timeout == 0 {
		timeout = time.Duration(math.MaxInt64)
	}

	t := time.NewTimer(timeout)
	for {
		select {
		case <-ctx.Done():
			return ctx.Err()
		case <-t.C:
			return TimeoutErr
		case <-time.After(interval):
			err := do()
			if err == nil {
				return nil
			}

			if err != RetryAbleErr {
				return err
			}
		}
	}
}
