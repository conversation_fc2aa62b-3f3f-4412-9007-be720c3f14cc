package util

import (
	"fmt"
	"os"

	"git.woa.com/tencentcloud.hrt/dencrypt/enigmarotor/enigmarotor/decryptrotor"
)

// Decrypt decrypts the string
func Decrypt(s string) (string, error) {
	key := os.Getenv("ENIGMA_KEYS_PATH")
	if key == "" {
		return "", fmt.Errorf("ENIGMA_KEYS_PATH is empty")
	}
	rotor, err := decryptrotor.NewEnigmaDecryptRotorFromConfigPath(key)
	if err != nil {
		return "", err
	}
	// 解密
	decrypted, err := rotor.DecryptString(s)
	if err != nil {
		return "", err
	}
	return decrypted, nil
}
