package util

import (
	"context"
	"fmt"
	"time"

	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/klog/v2"

	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
)

// CheckDeploymentReadyWithRetry 检查deployment就绪状态，支持重试
func CheckDeploymentReadyWithRetry(ctx context.Context, client kubernetes.Interface, namespace, workloadName, traceId string, maxRetries int, name, risk, level string) (*strategyprovider.TaskStrategyReply, error) {
	baseLog := fmt.Sprintf("traceId:%s", traceId)
	rsp := &strategyprovider.TaskStrategyReply{
		HealthCheckName: name,
		RiskName:        risk,
		Level:           level,
	}

	count := 1
	for {
		deployment, err := client.AppsV1().Deployments(namespace).Get(ctx, workloadName, metav1.GetOptions{})
		if err != nil {
			return HandleError(rsp, "get deployment failed", err, baseLog)
		}

		// 检查副本状态
		if deployment.Status.ReadyReplicas == deployment.Status.Replicas &&
			deployment.Status.UpdatedReplicas == deployment.Status.Replicas &&
			deployment.Status.ObservedGeneration == deployment.Generation {
			klog.Infof("deployment ready check pass, %s", baseLog)
			rsp.Code = healthcheck.HEALTHCHECK_CODE_PASS
			return rsp, nil
		}

		klog.Infof("deployment not ready, current round:%d, %s", count, baseLog)
		count++
		if count > maxRetries {
			break
		}

		backoff := time.Duration(count*count) * 6 * time.Second
		if backoff > 75*time.Second {
			backoff = 75 * time.Second
		}
		time.Sleep(backoff)
	}

	// 超过最大重试次数，返回失败
	errMsg := fmt.Sprintf("deployment %s/%s not ready after %d retries", namespace, workloadName, maxRetries)
	solution := fmt.Sprintf("请检查deployment %s/%s的状态", namespace, workloadName)
	return HandleFailed(rsp, errMsg, solution, baseLog)
}
