package util

import (
	"fmt"

	"git.woa.com/kmetis/starship/pkg/healthcheck"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"

	"k8s.io/klog/v2"
)

// HandleError 处理错误情况
func HandleError(rsp *strategyprovider.TaskStrategyReply, msgPrefix string, err error, baseLog string) (*strategyprovider.TaskStrategyReply, error) {
	if err != nil {
		msgPrefix = fmt.Sprintf("%s, err:%s", msgPrefix, err.Error())
	}
	klog.Errorf("%s, %s", msgPrefix, baseLog)
	rsp.Code = healthcheck.HEALTHCHECK_CODE_ERROR
	rsp.Detail = msgPrefix
	return rsp, nil
}

// HandleFailed 处理失败情况
func HandleFailed(rsp *strategyprovider.TaskStrategyReply, detail string, solution, baseLog string) (*strategyprovider.TaskStrategyReply, error) {
	klog.Errorf("%s, %s", detail, baseLog)
	rsp.Code = healthcheck.HEALTHCHECK_CODE_FAILED
	rsp.Detail = detail
	rsp.Solution = solution
	return rsp, nil
}
