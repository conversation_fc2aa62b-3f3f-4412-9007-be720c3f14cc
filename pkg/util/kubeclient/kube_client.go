package kubeclient

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"io/ioutil"
	"net/http"
	"os"
	"strconv"
	_ "strings"
	"sync"

	"github.com/google/uuid"
	apierrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/klog/v2"

	masterclientset "cloud.tencent.com/tke/master-operator/pkg/client/clientset/versioned"
	"git.woa.com/kmetis/starship/pkg/clientset/application"
	"git.woa.com/kmetis/starship/pkg/clientset/platform"
	configpkg "git.woa.com/kmetis/starship/pkg/config"
	"git.woa.com/kmetis/starship/pkg/metrics"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	"git.woa.com/kmetis/starship/pkg/util"
	"tkestack.io/tke/api/client/clientset/versioned"
	platformapiv1 "tkestack.io/tke/api/platform/v1"
)

// dashboard接口中Action字段的值
const (
	describeClusterSecurityReqAction      = "DescribeClusterSecurity"
	describeEKSClusterCredentialReqAction = "DescribeEKSClusterCredential"

	eksAnnotationProductName = "eks.tke.cloud.tencent.com/product-name"
	tkeAnnotationOwnerUin    = "tke.cloud.tencent.com/ownerUin"
	eksAnnotationOwnerUin    = "eks.tke.cloud.tencent.com/owner-uin"

	AccountURL = "http://account.tencentyun.com:50001"
)

var (
	once    sync.Once
	manager *KubeClientManager
)

type RequestV3Common struct {
	RequestID     string `json:"RequestId"`
	Action        string `json:"Action"`
	AppID         uint64 `json:"AppId,omitempty"`
	UIN           string `json:"Uin,omitempty"`
	SubAccountUIN string `json:"SubAccountUin,omitempty"`
	ClientIP      string `json:"ClientIp,omitempty"`
	APIModule     string `json:"ApiModule,omitempty"`
	Region        string `json:"Region,omitempty"`
	Token         string `json:"Token,omitempty"`
	CamContext    string `json:"CamContext,omitempty"`
	Version       string `json:"Version,omitempty"`
	RequestSource string `json:"RequestSource,omitempty"`
	Language      string `json:"Language,omitempty"`
	RequestClient string `json:"RequestClient,omitempty"`
}

type DescribeClusterSecurityReq struct {
	RequestV3Common
	ClusterId           string `json:"ClusterId"`
	JnsGwEndpointEnable bool   `json:"JnsGwEndpointEnable"`
}

type DescribeClusterSecurityResp struct {
	Response DescribeClusterSecurityRespImpl `json:"Response"`
}

type DescribeClusterSecurityRespImpl struct {
	RequestId               string   `json:"RequestId"`
	UserName                string   `json:"UserName"`
	Password                string   `json:"Password"`
	CertificationAuthority  string   `json:"CertificationAuthority"`
	ClusterExternalEndpoint string   `json:"ClusterExternalEndpoint"`
	PgwEndpoint             string   `json:"PgwEndpoint"`
	Domain                  string   `json:"Domain"`
	SecurityPolicy          []string `json:"SecurityPolicy"`
	Kubeconfig              string   `json:"Kubeconfig"`
	JnsGwEndpoint           string   `json:"JnsGwEndpoint"`
	VpcLbEndpoint           string   `json:"VpcLbEndpoint"`
}

type credential struct {
	CACert string `json:"CACert"`
	Token  string `json:"Token"`
}

type ipAddress struct {
	Type string `json:"Type"`
	Ip   string `json:"Ip"`
	Port int    `json:"Port"`
}

type DescribeEksClusterCredentialReq struct {
	RequestV3Common
	ClusterID        string `json:"ClusterId"`
	ProductName      string `json:"ProductName,omitempty"`
	JnsGatewayEnable bool   `json:"JnsGatewayEnable,omitempty"`
	VpcLbEnable      bool   `json:"VpcLbEnable,omitempty"`
}

type DescribeEksClusterCredentialRespImpl struct {
	RequestId  string      `json:"RequestId"`
	Addresses  []ipAddress `json:"Addresses"`
	Credential credential  `json:"Credential"`
	Kubeconfig string      `json:"Kubeconfig"`
}

type DescribeEksClusterCredentialResp struct {
	Response DescribeEksClusterCredentialRespImpl `json:"Response"`
}

type CommonRequest struct {
	Header
	Interface struct {
		Interface string      `json:"interfaceName"`
		Param     interface{} `json:"para"`
	} `json:"interface"`
}

type Header struct {
	Version       int    `json:"version"`
	ComponentName string `json:"componentName"`
	EventId       int64  `json:"eventId"`
}

type CommonResponse struct {
	Header
	Code int         `json:"returnCode"`
	Msg  string      `json:"returnMessage"`
	Data interface{} `json:"data"`
}

type GetUserAttrRequest struct {
	AppIdArr []uint64 `json:"appIdArr"`
}

type GetUserAttrResponse struct {
	AppIdMap map[string]string `json:"appIdMap"`
}

type KubeConfigInfo struct {
	KubeConfig    string       `json:"KubeConfig"`
	Token         string       `json:"Token"`
	JnsGwEndpoint string       `json:"JnsGwEndpoint"`
	ClusterInfo   *ClusterInfo `json:"ClusterInfo"`
	restConfig    *rest.Config
}

type ClusterInfo struct {
	ClusterId   string `json:"ClusterId"`
	AppId       uint64 `json:"AppId"`
	Uin         string `json:"Uin"`
	ProductName string `json:"ProductName"`
	ClusterType string `json:"ClusterType"`
}

type KubeClientManager struct {
	// 用于存储 kubeconfigInfo 信息的缓存
	cache map[string]*KubeConfigInfo
	// 用于存储appId与uin信息缓存
	uinCache map[uint64]string
	mux      sync.RWMutex
}

func NewKubeClientManager() *KubeClientManager {
	return &KubeClientManager{
		cache:    make(map[string]*KubeConfigInfo),
		uinCache: make(map[uint64]string),
	}
}

func updateEksClusterKubeConfigInfo(clusterInfo *ClusterInfo) error {
	requestRource := "API"
	if clusterInfo.ProductName != "" {
		requestRource = "starship"
	}

	req := DescribeEksClusterCredentialReq{
		RequestV3Common: RequestV3Common{
			RequestID:     uuid.NewString(),
			Action:        describeEKSClusterCredentialReqAction,
			AppID:         clusterInfo.AppId,
			UIN:           clusterInfo.Uin,
			SubAccountUIN: clusterInfo.Uin,
			RequestSource: requestRource,
			APIModule:     "tke",
			Language:      "zh-CN",
			Version:       "2018-05-25",
			RequestClient: "SDK_GO_1.0.1074",
			Region:        os.Getenv("LONG_REGION"),
		},
		ClusterID:        clusterInfo.ClusterId,
		ProductName:      clusterInfo.ProductName,
		JnsGatewayEnable: true,
	}
	body, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("marshal request body failed: %v", err)
	}
	tkeCloudGwUrl := configpkg.GetClientConfig().TkeCloudGWServer
	resp, err := http.Post(tkeCloudGwUrl, "application/json", bytes.NewReader(body))
	if err != nil {
		return fmt.Errorf("request tke-cloud-gw failed: %v", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		io.Copy(io.Discard, resp.Body)
		return fmt.Errorf("request tke-cloud-gw failed: %v", resp.Status)
	}
	var respBody []byte
	respBody, err = ioutil.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("read response body failed: %v", err)
	}
	var describeEksClusterCredentialResp DescribeEksClusterCredentialResp
	if err = json.Unmarshal(respBody, &describeEksClusterCredentialResp); err != nil {
		return fmt.Errorf("unmarshal response body failed: %v", err)
	}

	var endpoint string
	for _, item := range describeEksClusterCredentialResp.Response.Addresses {
		if item.Type == "Internal" {
			endpoint = fmt.Sprintf("%s:%d", item.Ip, item.Port)
			break
		}
	}
	if endpoint == "" {
		return fmt.Errorf("failed to get cluster credential endpoint")
	}
	if err = setClusterRestConfig(clusterInfo, describeEksClusterCredentialResp.Response.Credential.Token, endpoint); err != nil {
		return err
	}
	return nil
}

func updateTkeClusterKubeConfigInfo(clusterInfo *ClusterInfo) error {
	req := DescribeClusterSecurityReq{
		RequestV3Common: RequestV3Common{
			RequestID:     uuid.NewString(),
			Action:        describeClusterSecurityReqAction,
			AppID:         clusterInfo.AppId,
			UIN:           clusterInfo.Uin,
			SubAccountUIN: clusterInfo.Uin,
			RequestSource: "API",
			APIModule:     "tke",
			Language:      "zh-CN",
			Version:       "2018-05-25",
			RequestClient: "SDK_GO_1.0.1074",
			Region:        os.Getenv("LONG_REGION"),
		},
		ClusterId:           clusterInfo.ClusterId,
		JnsGwEndpointEnable: true,
	}
	body, err := json.Marshal(req)
	if err != nil {
		return fmt.Errorf("marshal request body failed: %v", err)
	}
	dashboardUrl := configpkg.GetClientConfig().DashboardServer
	resp, err := http.Post(dashboardUrl, "application/json", bytes.NewReader(body))
	if err != nil {
		return fmt.Errorf("request dashboard failed: %v", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		io.Copy(io.Discard, resp.Body)
		return fmt.Errorf("request dashboard failed: %v", resp.Status)
	}
	var respBody []byte
	respBody, err = ioutil.ReadAll(resp.Body)
	if err != nil {
		return fmt.Errorf("read response body failed: %v", err)
	}
	var describeClusterSecurityResp DescribeClusterSecurityResp
	if err = json.Unmarshal(respBody, &describeClusterSecurityResp); err != nil {
		return fmt.Errorf("unmarshal response body failed: %v", err)
	}

	if err = setClusterRestConfig(clusterInfo, describeClusterSecurityResp.Response.Password, describeClusterSecurityResp.Response.JnsGwEndpoint); err != nil {
		return err
	}
	return nil
}

func updateClusterKubeConfigInfo(clusterInfo *ClusterInfo) error {
	if clusterInfo.ClusterType == "tke" {
		return updateTkeClusterKubeConfigInfo(clusterInfo)
	} else if clusterInfo.ClusterType == "eks" {
		return updateEksClusterKubeConfigInfo(clusterInfo)
	} else {
		return fmt.Errorf("cluster type is invalid: %s", clusterInfo.ClusterType)
	}
}

func setClusterRestConfig(clusterInfo *ClusterInfo, token, endpoint string) error {
	if token == "" || endpoint == "" {
		return fmt.Errorf("cluster security token or jnsgw endpoint is empty")
	}

	restConfig := &rest.Config{
		Host:        fmt.Sprintf("https://%s", endpoint),
		BearerToken: token,
		TLSClientConfig: rest.TLSClientConfig{
			Insecure: true,
		},
		QPS:   50,
		Burst: 100,
	}

	manager.mux.Lock()
	defer manager.mux.Unlock()
	manager.cache[clusterInfo.ClusterId] = &KubeConfigInfo{
		ClusterInfo:   clusterInfo,
		Token:         token,
		JnsGwEndpoint: endpoint,
		restConfig:    restConfig,
	}
	return nil
}

func getKubeConfigInfoFromCache(clusterId string) (*KubeConfigInfo, error) {
	manager.mux.RLock()
	defer manager.mux.RUnlock()
	kubeConfigInfo, ok := manager.cache[clusterId]
	if !ok || kubeConfigInfo == nil {
		return nil, fmt.Errorf("cluster %s kubeconfigInfo not find", clusterId)
	}
	return kubeConfigInfo, nil
}

func GetRestConfig(clusterId, productName string) (*rest.Config, error) {
	kubeConfigInfo, err := getKubeConfigInfoFromCache(clusterId)
	if err != nil || !isReachable(kubeConfigInfo.restConfig) {
		var clusterInfo *ClusterInfo
		if kubeConfigInfo != nil {
			clusterInfo = kubeConfigInfo.ClusterInfo
		} else {
			if clusterInfo, err = getClusterInfo(clusterId, productName); err != nil {
				return nil, fmt.Errorf("get cluster info failed: %v", err)
			}
		}
		if err = updateClusterKubeConfigInfo(clusterInfo); err != nil {
			return nil, fmt.Errorf("update resetConfig failed: %v", err)
		}
		kubeConfigInfo, _ = getKubeConfigInfoFromCache(clusterId)
	}
	return kubeConfigInfo.restConfig, nil
}

func GetK8sClient(clusterId, token, user, productName string) (kubernetes.Interface, error) {
	// 兼容ianvs token，若token不为空则使用token获取k8s client
	if token != "" {
		return util.K8sClient(clusterId, token, user)
	}
	restConfig, err := GetRestConfig(clusterId, productName)
	if err != nil {
		return nil, err
	}
	client, err := kubernetes.NewForConfig(restConfig)
	if err != nil {
		return nil, fmt.Errorf("new k8s client failed: %v", err)
	}
	return client, nil
}

func getClusterInfo(clusterId, productName string) (*ClusterInfo, error) {
	var cluster *platformapiv1.Cluster
	var err error
	if productName == "tke" {
		cluster, err = platform.GetClientSet().Client.PlatformV1().Clusters().Get(context.TODO(), clusterId, metav1.GetOptions{})
	} else if productName == "eks" {
		cluster, err = platform.GetClientSet().EksClient.PlatformV1().Clusters().Get(context.TODO(), clusterId, metav1.GetOptions{})
	} else {
		return nil, fmt.Errorf("productName is invalid: %s", productName)
	}
	if err != nil {
		metrics.ClusterInfoFetchFailedTotal.WithLabelValues().Inc()
		return nil, fmt.Errorf("failed to get cluster:%v", err)
	}

	var appid uint64
	if appid, err = strconv.ParseUint(cluster.Spec.TenantID, 10, 64); err != nil {
		return nil, fmt.Errorf("failed to parse appid from cluster:%v", err)
	}

	var uin string
	if productName == "tke" {
		uin = cluster.Annotations[tkeAnnotationOwnerUin]
	} else if productName == "eks" {
		uin = cluster.Annotations[eksAnnotationOwnerUin]
	}
	if uin == "" {
		klog.Info("failed to get uin from cluster, try to get from account")
		uin, err = getUinFromAppId(appid)
		if err != nil {
			return nil, fmt.Errorf("failed to get uin from account:%v", err)
		}
	}
	clsProductName := cluster.Annotations[eksAnnotationProductName]
	return &ClusterInfo{
		ClusterId:   clusterId,
		AppId:       appid,
		Uin:         uin,
		ProductName: clsProductName,
		ClusterType: productName,
	}, nil
}

func isReachable(restConfig *rest.Config) bool {
	if restConfig == nil {
		return false
	}
	client, err := kubernetes.NewForConfig(restConfig)
	if err != nil {
		return false
	}
	_, err = client.ServerVersion()
	return err == nil
}

func getUinFromAppId(appId uint64) (string, error) {
	// get uin from cache
	manager.mux.RLock()
	uin, ok := manager.uinCache[appId]
	manager.mux.RUnlock()
	if ok && uin != "" {
		return uin, nil
	}

	request := &CommonRequest{}
	request.Interface.Interface = "account.Qcauth.getUserIdAttr"
	request.Interface.Param = GetUserAttrRequest{
		AppIdArr: []uint64{appId},
	}
	body, err := json.Marshal(request)
	if err != nil {
		return "", fmt.Errorf("marshal request body failed: %v", err)
	}
	resp, err := http.Post(AccountURL, "application/json", bytes.NewReader(body))
	if err != nil {
		return "", fmt.Errorf("request account failed: %v", err)
	}
	defer resp.Body.Close()
	if resp.StatusCode != http.StatusOK {
		io.Copy(io.Discard, resp.Body)
		return "", fmt.Errorf("request dashboard failed: %v", resp.Status)
	}
	var respBody []byte
	respBody, err = ioutil.ReadAll(resp.Body)
	if err != nil {
		return "", fmt.Errorf("read response body failed: %v", err)
	}
	data := &GetUserAttrResponse{}
	commonResponse := &CommonResponse{
		Data: data,
	}
	if err = json.Unmarshal(respBody, &commonResponse); err != nil {
		return "", fmt.Errorf("unmarshal response body failed: %v", err)
	}

	uin, exist := data.AppIdMap[fmt.Sprintf("%d", appId)]
	if !exist {
		return "", fmt.Errorf("uin not found")
	}
	manager.mux.Lock()
	defer manager.mux.Unlock()
	manager.uinCache[appId] = uin
	return uin, nil
}

// 这里不考虑ianvs token场景，只考虑由tke接口获取token的场景
func GetClusterClientInfo(request *strategyprovider.TaskStrategyRequest) (string, string, error) {
	var clusterId string
	productName := request.ProductName
	if request.Location == "user" {
		clusterId = request.ClusterId
	} else if request.Location == "meta" {
		clusterId = request.MetaClusterId
		// eks集群的meta集群是tke集群，因此需要将productName转换为tke
		if request.ProductName == "eks" {
			productName = "tke"
		}
	}

	kubeConfigInfo, err := getKubeConfigInfoFromCache(clusterId)
	if err != nil || !isReachable(kubeConfigInfo.restConfig) {
		var clusterInfo *ClusterInfo
		if kubeConfigInfo != nil {
			clusterInfo = kubeConfigInfo.ClusterInfo
		} else {
			if clusterInfo, err = getClusterInfo(clusterId, productName); err != nil {
				return "", "", fmt.Errorf("get cluster info failed: %v", err)
			}
		}
		if err = updateClusterKubeConfigInfo(clusterInfo); err != nil {
			return "", "", fmt.Errorf("update resetConfig failed: %v", err)
		}
		kubeConfigInfo, _ = getKubeConfigInfoFromCache(clusterId)
	}

	// 返回集群的addr地址和token即可
	return kubeConfigInfo.restConfig.Host, kubeConfigInfo.restConfig.BearerToken, nil
}

func GetTargetK8sClient(request *strategyprovider.TaskStrategyRequest) (kubernetes.Interface, error) {
	// 兼容ianvs token，若token不为空则使用token获取k8s client
	if request.IanvsToken != "" {
		return util.GetTargetK8sClient(request)
	}
	var clusterId string
	productName := request.ProductName
	if request.Location == "user" {
		clusterId = request.ClusterId
	} else if request.Location == "meta" {
		clusterId = request.MetaClusterId
		// eks集群的meta集群是tke集群，因此需要将productName转换为tke
		if request.ProductName == "eks" {
			productName = "tke"
		}
	}
	return GetK8sClient(clusterId, request.IanvsToken, request.User, productName)
}

func GetTargetMetaClient(request *strategyprovider.TaskStrategyRequest) (masterclientset.Interface, error) {
	// 兼容ianvs token，若token不为空则使用token获取k8s client
	if request.IanvsToken != "" {
		return util.GetTargetMetaClient(request)
	}
	productName := request.ProductName
	// eks集群的meta集群是tke集群，因此需要将productName转换为tke
	if request.ProductName == "eks" {
		productName = "tke"
	}
	restConfig, err := GetRestConfig(request.MetaClusterId, productName)
	if err != nil {
		return nil, err
	}
	return masterclientset.NewForConfig(restConfig)
}

func GetPlatformClient(productName string) (client *versioned.Clientset, err error) {
	switch productName {
	case util.ProductTKE:
		client = platform.GetClientSet().Client
	case util.ProductEKS:
		client = platform.GetClientSet().EksClient
	default:
		return nil, fmt.Errorf("unknown product %s", productName)
	}
	return client, nil
}

func GetApplicationClient(productName string) (client *versioned.Clientset, err error) {
	switch productName {
	case util.ProductTKE:
		client = application.GetClientSet().Client
	case util.ProductEKS:
		client = application.GetClientSet().EksClient
	default:
		return nil, fmt.Errorf("unknown product %s", productName)
	}
	return client, nil
}

func init() {
	once.Do(func() {
		manager = NewKubeClientManager()
	})
}

func GetClusterClientByToken(endpoint, token, username string) (kubernetes.Interface, error) {
	if token == "" {
		return nil, fmt.Errorf("cluster security token is emtpy")
	}
	if endpoint == "" {
		return nil, fmt.Errorf("cluster endpoint is empty")
	}

	restConfig := &rest.Config{
		Host:        fmt.Sprintf("https://%s", endpoint),
		UserAgent:   fmt.Sprintf("starship"),
		BearerToken: token,
		TLSClientConfig: rest.TLSClientConfig{
			Insecure: true,
		},
		QPS:   50,
		Burst: 100,
	}

	return kubernetes.NewForConfig(restConfig)
}

// IsClusterExist Returns true if the cluster is found, indicating it exists.
func IsClusterExist(clusterId, productName string) (bool, error) {
	client, err := GetPlatformClient(productName)
	if err != nil {
		return false, fmt.Errorf("failed to get platform client: %v", err)
	}

	_, err = client.PlatformV1().Clusters().Get(context.TODO(), clusterId, metav1.GetOptions{})
	if err != nil {
		if apierrors.IsNotFound(err) {
			return false, nil
		}
		return false, fmt.Errorf("failed to get cluster: %v", err)
	}
	return true, nil
}

func GetK8sClientWithUserAgent(request *strategyprovider.TaskStrategyRequest, userAgent string) (kubernetes.Interface, error) {
	// 不支持ianvs token
	var clusterId string
	productName := request.ProductName
	if request.Location == "user" {
		clusterId = request.ClusterId
	} else if request.Location == "meta" {
		clusterId = request.MetaClusterId
		// eks集群的meta集群是tke集群，因此需要将productName转换为tke
		if request.ProductName == "eks" {
			productName = "tke"
		}
	}
	restConfig, err := GetRestConfig(clusterId, productName)
	if err != nil {
		return nil, err
	}
	restConfig.UserAgent = userAgent
	client, err := kubernetes.NewForConfig(restConfig)
	if err != nil {
		return nil, fmt.Errorf("new k8s client failed: %v", err)
	}
	return client, nil
}
