package util

import (
	"fmt"
	"strings"

	corev1 "k8s.io/api/core/v1"

	"git.woa.com/kmetis/starship/pkg/model"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
)

// GetContainerIndexes 获取需要更新的容器索引列表
func GetContainerIndexes(podSpec *corev1.PodSpec, extend *model.ExtendInfo) ([]int, error) {
	if len(podSpec.Containers) == 1 {
		return []int{0}, nil
	}

	// 多容器场景下必须指定containerName
	if extend == nil || extend.ContainerName == "" {
		return nil, fmt.Errorf("k8s native workload: pls set containerName to support multi containers")
	}

	containerNames := strings.Split(extend.ContainerName, ",")

	// 创建容器名到索引的映射
	containerIndexMap := make(map[string]int, len(podSpec.Containers))
	for i, container := range podSpec.Containers {
		containerIndexMap[container.Name] = i
	}

	// 获取指定容器的索引
	indexes := make([]int, 0, len(containerNames))
	for _, name := range containerNames {
		if index, exists := containerIndexMap[name]; exists {
			indexes = append(indexes, index)
		} else {
			return nil, fmt.Errorf("container '%s' not found in workload", name)
		}
	}

	return indexes, nil
}

// UpdatePodTemplate 更新Pod模板的配置
func UpdatePodTemplate(podTemplate *corev1.PodTemplateSpec, request *strategyprovider.TaskStrategyRequest) error {
	containerIndexes, err := GetContainerIndexes(&podTemplate.Spec, request.Extend)
	if err != nil {
		return fmt.Errorf("failed to get container indexes, cluster %s, component %s, workload name %s, err is %v",
			request.ClusterId, request.Component, request.WorkloadName, err)
	}
	// 处理Pod级别的配置
	if request.Extend != nil {
		if len(request.Extend.HostAlias) > 0 {
			newHostAliases := BuildNewHostAliases(podTemplate.Spec.HostAliases, request.Extend.HostAlias)
			podTemplate.Spec.HostAliases = newHostAliases
		}
		if len(request.Extend.Annotations) > 0 {
			var err error
			var newAnnotations map[string]string
			newAnnotations, err = BuildNewAnnotations(podTemplate.Annotations, request.Extend.Annotations)
			if err != nil {
				return fmt.Errorf("failed to build new annotations: %v", err)
			}
			podTemplate.Annotations = newAnnotations
		}
	}

	// 处理容器级别的配置
	for _, index := range containerIndexes {
		targetImage, err := GetTargetImage(podTemplate.Spec.Containers[index].Image, request.ImageTag, request.Extend)
		if err != nil {
			return fmt.Errorf("failed to get target image for container %s: %v",
				podTemplate.Spec.Containers[index].Name, err)
		}
		podTemplate.Spec.Containers[index].Image = targetImage

		if request.Extend != nil {
			if len(request.Extend.Args) > 0 {
				var newArgs []string
				newArgs, err = BuildNewArgs(podTemplate.Spec.Containers[index].Args, request.Extend.Args)
				if err != nil {
					return fmt.Errorf("failed to build new args: %v", err)
				}
				podTemplate.Spec.Containers[index].Args = newArgs
			}
			if len(request.Extend.Envs) > 0 {
				var newEnv []corev1.EnvVar
				newEnv, err = BuildNewEnvs(podTemplate.Spec.Containers[index].Env, request.Extend.Envs)
				if err != nil {
					return fmt.Errorf("failed to build new env: %v", err)
				}
				podTemplate.Spec.Containers[index].Env = newEnv
			}
		}
	}

	return nil
}
