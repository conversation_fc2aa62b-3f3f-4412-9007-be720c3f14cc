package util

import (
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"time"

	"github.com/spf13/viper"
)

// PrometheusQueryResult Prometheus查询结果的结构
type PrometheusQueryResult struct {
	Status string `json:"status"`
	Data   struct {
		ResultType string `json:"resultType"`
		Result     []struct {
			Metric map[string]string `json:"metric"`
			Value  []any             `json:"value"` // [timestamp, value]
		} `json:"result"`
	} `json:"data"`
}

// QueryPrometheus 执行Prometheus查询
func QueryPrometheus(prometheusURL, query string) (*PrometheusQueryResult, error) {
	// 构建查询URL
	queryURL, err := BuildPrometheusQueryURL(prometheusURL, query)
	if err != nil {
		return nil, err
	}

	// 执行HTTP请求
	client := http.Client{Timeout: 10 * time.Second}
	resp, err := client.Get(queryURL)
	if err != nil {
		return nil, fmt.Errorf("failed to query prometheus: %v", err)
	}
	defer resp.Body.Close()

	// 解析响应
	var result PrometheusQueryResult
	if err := json.NewDecoder(resp.Body).Decode(&result); err != nil {
		return nil, fmt.Errorf("failed to decode prometheus response: %v", err)
	}

	// 检查响应状态
	if result.Status != "success" {
		return nil, fmt.Errorf("prometheus query failed with status: %s", result.Status)
	}

	return &result, nil
}

// QueryPrometheusByDatasource 根据datasource执行Prometheus查询
// datasource: 配置文件中的datasource字段，如 "global-prometheus"
// query: Prometheus查询语句
func QueryPrometheusByDatasource(datasource, query string) (*PrometheusQueryResult, error) {
	// 根据datasource和region获取Prometheus URL
	prometheusURL, err := GetServiceURLByDatasource(datasource)
	if err != nil {
		return nil, fmt.Errorf("failed to get prometheus URL from datasource %s: %v", datasource, err)
	}

	// 执行查询
	return QueryPrometheus(prometheusURL, query)
}

// ParsePrometheusQueryConfigs 解析Prometheus查询配置
func ParsePrometheusQueryConfigs(prometheusConfig any) ([]PrometheusQueryConfig, error) {
	// 检查是否是数组格式
	queriesSlice, ok := prometheusConfig.([]any)
	if !ok {
		return nil, fmt.Errorf("invalid prometheus config format: expected array")
	}

	var configs []PrometheusQueryConfig
	for i, queryInterface := range queriesSlice {
		config, err := parseQueryConfig(queryInterface, i)
		if err != nil {
			return nil, err
		}
		configs = append(configs, config)
	}

	return configs, nil
}

// PrometheusQueryConfig 单个查询配置
type PrometheusQueryConfig struct {
	Name       string  `json:"name"`
	Query      string  `json:"query"`
	Threshold  float64 `json:"threshold"`
	Operator   string  `json:"operator"`
	Datasource string  `json:"datasource"`
}

// parseQueryConfig 解析单个查询配置
func parseQueryConfig(queryInterface any, index int) (PrometheusQueryConfig, error) {
	queryMap, ok := queryInterface.(map[string]any)
	if !ok {
		return PrometheusQueryConfig{}, fmt.Errorf("query %d must be an object", index)
	}

	// 解析name字段
	name, _ := GetTypedConfigValue[string](queryMap, "name")
	if name == "" {
		name = fmt.Sprintf("Query_%d", index+1)
	}

	// 解析必需字段
	query, ok := GetTypedConfigValue[string](queryMap, "query")
	if !ok || query == "" {
		return PrometheusQueryConfig{}, fmt.Errorf("query %d: prometheus query is not configured or invalid", index)
	}

	threshold, ok := GetTypedConfigValue[float64](queryMap, "threshold")
	if !ok {
		return PrometheusQueryConfig{}, fmt.Errorf("query %d: prometheus threshold is not configured or invalid", index)
	}

	operator, ok := GetTypedConfigValue[string](queryMap, "operator")
	if !ok || operator == "" {
		return PrometheusQueryConfig{}, fmt.Errorf("query %d: prometheus operator is not configured or invalid", index)
	}

	// 解析可选字段
	datasource, _ := GetTypedConfigValue[string](queryMap, "datasource")

	return PrometheusQueryConfig{
		Name:       name,
		Query:      query,
		Threshold:  threshold,
		Operator:   operator,
		Datasource: datasource,
	}, nil
}

// BuildPrometheusQueryURL 构建Prometheus查询URL
func BuildPrometheusQueryURL(baseURL, query string) (string, error) {
	apiURL := fmt.Sprintf("%s/api/v1/query", baseURL)
	u, err := url.Parse(apiURL)
	if err != nil {
		return "", fmt.Errorf("failed to parse prometheus URL: %v", err)
	}

	// 添加查询参数
	q := u.Query()
	q.Set("query", query)
	u.RawQuery = q.Encode()

	return u.String(), nil
}

// GetServiceURLByDatasource 根据datasource获取服务URL
// datasource格式: "global-prometheus" 或 "tke-user-apiserver"
func GetServiceURLByDatasource(datasource string) (string, error) {
	if datasource == "" {
		return "", fmt.Errorf("datasource cannot be empty")
	}

	var configData struct {
		DatasourceMappings map[string]string `mapstructure:"datasourceMappings"`
	}

	err := viper.Unmarshal(&configData)
	if err != nil {
		return "", fmt.Errorf("failed to unmarshal config: %v", err)
	}
	url, exists := configData.DatasourceMappings[datasource]
	if !exists {
		return "", fmt.Errorf("no URL mapping configured for datasource: %s", datasource)
	}

	return url, nil
}
