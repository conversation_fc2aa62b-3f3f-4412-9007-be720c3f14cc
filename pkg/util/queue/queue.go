package queue

import (
	"container/list"
	"sync"
)

// ConcurrentQueue 是一个线程安全的FIFO队列 From deepseek ^_^

type ConcurrentQueue struct {
	list *list.List // 使用双向链表存储元素
	lock sync.Mutex // 互斥锁保证并发安全
}

// NewConcurrentQueue 创建一个新的并发队列
func NewConcurrentQueue() *ConcurrentQueue {
	return &ConcurrentQueue{
		list: list.New(),
	}
}

// Push 将元素添加到队列末尾
func (q *ConcurrentQueue) Push(item interface{}) {
	q.lock.Lock()
	defer q.lock.Unlock()
	q.list.PushBack(item)
}

// Pop 从队列头部移除并返回元素
// 第二个返回值表示是否成功获取元素（false表示队列为空）
func (q *ConcurrentQueue) Pop() (interface{}, bool) {
	q.lock.Lock()
	defer q.lock.Unlock()

	if q.list.Len() == 0 {
		return nil, false
	}

	element := q.list.Front()
	q.list.Remove(element)
	return element.Value, true
}

// Len 返回队列当前元素数量
func (q *ConcurrentQueue) Len() int {
	q.lock.Lock()
	defer q.lock.Unlock()
	return q.list.Len()
}

// IsEmpty 检查队列是否为空
func (q *ConcurrentQueue) IsEmpty() bool {
	return q.Len() == 0
}
