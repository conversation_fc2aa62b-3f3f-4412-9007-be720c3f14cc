package queue

import (
	"sync"
	"testing"
)

func TestNewConcurrentQueue(t *testing.T) {
	queue := NewConcurrentQueue()
	if queue == nil {
		t.<PERSON><PERSON>("NewConcurrentQueue() returned nil")
	}
	if queue != nil && queue.list == nil {
		t.<PERSON>rror("queue.list is nil")
	}
}

func TestConcurrentQueue_PushAndPop(t *testing.T) {
	queue := NewConcurrentQueue()

	// Test Push and Pop with single element
	queue.Push(1)
	if queue.Len() != 1 {
		t.<PERSON>rrorf("queue.Len() = %d; want 1", queue.Len())
	}

	value, ok := queue.Pop()
	if !ok {
		t.Error("queue.Pop() returned false, want true")
	}
	if value != 1 {
		t.Errorf("queue.Pop() = %v; want 1", value)
	}

	// Test Pop from empty queue
	value, ok = queue.Pop()
	if ok {
		t.Error("queue.Pop() from empty queue returned true, want false")
	}
	if value != nil {
		t.Errorf("queue.Pop() from empty queue = %v; want nil", value)
	}
}

func TestConcurrentQueue_MultiplePushPop(t *testing.T) {
	queue := NewConcurrentQueue()
	items := []interface{}{1, "two", 3.0, true}

	// Push multiple items
	for _, item := range items {
		queue.Push(item)
	}

	// Pop and verify all items
	for i, expected := range items {
		value, ok := queue.Pop()
		if !ok {
			t.Errorf("queue.Pop() at index %d returned false, want true", i)
		}
		if value != expected {
			t.Errorf("queue.Pop() at index %d = %v; want %v", i, value, expected)
		}
	}
}

func TestConcurrentQueue_IsEmpty(t *testing.T) {
	queue := NewConcurrentQueue()

	if !queue.IsEmpty() {
		t.Error("queue.IsEmpty() = false; want true for new queue")
	}

	queue.Push(1)
	if queue.IsEmpty() {
		t.Error("queue.IsEmpty() = true; want false after Push")
	}

	queue.Pop()
	if !queue.IsEmpty() {
		t.Error("queue.IsEmpty() = false; want true after Pop")
	}
}

func TestConcurrentQueue_Len(t *testing.T) {
	queue := NewConcurrentQueue()

	if queue.Len() != 0 {
		t.Errorf("queue.Len() = %d; want 0 for new queue", queue.Len())
	}

	queue.Push(1)
	queue.Push(2)
	if queue.Len() != 2 {
		t.Errorf("queue.Len() = %d; want 2 after two Pushes", queue.Len())
	}

	queue.Pop()
	if queue.Len() != 1 {
		t.Errorf("queue.Len() = %d; want 1 after one Pop", queue.Len())
	}
}

func TestConcurrentQueue_ConcurrentAccess(t *testing.T) {
	queue := NewConcurrentQueue()
	const numGoroutines = 10
	const numOperations = 100

	var wg sync.WaitGroup
	wg.Add(numGoroutines * 2) // For both producers and consumers

	// Producers
	for i := 0; i < numGoroutines; i++ {
		go func(id int) {
			defer wg.Done()
			for j := 0; j < numOperations; j++ {
				queue.Push(j)
			}
		}(i)
	}

	// Consumers
	poppedItems := make(chan interface{}, numGoroutines*numOperations)
	for i := 0; i < numGoroutines; i++ {
		go func() {
			defer wg.Done()
			for j := 0; j < numOperations; j++ {
				if value, ok := queue.Pop(); ok {
					poppedItems <- value
				} else {
					j-- // Try again if Pop failed
				}
			}
		}()
	}

	wg.Wait()
	close(poppedItems)

	// Count popped items
	count := 0
	for range poppedItems {
		count++
	}

	if count != numGoroutines*numOperations {
		t.Errorf("Got %d items, want %d items", count, numGoroutines*numOperations)
	}
}
