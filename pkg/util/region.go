package util

type Region struct {
	RegionLong   string
	RegionShort  string
	RegionNameCn string
	Area         string
	Oversea      bool
	PreCCR       bool
}

const (
	AreaHuaNan       = "华南地区"
	AreaHuaDong      = "华东地区"
	AreaHuaBei       = "华北地区"
	AreaHuaZhong     = "华中地区"
	AreaXiNan        = "西南地区"
	AreaXiBei        = "西北地区"
	AreaGangAoTai    = "港澳台地区"
	AreaAsiaPacific  = "亚太地区"
	AreaAPSSoutheast = "亚太东南"
	AreaAPSouth      = "亚太南部"
	AreaAPNortheast  = "亚太东北"
	AreaNA           = "北美地区"
	AreaUSWestern    = "美国西部"
	AreaUSEEastern   = "美国东部"
	AreaEU           = "欧洲地区"
	AreaIndonesia    = "印度尼西亚"
	AreaSouthAmerica = "南美地区"
	AreaAPMiddleEast = "中东地区"
)

// 从前端获取的RegionList，新增region须及时对齐
var regionList = []Region{
	{
		RegionLong:   "ap-guangzhou",
		RegionShort:  "gz",
		RegionNameCn: "广州",
		Area:         AreaHuaNan,
		Oversea:      false,
	}, {
		RegionLong:   "ap-qingyuan",
		RegionNameCn: "清远",
		RegionShort:  "qy",
		Area:         AreaHuaNan,
		Oversea:      false,
	}, {
		RegionLong:   "ap-shenzhen",
		RegionShort:  "szx",
		RegionNameCn: "深圳",
		Area:         AreaHuaNan,
		Oversea:      false,
	}, {
		RegionLong:   "ap-shenzhen-fsi",
		RegionShort:  "szjr",
		RegionNameCn: "深圳金融",
		Area:         AreaHuaNan,
		Oversea:      false,
		PreCCR:       true,
	}, {
		RegionLong:   "ap-shanghai",
		RegionShort:  "sh",
		RegionNameCn: "上海",
		Area:         AreaHuaDong,
		Oversea:      false,
	}, {
		RegionLong:   "ap-shanghai-fsi",
		RegionShort:  "shjr",
		RegionNameCn: "上海金融",
		Area:         AreaHuaDong,
		Oversea:      false,
		PreCCR:       true,
	}, {
		RegionLong:   "ap-nanjing",
		RegionShort:  "nj",
		RegionNameCn: "南京",
		Area:         AreaHuaDong,
		Oversea:      false,
	}, {
		RegionLong:   "ap-jinan-ec",
		RegionShort:  "jnec",
		RegionNameCn: "济南EC",
		Area:         AreaHuaDong,
		Oversea:      false,
	}, {
		RegionLong:   "ap-hangzhou-ec",
		RegionShort:  "hzec",
		RegionNameCn: "杭州EC",
		Area:         AreaHuaDong,
		Oversea:      false,
	}, {
		RegionLong:   "ap-fuzhou-ec",
		RegionShort:  "fzec",
		RegionNameCn: "福州EC",
		Area:         AreaHuaDong,
		Oversea:      false,
	}, {
		RegionLong:   "ap-beijing",
		RegionShort:  "bj",
		RegionNameCn: "北京",
		Area:         AreaHuaBei,
		Oversea:      false,
	}, {
		RegionLong:   "ap-tianjin",
		RegionShort:  "tsn",
		RegionNameCn: "天津",
		Area:         AreaHuaBei,
		Oversea:      false,
	}, {
		RegionLong:   "ap-shijiazhuang-ec",
		RegionShort:  "sjwec",
		RegionNameCn: "石家庄EC",
		Area:         AreaHuaBei,
		Oversea:      false,
	}, {
		RegionLong:   "ap-beijing-fsi",
		RegionShort:  "bjjr",
		RegionNameCn: "北京金融",
		Area:         AreaHuaBei,
		Oversea:      false,
		PreCCR:       true,
	}, {
		RegionLong:   "ap-wuhan-ec",
		RegionShort:  "whec",
		RegionNameCn: "武汉EC",
		Area:         AreaHuaZhong,
		Oversea:      false,
	}, {
		RegionLong:   "ap-changsha-ec",
		RegionShort:  "csec",
		RegionNameCn: "长沙EC",
		Area:         AreaHuaZhong,
		Oversea:      false,
	}, {
		RegionLong:   "ap-chengdu",
		RegionShort:  "cd",
		RegionNameCn: "成都",
		Area:         AreaXiNan,
		Oversea:      false,
	}, {
		RegionLong:   "ap-chongqing",
		RegionShort:  "cq",
		RegionNameCn: "重庆",
		Area:         AreaXiNan,
		Oversea:      false,
	}, {
		RegionLong:   "ap-taipei",
		RegionShort:  "tpe",
		RegionNameCn: "中国台北",
		Area:         AreaGangAoTai,
		Oversea:      true,
		PreCCR:       true,
	}, {
		RegionLong:   "ap-hongkong",
		RegionShort:  "hk",
		RegionNameCn: "中国香港",
		Area:         AreaGangAoTai,
		Oversea:      true,
		PreCCR:       true,
	}, {
		RegionLong:   "ap-singapore",
		RegionShort:  "sg",
		RegionNameCn: "新加坡",
		Area:         AreaAPSSoutheast,
		Oversea:      true,
		PreCCR:       true,
	}, {
		RegionLong:   "ap-bangkok",
		RegionShort:  "th",
		RegionNameCn: "曼谷",
		Area:         AreaAsiaPacific,
		Oversea:      true,
		PreCCR:       true,
	}, {
		RegionLong:   "ap-mumbai",
		RegionShort:  "in",
		RegionNameCn: "孟买",
		Area:         AreaAPSouth,
		Oversea:      true,
		PreCCR:       true,
	}, {
		RegionLong:   "ap-seoul",
		RegionShort:  "kr",
		RegionNameCn: "首尔",
		Area:         AreaAPNortheast,
		Oversea:      true,
		PreCCR:       true,
	}, {
		RegionLong:   "ap-tokyo",
		RegionShort:  "jp",
		RegionNameCn: "东京",
		Area:         AreaAPNortheast,
		Oversea:      true,
		PreCCR:       true,
	}, {
		RegionLong:   "na-siliconvalley",
		RegionShort:  "usw",
		RegionNameCn: "硅谷",
		Area:         AreaUSWestern,
		Oversea:      true,
		PreCCR:       true,
	}, {
		RegionLong:   "na-ashburn",
		RegionShort:  "use",
		RegionNameCn: "弗吉尼亚",
		Area:         AreaUSEEastern,
		Oversea:      true,
		PreCCR:       true,
	}, {
		RegionLong:   "na-toronto",
		RegionShort:  "ca",
		RegionNameCn: "多伦多",
		Area:         AreaNA,
		Oversea:      true,
		PreCCR:       true,
	}, {
		RegionLong:   "eu-frankfurt",
		RegionShort:  "de",
		RegionNameCn: "法兰克福",
		Area:         AreaEU,
		Oversea:      true,
		PreCCR:       true,
	}, {
		RegionLong:   "eu-moscow",
		RegionShort:  "ru",
		RegionNameCn: "莫斯科",
		Area:         AreaEU,
		Oversea:      true,
		PreCCR:       true,
	}, {
		RegionLong:   "ap-xibei-ec",
		RegionShort:  "xbec",
		RegionNameCn: "西北EC",
		Area:         AreaXiBei,
		Oversea:      false,
	}, {
		RegionLong:   "ap-shenyang-ec",
		RegionShort:  "sheec",
		RegionNameCn: "沈阳EC",
		Area:         AreaXiBei,
		Oversea:      false,
	}, {
		RegionLong:   "ap-hefei-ec",
		RegionShort:  "hfeec",
		RegionNameCn: "合肥EC",
		Area:         AreaXiBei,
		Oversea:      false,
	},
	{
		RegionLong:   "ap-jakarta",
		RegionShort:  "jkt",
		RegionNameCn: "雅加达",
		Area:         AreaIndonesia,
		Oversea:      true,
		PreCCR:       true,
	},
	{
		RegionLong:   "sa-saopaulo",
		RegionShort:  "sao",
		RegionNameCn: "圣保罗",
		Area:         AreaSouthAmerica,
		Oversea:      true,
		PreCCR:       true,
	},
	{
		RegionLong:   "ap-xian-ec",
		RegionShort:  "xiyec",
		RegionNameCn: "西安EC",
		Area:         AreaXiBei,
		Oversea:      false,
	},
	{
		RegionLong:   "ap-shanghai-adc",
		RegionShort:  "shadc",
		RegionNameCn: "上海自动驾驶云",
		Area:         AreaHuaDong,
		Oversea:      false,
		PreCCR:       true,
	},
	{
		RegionLong:   "ap-guangzhou-wxzf",
		RegionShort:  "gzwxzf",
		RegionNameCn: "广州微信支付",
		Area:         AreaHuaNan,
		Oversea:      false,
		PreCCR:       true,
	},
	{
		RegionLong:   "ap-shanghai-wxzf",
		RegionShort:  "shwxzf",
		RegionNameCn: "上海微信支付",
		Area:         AreaHuaDong,
		Oversea:      false,
		PreCCR:       true,
	},
	{
		RegionLong:   "ap-shenzhen-jxcft",
		RegionShort:  "szjxcft",
		RegionNameCn: "深圳锦绣财付通",
		Area:         AreaHuaNan,
		Oversea:      false,
		PreCCR:       true,
	},
	{
		RegionLong:   "ap-shanghai-hq-cft",
		RegionShort:  "shhqcft",
		RegionNameCn: "上海花桥财付通",
		Area:         AreaHuaDong,
		Oversea:      false,
		PreCCR:       true,
	},
	{
		RegionLong:   "ap-shanghai-hq-uat-cft",
		RegionShort:  "shhqcftfzhj",
		RegionNameCn: "上海花桥财付通仿真环境",
		Area:         AreaHuaDong,
		Oversea:      false,
		PreCCR:       true,
	},
	{
		RegionLong:   "ap-zhengzhou-ec",
		RegionShort:  "cgoec",
		RegionNameCn: "郑州",
		Area:         AreaHuaZhong,
		Oversea:      false,
		PreCCR:       true,
	},
	{
		RegionLong:   "ap-nanjing-xf-cft",
		RegionShort:  "njxfcft",
		RegionNameCn: "南京学府财付通",
		Area:         AreaHuaDong,
		Oversea:      false,
		PreCCR:       true,
	},
	{
		RegionLong:   "ap-shanghai-wxp-ops",
		RegionShort:  "shwxzfjpyzc",
		RegionNameCn: "上海微信支付精品云支撑",
		Area:         AreaHuaDong,
		Oversea:      false,
		PreCCR:       true,
	},
	{
		RegionLong:   "me-saudi-arabia",
		RegionShort:  "sa",
		RegionNameCn: "沙特",
		Area:         AreaAPMiddleEast,
		Oversea:      true,
		PreCCR:       true,
	},
	{
		RegionLong:   "ap-zhongwei",
		RegionShort:  "zw",
		RegionNameCn: "中卫",
		Area:         AreaXiBei,
		Oversea:      false,
		PreCCR:       true,
	},
}

func FindByLongName(regionLong string) *Region {
	rg := Region{}
	for _, r := range regionList {
		if r.RegionLong == regionLong {
			rg = r
			return &rg
		}
	}
	return nil
}

func FindByShortName(regionShort string) *Region {
	return FindByLongName(ShortToLong(regionShort))
}

func ShortToLong(regionShort string) string {
	for _, r := range regionList {
		if r.RegionShort == regionShort {
			return r.RegionLong
		}
	}
	return ""
}

func LongToShort(regionLong string) string {
	for _, r := range regionList {
		if r.RegionLong == regionLong {
			return r.RegionShort
		}
	}
	return ""
}
