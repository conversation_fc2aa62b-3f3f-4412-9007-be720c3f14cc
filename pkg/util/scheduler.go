package util

import (
	"context"
	"errors"
	"fmt"
	"regexp"
	"strconv"
	"strings"
	"time"

	sc "cloud.tencent.com/mervynwang/scheduler-tools/pkg/schedulerConfig"

	"github.com/hashicorp/go-version"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	v1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/serializer"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/client-go/kubernetes"
	"k8s.io/klog/v2"
	"sigs.k8s.io/yaml"
)

const (
	eksSvcName = "kube-eklet-support"
	tkeSvcName = "eklet-svc"
	portName   = "ekscheduler"
)

var versionPattern = regexp.MustCompile(`^v\d+\.\d+\.\d+-tke\.\d+$`)

func UpgradeScheduler(client kubernetes.Interface, clusterType, clusterId string, schedulerConfig string) error {
	// 1. 校验k8s版本是否支持
	deployment, err := client.AppsV1().Deployments(clusterId).Get(context.TODO(), fmt.Sprintf("%s-scheduler", clusterId), metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("failed to get scheduler deployment: %v", err)
	}
	isSupported, k8sVersion := preCheckK8sVersionAndConfig(deployment)
	if !isSupported {
		// 版本不符合要求，直接跳过调度器配置文件升级
		return nil
	}

	// 2. 获取并备份ConfigMap
	var cm *v1.ConfigMap
	var cmName string
	switch clusterType {
	case ProductEKS:
		cmName = "scheduler-config"
	case ProductTKE:
		cmName = clusterId + "-extra-config"
	default:
		return fmt.Errorf("unsupported cluster type: %s", clusterType)
	}

	cm, err = client.CoreV1().ConfigMaps(clusterId).Get(context.TODO(), cmName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("failed to get scheduler ConfigMap: %v", err)
	}

	// 3. 备份现有配置
	if err = backupCmAndConfiguration(cm); err != nil {
		return fmt.Errorf("failed to backup scheduler configuration: %v", err)
	}

	// 4. 更新调度器配置
	schedulerCfg, err := sc.NewSchedulerConfig(clusterType, k8sVersion)
	if err != nil {
		return fmt.Errorf("failed to create new scheduler config: %v", err)
	}

	// 4.1 反序列化现有配置
	if err = schedulerCfg.Unmarshal(cm); err != nil {
		return fmt.Errorf("failed to unmarshal scheduler config: %v", err)
	}

	// 4.2 更新Extender配置
	if err = schedulerCfg.UpdateExtender([]string{schedulerConfig}, nil); err != nil {
		return fmt.Errorf("failed to update scheduler extender: %v", err)
	}

	// 4.3 序列化更新后的配置
	if err = schedulerCfg.Marshal(cm); err != nil {
		return fmt.Errorf("failed to marshal scheduler config: %v", err)
	}

	// 5. 更新ConfigMap
	if _, err = client.CoreV1().ConfigMaps(clusterId).Update(context.TODO(), cm, metav1.UpdateOptions{}); err != nil {
		return fmt.Errorf("failed to update scheduler ConfigMap: %v", err)
	}

	// 6. 验证配置格式
	updatedCm, err := client.CoreV1().ConfigMaps(clusterId).Get(context.TODO(), cmName, metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("failed to get updated ConfigMap: %v", err)
	}

	if err = checkAndPrintConfigMap(updatedCm); err != nil {
		return fmt.Errorf("invalid scheduler configuration format: %v", err)
	}

	// 7. 重启调度器
	if err = restartScheduler(client, clusterId); err != nil {
		return fmt.Errorf("failed to restart scheduler: %v", err)
	}
	klog.Info("scheduler restart completed successfully")

	return nil
}

func DecodeConfigMap(configMapYAML string) (*corev1.ConfigMap, error) {
	decoder := serializer.NewCodecFactory(runtime.NewScheme()).UniversalDeserializer()
	configMap := &corev1.ConfigMap{}
	_, _, err := decoder.Decode([]byte(configMapYAML), nil, configMap)
	if err != nil {
		return nil, fmt.Errorf("decoding ConfigMap YAML failed, err: %v", err)
	}
	return configMap, nil
}

func EncodeConfigMap(configMap *corev1.ConfigMap) (string, error) {
	scheme := runtime.NewScheme()
	if err := corev1.AddToScheme(scheme); err != nil {
		return "", fmt.Errorf("error adding ConfigMap to scheme: %v", err)
	}
	codecFactory := serializer.NewCodecFactory(scheme)
	encoder := codecFactory.LegacyCodec(corev1.SchemeGroupVersion)
	//encoder := serializer.NewCodecFactory(scheme).EncoderForVersion(serializer.NewCodecFactory(scheme).LegacyCodec(corev1.SchemeGroupVersion), corev1.SchemeGroupVersion)
	configMapBytes, err := runtime.Encode(encoder, configMap)
	if err != nil {
		return "", fmt.Errorf("error encoding ConfigMap YAML: %v", err)
	}

	yamlString, err := yaml.JSONToYAML(configMapBytes)
	if err != nil {
		return "", fmt.Errorf("error encoding ConfigMap YAML: %v", err)
	}

	return string(yamlString), nil
}

func checkConfigMap(configMap *corev1.ConfigMap) error {
	// 检验 configMap 格式
	cfgYaml, _ := EncodeConfigMap(configMap)
	_, _ = DecodeConfigMap(cfgYaml)
	return nil
}

func checkAndPrintConfigMap(configMap *corev1.ConfigMap) error {
	err := checkConfigMap(configMap)
	if err != nil {
		return errors.New("error decoding configMap or KubeSchedulerConfiguration to YAML")
	}
	return nil
}

func isSupportedVersion(str string) (bool, string) {
	// Extract the tag part after ':' if present
	tag := str
	if idx := strings.LastIndex(str, ":"); idx != -1 {
		tag = str[idx+1:]
	}

	minVersions := []string{
		"v1.30.0-tke.2",
		"v1.28.3-tke.6",
		"v1.26.1-tke.8",
		"v1.24.4-tke.18",
		"v1.22.5-tke.28",
		"v1.20.6-tke.47",
		"v1.18.4-tke.49",
	}

	// Match the tag against the format 'vX.Y.Z-tke.N'
	if !versionPattern.MatchString(tag) {
		return false, ""
	}

	// Find the matching minimum version
	for _, v := range minVersions {
		prefix := v[:strings.LastIndex(v, ".")]
		if strings.HasPrefix(tag, prefix) {
			semverTag, err := version.NewSemver(tag)
			if err != nil {
				return false, ""
			}
			semverMin, err := version.NewSemver(v)
			if err != nil {
				return false, ""
			}
			if semverTag.GreaterThanOrEqual(semverMin) {
				// Return the major version (e.g., 'v1.18.4')
				majorVersion := v[:strings.Index(v, "-tke")]
				return true, majorVersion
			}
			return false, ""
		}
	}

	return false, ""
}

func preCheckK8sVersionAndConfig(deployment *appsv1.Deployment) (bool, string) {
	for _, container := range deployment.Spec.Template.Spec.Containers {
		if container.Name != "scheduler" {
			continue
		}
		isSupport, k8sVersion := isSupportedVersion(container.Image)
		if !isSupport {
			return false, ""
		}
		for _, cmd := range container.Command {
			if (strings.HasPrefix(cmd, "--config=") && !strings.HasSuffix(cmd, ".yaml")) ||
				(strings.HasPrefix(cmd, "--policy-config-file=") && !strings.HasSuffix(cmd, ".json")) {
				return false, ""
			}
		}
		return true, k8sVersion

	}
	return false, ""
}

func restartScheduler(metaClientSet kubernetes.Interface, clusterId string) error {
	deployment, err := metaClientSet.AppsV1().Deployments(clusterId).Get(context.TODO(), fmt.Sprintf("%s-scheduler", clusterId), metav1.GetOptions{})
	if err != nil {
		return fmt.Errorf("get scheduler deploy failed, err: %v", err)
	}
	if deployment.Spec.Template.Annotations == nil {
		deployment.Spec.Template.Annotations = make(map[string]string)
	}
	deployment.Spec.Template.Annotations["qcloud-redeploy-timestamp"] = strconv.FormatInt(time.Now().UnixMilli(), 10)
	_, err = metaClientSet.AppsV1().Deployments(clusterId).Update(context.TODO(), deployment, metav1.UpdateOptions{})
	if err != nil {
		return fmt.Errorf("update scheduler deploy failed, err: %v", err)
	}
	return nil
}

func backupCmAndConfiguration(cm *corev1.ConfigMap) error {
	// 暂时不支持直接保存，日志打印
	oldCmYaml, err := EncodeConfigMap(cm)
	if err != nil {
		return err
	}
	klog.Infof("old configMap: %s", oldCmYaml)
	return nil
}

func UpgradeSvc(client kubernetes.Interface, clusterType, clusterId string) (err error) {
	var ekletSvc *corev1.Service
	switch clusterType {
	case ProductEKS:
		ekletSvc, err = client.CoreV1().Services(clusterId).Get(context.TODO(), eksSvcName, metav1.GetOptions{})
		if err != nil {
			return fmt.Errorf("fail to get eklet service. err:%s. clusterid:%s\n", err, clusterId)
		}
		klog.Infof("EKS cluster service: %s/%s", ekletSvc.Namespace, ekletSvc.Name)
	case ProductTKE:
		ekletSvc, err = client.CoreV1().Services(clusterId).Get(context.TODO(), tkeSvcName, metav1.GetOptions{})
		if err != nil {
			return fmt.Errorf("fail to get eklet service. err:%s. clusterid:%s\n", err, clusterId)
		}
		klog.Infof("TKE cluster service: %s/%s", ekletSvc.Namespace, ekletSvc.Name)
	}

	if ekletSvc == nil {
		return fmt.Errorf("fail to get eklet service. err:%s. clusterid:%s\n", err, clusterId)
	}

	var portExist bool
	for _, port := range ekletSvc.Spec.Ports {
		if port.Name == portName {
			portExist = true
		}
	}

	if !portExist {
		newPort := corev1.ServicePort{
			Name:       portName,
			Protocol:   corev1.ProtocolTCP,
			Port:       8080,
			TargetPort: intstr.FromInt32(8080),
		}
		ekletSvc.Spec.Ports = append(ekletSvc.Spec.Ports, newPort)
		_, err := client.CoreV1().Services(ekletSvc.Namespace).Update(context.TODO(), ekletSvc, metav1.UpdateOptions{})
		if err != nil {
			return fmt.Errorf("fail to update eklet service. err:%s. clusterid:%s\n", err, clusterId)
		}
		klog.Infof("success update service %s/%s", ekletSvc.Namespace, ekletSvc.Name)
	}

	return nil
}
