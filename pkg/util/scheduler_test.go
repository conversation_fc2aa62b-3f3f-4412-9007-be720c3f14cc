package util

import "testing"

func TestIsSupportedVersion(t *testing.T) {
	tests := []struct {
		input    string
		expected bool
		version  string
	}{
		// 测试等于最低版本的情况
		{"image:v1.30.0-tke.2", true, "v1.30.0"},
		{"image:v1.28.3-tke.6", true, "v1.28.3"},
		{"image:v1.26.1-tke.8", true, "v1.26.1"},
		{"image:v1.24.4-tke.18", true, "v1.24.4"},
		{"image:v1.22.5-tke.28", true, "v1.22.5"},
		{"image:v1.20.6-tke.47", true, "v1.20.6"},
		{"image:v1.18.4-tke.49", true, "v1.18.4"},

		// 测试高于最低版本的情况
		{"image:v1.30.0-tke.3", true, "v1.30.0"},
		{"image:v1.28.3-tke.10", true, "v1.28.3"},
		{"image:v1.26.1-tke.15", true, "v1.26.1"},
		{"image:v1.24.4-tke.20", true, "v1.24.4"},
		{"image:v1.22.5-tke.30", true, "v1.22.5"},
		{"image:v1.20.6-tke.50", true, "v1.20.6"},
		{"image:v1.18.4-tke.60", true, "v1.18.4"},

		// 测试低于最低版本的情况
		{"image:v1.30.0-tke.1", false, ""},
		{"image:v1.28.3-tke.5", false, ""},
		{"image:v1.26.1-tke.7", false, ""},
		{"image:v1.24.4-tke.17", false, ""},
		{"image:v1.22.5-tke.27", false, ""},
		{"image:v1.20.6-tke.46", false, ""},
		{"image:v1.18.4-tke.48", false, ""},

		// 测试不匹配的版本
		{"image:v1.31.0-tke.1", false, ""},
		{"image:v1.29.0-tke.1", false, ""},
		{"image:v1.27.0-tke.1", false, ""},
		{"image:v1.25.0-tke.1", false, ""},
		{"image:v1.23.0-tke.1", false, ""},
		{"image:v1.21.0-tke.1", false, ""},
		{"image:v1.19.0-tke.1", false, ""},
		{"image:v1.17.0-tke.1", false, ""},

		// 测试复杂字符串
		{"ccr.ccs.tencentyun.com/tkeimages/hyperkube:v1.24.4-tke.18", true, "v1.24.4"},
		{"v1.24.4-tke.17-with-extra-stuff", false, ""},
	}

	for i, test := range tests {
		got, version := isSupportedVersion(test.input)
		if got != test.expected || version != test.version {
			t.Errorf("Test %d: isSupportedVersion(%q) = (%v, %q), want (%v, %q)",
				i, test.input, got, version, test.expected, test.version)
		}
	}
}
