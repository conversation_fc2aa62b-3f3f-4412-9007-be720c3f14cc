package util

import (
	"bytes"
	"context"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"io"
	"os"
	"regexp"
	"strconv"
	"strings"

	masterclientset "cloud.tencent.com/tke/master-operator/pkg/client/clientset/versioned"
	ianvs "git.woa.com/ianvs/ianvs-sdk/pkg/client"
	appsv1 "k8s.io/api/apps/v1"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/intstr"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/klog"

	"git.woa.com/kmetis/starship/pkg/model"
	"git.woa.com/kmetis/starship/pkg/strategyprovider"
	tadclientset "git.woa.com/tad-dev/api/generated/clientset/versioned"
)

const (
	ProductTKE = "tke"
	ProductEKS = "eks"

	ComponentApiserver              = "apiserver"
	ComponentEtcd                   = "etcd"
	ComponentScheduler              = "scheduler"
	ComponentControllerManager      = "controller-manager"
	ComponentServiceController      = "service-controller"
	ComponentIngressController      = "ingress-controller"
	ComponentCBSController          = "cbs-provisioner"
	ComponentHPAController          = "hpa-metrics-server"
	ComponentCloudControllerManager = "cloud-controller-manager"
	ComponentClusterMonitor         = "cluster-monitor"
	ComponentKubeJarvis             = "kube-jarvis"
	ComponentProxy                  = "proxy"
	ComponentCSIController          = "csi-cbs-controller"
	ComponentEklet                  = "eklet"
	ComponentEksController          = "eks-controllers"
	ComponentClusterAutoScaler      = "cluster-autoscaler"
	ComponentHpaMetricsServer       = "hpa-metrics-server"

	ComponentLocationUser = "user"
	ComponentLocationMeta = "meta"

	NamespaceClusterId = "$clusterid"

	GenericStrategyPrefix      = "generic-"
	GlobalHealthCheckComponent = ""
	ReleaseStrategyDeployment  = "deployment"
	ReleaseStrategyStatefulSet = "statefulset"
	ReleaseStrategyDaemonSet   = "daemonset"
	ReleaseStrategyPod         = "pod"
	ReleaseStrategyMasterCRD   = "masterCRD"
	ReleaseStrategyPlugin      = "plugin"
	ReleaseStrategyAddon       = "addon"
	ReleaseStrategyK8sNative   = "k8sNative"
	ReleaseStrategyAppFabric   = "appFabricApplication"
	ReleaseStrategyGRPC        = "grpc"
	ReleaseStrategyTcrCert     = "tcrCert"
	ReleaseStrategyEkletagent  = "eklet-agent"
	ReleaseStrategyConfigmap   = "configmap"

	// 任务动作
	TaskActionPreCheck  = "precheck"
	TaskActionUpgrade   = "upgrade"
	TaskActionPostCheck = "postcheck"
	TaskActionRollback  = "rollback"

	// 这个名称是固定的，在application-controller中使用。
	UserApplicationController = "ApplicationController"

	// DB中任务的状态
	TaskStatusPending    = "pending"
	TaskStatusProcessing = "processing"
	TaskStatusDone       = "done"

	EkletAgentTaskTypeCompleted = "Completed"
	EkletAgentTaskTypeFailed    = "Failed"
)

func IsAsyncStrategy(strategy string) bool {
	return strategy == ReleaseStrategyPlugin || strategy == ReleaseStrategyGRPC
}

func GetConfigValue(key string) (string, bool) {
	value, exists := os.LookupEnv(key)
	if !exists {
		return "", false
	}
	return value, true
}

func K8sClient(cls, token, user string) (kubernetes.Interface, error) {
	var restConfig *rest.Config
	var err error
	if restConfig, err = GetRestConfig(cls, token, user); err != nil {
		return nil, err
	}

	return kubernetes.NewForConfig(restConfig)
}

func MetaClient(cls, token, user string) (masterclientset.Interface, error) {
	var restConfig *rest.Config
	var err error
	if restConfig, err = GetRestConfig(cls, token, user); err != nil {
		return nil, err
	}

	return masterclientset.NewForConfig(restConfig)
}

func TadClient(cls, token, user string) (tadclientset.Interface, error) {
	var restConfig *rest.Config
	var err error
	if restConfig, err = GetRestConfig(cls, token, user); err != nil {
		return nil, err
	}
	return tadclientset.NewForConfig(restConfig)
}

// GetRestConfig通过ianvs token 获取rest config，using recover to avioid panic
func GetRestConfig(cls, token, user string) (config *rest.Config, err error) {
	defer func() {
		if r := recover(); r != nil {
			config = nil
			err = fmt.Errorf("failed to get rest config")
		}
	}()

	client := ianvs.NewTmpTokenClient(user, token)
	config, err = client.GetRestConfig(cls)
	if err != nil {
		return nil, err
	}
	return
}

func GetComponentNamespace(clusterId, namespace string) string {
	if namespace == NamespaceClusterId {
		return clusterId
	}
	return namespace
}

func GetWorkloadName(clusterId, workloadName string) string {
	if strings.HasPrefix(workloadName, NamespaceClusterId) {
		workloadName = clusterId + strings.TrimPrefix(workloadName, NamespaceClusterId)
		return workloadName
	}
	return workloadName
}

func skipImageTag(appName, strategy string, extendInfo *model.ExtendInfo) bool {
	if strategy == ReleaseStrategyAppFabric || strategy == ReleaseStrategyAddon {
		return true
	}

	// kube-proxy隐患治理：仅修改ipvs timeout场景
	// eklet隐患治理：有仅变更args的需求
	// kube-schedule也类似有只变更参数的需求
	// clusterscaler有仅变更annotation的需求
	if (appName == "kube-proxy" || appName == "eklet" || appName == "scheduler" || appName == "clusterscaler") && (extendInfo != nil && (len(extendInfo.Args) > 0 || len(extendInfo.Annotations) > 0)) {
		return true
	}

	return false
}

func ValidateTaskStrategyRequest(request *strategyprovider.TaskStrategyRequest) error {
	// NOTICE:
	// 1. 从application-controller过来的请求，不需要进行校验;
	// 2. rollback的任务也不进行检查
	// 3. tcr证书变更的任务也不进行检查：hook中注释掉ValidateTaskStrategyRequest的调用
	if request.User == UserApplicationController || request.IsRollback {
		// TODO：要进行适配，根据公用的user去申请只读token。如果请求是从application-controller过来的，
		// 没有发起人（user和 ivanToken信息为空），所以无法执行deploy ready检查
		klog.Infof("request is from application-controller or rollback or tcrCert, skip validate, req is:%+v", request)
		return nil
	}
	if request.Location != ComponentLocationUser && request.Location != ComponentLocationMeta {
		klog.Errorf("cluster id %s,unknown location %s", request.ClusterId, request.Location)
		return fmt.Errorf("unknown location %s", request.Location)
	}
	if request.Component == "" {
		klog.Errorf("cluster id %s,component is empty", request.ClusterId)
		return fmt.Errorf("component is empty")
	}
	if request.Strategy != ReleaseStrategyAppFabric && request.WorkloadName == "" {
		klog.Errorf("cluster id %s,workload name is empty", request.ClusterId)
		return fmt.Errorf("workload name is empty")
	}
	if request.Strategy != ReleaseStrategyAppFabric && request.Namespace == "" {
		klog.Errorf("cluster id %s,namespace is empty", request.ClusterId)
		return fmt.Errorf("namespace is empty")
	}
	if request.ClusterId == "" {
		klog.Errorf("cluster id %s is empty", request.ClusterId)
		return fmt.Errorf("cluster id is empty")
	}
	if request.Location == ComponentLocationMeta && request.MetaClusterId == "" {
		klog.Errorf("cluster id %s,meta cluster id is empty", request.ClusterId)
		return fmt.Errorf("meta cluster id is empty")
	}
	if !skipImageTag(request.Component, request.Strategy, request.Extend) && request.ImageTag == "" {
		klog.Errorf("cluster id %s,image tag is empty", request.ClusterId)
		return fmt.Errorf("image tag is empty")
	}
	if request.User == "" {
		klog.Errorf("cluster id %s,user is empty", request.ClusterId)
		return fmt.Errorf("user is empty")
	}
	if request.ChangeId == "" {
		klog.Errorf("cluster id %s,change id is empty", request.ClusterId)
		return fmt.Errorf("change id is empty")
	}
	if request.TaskName == "" {
		klog.Errorf("cluster id %s,task name is empty", request.ClusterId)
		return fmt.Errorf("task name is empty")
	}
	// WorkloadType校验
	if request.Strategy != ReleaseStrategyAppFabric && request.WorkloadType != ReleaseStrategyDeployment && request.WorkloadType != ReleaseStrategyStatefulSet && request.WorkloadType != ReleaseStrategyDaemonSet &&
		request.WorkloadType != ReleaseStrategyPod && request.WorkloadType != ReleaseStrategyConfigmap {
		klog.Errorf("cluster id %s,workload type %s is invalid", request.ClusterId, request.WorkloadType)
		return fmt.Errorf("workload type %s is invalid", request.WorkloadType)
	}
	return nil
}

func GetTargetK8sClient(request *strategyprovider.TaskStrategyRequest) (kubernetes.Interface, error) {
	var clusterId string
	if request.Location == ComponentLocationUser {
		clusterId = request.ClusterId
	} else if request.Location == ComponentLocationMeta {
		clusterId = request.MetaClusterId
	}
	client, err := K8sClient(clusterId, request.IanvsToken, request.User)
	if err != nil {
		return nil, err
	}
	return client, err
}

func GetTargetMetaClient(request *strategyprovider.TaskStrategyRequest) (masterclientset.Interface, error) {
	client, err := MetaClient(request.MetaClusterId, request.IanvsToken, request.User)
	if err != nil {
		return nil, err
	}
	return client, err
}

func GetTargetTadClient(request *strategyprovider.TaskStrategyRequest) (tadclientset.Interface, error) {
	client, err := TadClient(request.ClusterId, request.IanvsToken, request.User)
	if err != nil {
		return nil, err
	}
	return client, err
}

func GetTargetImage(originImage, targetImageTag string, extendInfo *model.ExtendInfo) (string, error) {
	// 在部分场景(skipImageTag)，允许targetImageTag为空，此时保持image不变
	if targetImageTag == "" && extendInfo != nil &&
		(len(extendInfo.Args) > 0 || len(extendInfo.Annotations) > 0 || len(extendInfo.Envs) > 0 || len(extendInfo.HostAlias) > 0) {
		return originImage, nil
	}

	images := strings.Split(originImage, ":")
	if extendInfo != nil && len(extendInfo.Args) == 0 && len(extendInfo.HostAlias) == 0 &&
		len(extendInfo.Annotations) == 0 && len(extendInfo.Envs) == 0 && !extendInfo.Scheduler &&
		len(images) > 1 && images[1] == targetImageTag {
		return "", fmt.Errorf("image tag is same")
	}

	// 严格判断，只有当第二部分为ccs-dev，才进行替换
	repoParts := strings.Split(images[0], "/")
	if len(repoParts) == 3 && repoParts[1] == "ccs-dev" && repoParts[2] == "hyperkube" {
		repoParts[1] = "tkeimages"
		images[0] = strings.Join(repoParts, "/")
	}

	return images[0] + ":" + targetImageTag, nil
}

// 获取镜像Tag
func GetImageTag(image string) (string, error) {
	images := strings.Split(image, ":")
	if len(images) < 2 || images[1] == "" {
		return "", fmt.Errorf("image tag is invalid, image:%s", image)
	}
	return images[1], nil
}

func GetImageRepoAndTag(image string) (string, string) {
	splitedImage := strings.Split(image, ":")
	if len(splitedImage) == 2 {
		return splitedImage[0], splitedImage[1]
	}
	return "", ""
}

// 获取workload(支持deployment、statefulset、daemonset)第1个容器的image
func GetWorkloadImage(client kubernetes.Interface, workloadType, namespace, name string, extend *model.ExtendInfo) ([]string, error) {
	podSpec, err := getPodSpecFromWorkload(client, workloadType, namespace, name)
	if err != nil {
		return nil, err
	}

	if len(podSpec.Containers) == 0 {
		return nil, fmt.Errorf("%s %s/%s has no containers", workloadType, namespace, name)
	}

	// 如果只有一个容器，直接返回其镜像
	if len(podSpec.Containers) == 1 {
		return []string{podSpec.Containers[0].Image}, nil
	}

	// 获取需要查询的容器索引
	indexes, err := GetContainerIndexes(podSpec, extend)
	if err != nil {
		return nil, err
	}

	return getImageNameFromIndex(podSpec, indexes)
}

func getPodSpecFromWorkload(client kubernetes.Interface, workloadType, namespace, name string) (*corev1.PodSpec, error) {
	switch workloadType {
	case ReleaseStrategyDeployment:
		deployment, err := client.AppsV1().Deployments(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if err != nil {
			return nil, err
		}
		return &deployment.Spec.Template.Spec, nil

	case ReleaseStrategyStatefulSet:
		statefulset, err := client.AppsV1().StatefulSets(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if err != nil {
			return nil, err
		}
		return &statefulset.Spec.Template.Spec, nil

	case ReleaseStrategyDaemonSet:
		daemonset, err := client.AppsV1().DaemonSets(namespace).Get(context.TODO(), name, metav1.GetOptions{})
		if err != nil {
			return nil, err
		}
		return &daemonset.Spec.Template.Spec, nil

	default:
		return nil, fmt.Errorf("unsupported workload type: %s", workloadType)
	}
}

func getImageNameFromIndex(podSpec *corev1.PodSpec, indexes []int) ([]string, error) {
	// 检查PodSpec是否为空或没有容器
	if podSpec == nil || len(podSpec.Containers) == 0 {
		return nil, fmt.Errorf("podSpec is empty or has no containers")
	}

	// 检查索引是否有效
	for _, index := range indexes {
		if index < 0 || index >= len(podSpec.Containers) {
			return nil, fmt.Errorf("index %d out of range, container count is %d", index, len(podSpec.Containers))
		}
	}

	// 收集指定索引的镜像名称
	imageNames := make([]string, 0, len(indexes))
	for _, index := range indexes {
		imageNames = append(imageNames, podSpec.Containers[index].Image)
	}

	return imageNames, nil
}

func BuildReleaseConfig(strategyRequest *strategyprovider.TaskStrategyRequest) *strategyprovider.ReleaseConfig {
	releaseConfig := &strategyprovider.ReleaseConfig{
		Region:        strategyRequest.Region,
		ClusterId:     strategyRequest.ClusterId,
		Strategy:      strategyRequest.Strategy,
		Component:     strategyRequest.Component,
		PluginVersion: strategyRequest.PluginVersion,
		ChangeId:      strategyRequest.ChangeId,
		TaskId:        strategyRequest.TaskId,
		SubTaskId:     strategyRequest.SubTaskId,
		IanvsToken:    strategyRequest.IanvsToken,
		User:          strategyRequest.User,
		ImageTag:      strategyRequest.ImageTag,
		Action:        strategyRequest.Action,
		Timeout:       strategyRequest.Timeout,
		ProductName:   strategyRequest.ProductName,
	}
	return releaseConfig
}

// imageTag为 v1.21.6-tke.422.xxx 或者 1.21.6-tke.422.xxx 格式，要不要v前缀都可以
func GetVersionFromImageTag(imageTag string) (*model.TagVersion, error) {
	tagVersion := &model.TagVersion{}
	reVersion := regexp.MustCompile(`v?(\d+)\.(\d+)\.(\d+)(?:-tke\.)?(\d+)?`)
	matchArrStr := reVersion.FindStringSubmatch(imageTag)
	var matchArrInt []int
	for k, v := range matchArrStr {
		if k == 0 {
			continue
		}
		if v != "" {
			value, err := strconv.Atoi(v)
			if err != nil {
				klog.Errorf("failed to convert string to int, err:%v", err)
				return nil, err
			}
			matchArrInt = append(matchArrInt, value)
		}

	}
	if len(matchArrStr) >= 3 {
		tagVersion.MajorVersion = matchArrInt[0]
		tagVersion.MinorVersion = matchArrInt[1]
		tagVersion.PatchVersion = matchArrInt[2]

		// 避免tke版本为空，导致后续判断出错
		if len(matchArrInt) >= 4 {
			tagVersion.TkeVersion = matchArrInt[3]
		}
	} else {
		return nil, fmt.Errorf("failed to get major and minor version from image tag: %s", imageTag)
	}
	return tagVersion, nil
}

// error等信息写入DB时，需要将error等信息压缩，否则超过DB定义的长度会报错
// 报错：Data too long for column
func AdaptAttributeMaxLength(line string) string {
	compressedLine := strings.ReplaceAll(line, "\n", " ")
	if len(compressedLine) > 1024 {
		compressedLine = compressedLine[0:1024]
	}
	return compressedLine
}

// 获取pod所在的namespace
func GetPodNamespace() (string, error) {
	if ns := os.Getenv("POD_NAMESPACE"); ns != "" {
		return ns, nil
	}
	return "", fmt.Errorf("failed to get pod namespace from env")
}

// 获取pod所在的region
func GetLongRegion() (string, error) {
	if region := os.Getenv("LONG_REGION"); region != "" {
		return region, nil
	}
	return "", fmt.Errorf("failed to get region from env")
}

// 获取pod所在的shortRegion
func GetShortRegion() (string, error) {
	longRegion := os.Getenv("LONG_REGION")
	if longRegion == "" {
		return "", fmt.Errorf("failed to get region from env")
	}

	shortRegion := LongToShort(longRegion)
	if shortRegion == "" {
		return "", fmt.Errorf("failed to convert long region to short region: %s", longRegion)
	}

	return shortRegion, nil
}

// 根据需要新增/更新/移除的arg参数，构建新的workload args参数
func BuildNewArgs(oldArgs []string, updateArgItems []map[string]string) ([]string, error) {
	oldArgsCopy := make([]string, len(oldArgs))
	copy(oldArgsCopy, oldArgs)

	for _, argInfo := range updateArgItems {
		actionType := argInfo["type"]
		newArg := argInfo["value"]
		if actionType == "" || newArg == "" {
			return nil, fmt.Errorf("invalid args: %v", argInfo)
		}

		switch actionType {
		case "update":
			// check if arg key exists and can be updated
			canUpdate := false
			if !strings.Contains(newArg, "=") {
				// if newArg format is --key
				for _, oldArg := range oldArgsCopy {
					if !strings.Contains(oldArg, "=") {
						// if oldArg format is --key
						if oldArg == newArg {
							// arg already exists
							canUpdate = true
							break
						}
					} else {
						// if oldArg format is --key=value
						oldArgSplits := strings.Split(oldArg, "=")
						if len(oldArgSplits) < 2 {
							return nil, fmt.Errorf("invalid arg format for update, oldArg: %v", oldArg)
						}
						if oldArgSplits[0] == newArg {
							return nil, fmt.Errorf("conflict arg key for update, newArg: %v, oldArg: %v", newArg, oldArg)
						}
					}
				}
			} else {
				// if newArg format is --key=value
				newArgSplits := strings.Split(newArg, "=")
				if len(newArgSplits) < 2 {
					return nil, fmt.Errorf("invalid newArg format for update: %v", newArg)
				}
				for i, oldArg := range oldArgsCopy {
					if !strings.Contains(oldArg, "=") {
						// if oldArg format is --key
						if oldArg == newArgSplits[0] {
							return nil, fmt.Errorf("inconsistent arg for update, newArg: %v, oldArg: %v", newArg, oldArg)
						}
					} else {
						// if oldArg format is --key=value
						oldArgSplits := strings.Split(oldArg, "=")
						if len(oldArgSplits) < 2 {
							return nil, fmt.Errorf("invalid arg format for update, oldArg: %v", oldArg)
						}
						if oldArgSplits[0] == newArgSplits[0] {
							if strings.TrimPrefix(oldArg, oldArgSplits[0]) != strings.TrimPrefix(newArg, oldArgSplits[0]) {
								oldArgsCopy[i] = newArg
							}
							canUpdate = true
							break
						}
					}
				}
			}
			if !canUpdate {
				// If key not found, add new arg
				oldArgsCopy = append(oldArgsCopy, newArg)
			}
		case "remove":
			for i, oldArg := range oldArgsCopy {
				if !strings.Contains(newArg, "=") {
					if !strings.Contains(oldArg, "=") {
						if oldArg == newArg {
							oldArgsCopy = append(oldArgsCopy[:i], oldArgsCopy[i+1:]...)
							break
						}
					} else {
						oldArgSplits := strings.Split(oldArg, "=")
						if len(oldArgSplits) < 2 {
							return nil, fmt.Errorf("invalid arg format for remove, oldArg: %v", oldArg)
						}
						if oldArgSplits[0] == newArg {
							oldArgsCopy = append(oldArgsCopy[:i], oldArgsCopy[i+1:]...)
							break
						}
					}
				} else {
					if oldArg == newArg {
						oldArgsCopy = append(oldArgsCopy[:i], oldArgsCopy[i+1:]...)
						break
					}
				}
			}
		default:
			return nil, fmt.Errorf("invalid action type: %s", actionType)
		}
	}

	return oldArgsCopy, nil
}

// 根据需要新增/更新/移除的annotation，构建新的workload annotations参数
// addAnnotationItems 格式示例：[{"type": "update", "key": "key1", "value": "value1"}, {"type": "remove", "key": "key2"}]
// 当type为update时，若key已存在则会更新value值；若key不存在则会添加key value；
// 当type为remove时，若key不存在，则会忽略该条操作
func BuildNewAnnotations(oldAnnotations map[string]string, addAnnotationItems []map[string]string) (map[string]string, error) {
	oldAnnotationsCopy := make(map[string]string)
	for k, v := range oldAnnotations {
		oldAnnotationsCopy[k] = v
	}

	for _, annotationItem := range addAnnotationItems {
		actionType := strings.TrimSpace(annotationItem["type"])
		newKey := strings.TrimSpace(annotationItem["key"])
		newValue := strings.TrimSpace(annotationItem["value"])
		if actionType == "" || newKey == "" || (actionType != "remove" && newValue == "") {
			return nil, fmt.Errorf("invalid args: %v", annotationItem)
		}

		switch actionType {
		case "update":
			oldAnnotationsCopy[newKey] = newValue
		case "remove":
			delete(oldAnnotationsCopy, newKey)
		default:
			return nil, fmt.Errorf("invalid action type: %s", actionType)
		}
	}

	return oldAnnotationsCopy, nil
}

func BuildPatchAnnotations(annotationItems []map[string]string) (map[string]interface{}, error) {
	patchAnnotations := make(map[string]interface{})

	for _, item := range annotationItems {
		actionType := strings.TrimSpace(item["type"])
		key := strings.TrimSpace(item["key"])
		value := strings.TrimSpace(item["value"])

		if actionType == "" || key == "" || (actionType != "remove" && value == "") {
			return nil, fmt.Errorf("invalid args: %v", item)
		}

		switch actionType {
		case "update":
			patchAnnotations[key] = value
		case "remove":
			patchAnnotations[key] = nil // Patch中删除key的设置方式
		default:
			return nil, fmt.Errorf("invalid action type: %s", actionType)
		}
	}

	return patchAnnotations, nil
}

// 根据需要新增/更新/移除的env，构建新的workload env参数
// addEnvItems 格式示例：[{"type": "update", "key": "key1", "value": "value1"}, {"type": "remove", "key": "key2"}]
// 当type为update时，若key已存在则会更新value值；若key不存在则会添加key value；
// 当type为remove时，若key不存在，则会忽略该条操作
func BuildNewEnvs(oldEnvs []corev1.EnvVar, addEnvItems []map[string]string) ([]corev1.EnvVar, error) {
	oldEnvsCopy := make([]corev1.EnvVar, len(oldEnvs))
	for i, v := range oldEnvs {
		oldEnvsCopy[i] = *v.DeepCopy()
	}

	for _, envItem := range addEnvItems {
		actionType := strings.TrimSpace(envItem["type"])
		newKey := strings.TrimSpace(envItem["key"])
		newValue := strings.TrimSpace(envItem["value"])
		if actionType == "" || newKey == "" || (actionType != "remove" && newValue == "") {
			return nil, fmt.Errorf("invalid args: %v", envItem)
		}

		switch actionType {
		case "update":
			needAdd := true
			for i, oldEnv := range oldEnvsCopy {
				if oldEnv.Name == newKey {
					needAdd = false
					if oldEnv.Value != newValue {
						oldEnvsCopy[i].Value = newValue
					}
					break
				}
			}
			if needAdd {
				oldEnvsCopy = append(oldEnvsCopy, corev1.EnvVar{
					Name:  newKey,
					Value: newValue,
				})
			}
		case "remove":
			for i, oldEnv := range oldEnvsCopy {
				if oldEnv.Name == newKey {
					oldEnvsCopy = append(oldEnvsCopy[:i], oldEnvsCopy[i+1:]...)
					break
				}
			}
		default:
			return nil, fmt.Errorf("invalid action type: %s", actionType)
		}
	}

	return oldEnvsCopy, nil
}

func BuildNewHostAliases(old []corev1.HostAlias, updateItems map[string][]string) []corev1.HostAlias {
	var newHostAliasMap = make(map[string][]string)
	for _, hostAlias := range old {
		newHostAliasMap[hostAlias.IP] = hostAlias.Hostnames
	}

	for ip, domain := range updateItems {
		if len(domain) > 0 {
			newHostAliasMap[ip] = append(newHostAliasMap[ip], domain...)
		}
	}

	var newHostAliases []corev1.HostAlias
	for ip, domain := range newHostAliasMap {
		newHostAlias := corev1.HostAlias{
			IP:        ip,
			Hostnames: RemoveDuplicateStrings(domain),
		}
		newHostAliases = append(newHostAliases, newHostAlias)
	}

	return newHostAliases
}

func RemoveDuplicateStrings(strs []string) []string {
	result := []string{}
	tempMap := map[string]struct{}{}
	for _, str := range strs {
		l := len(tempMap)
		tempMap[str] = struct{}{}
		if len(tempMap) != l {
			result = append(result, str)
		}
	}
	return result
}

func CompareStringSlices(a, b []string) bool {
	if len(a) != len(b) {
		return false
	}
	for i := range a {
		if a[i] != b[i] {
			return false
		}
	}
	return true
}

// MaxSurge returns the maximum surge pods a rolling deployment can take.
func MaxSurge(deployment appsv1.Deployment) int32 {
	if deployment.Spec.Strategy.Type != appsv1.RollingUpdateDeploymentStrategyType {
		return int32(0)
	}
	// Error caught by validation
	maxSurge := deployment.Spec.Strategy.RollingUpdate.MaxSurge
	desired := *(deployment.Spec.Replicas)
	surge, err := intstr.GetValueFromIntOrPercent(intstr.ValueOrDefault(maxSurge, intstr.FromInt(0)), int(desired), true)
	if err != nil {
		klog.Errorf("deployment %s/%s failed to get surge value from int or percent: %v", deployment.Name, deployment.Namespace, err)
		return int32(0)
	}
	return int32(surge)
}

func Base64Decode(enc []byte) ([]byte, error) {
	return io.ReadAll(base64.NewDecoder(base64.StdEncoding, bytes.NewReader(enc)))
}

func DumpResource(resource interface{}) {
	data, _ := json.Marshal(resource)
	klog.V(5).Infof("DumpResource: %s", string(data))
}

// WrapSingleResult 将单个结果包装为数组，用于适配IsReady接口
func WrapSingleResult(rsp *strategyprovider.TaskStrategyReply, err error) ([]*strategyprovider.TaskStrategyReply, error) {
	if err != nil {
		return nil, err
	}
	if rsp == nil {
		return []*strategyprovider.TaskStrategyReply{}, nil
	}
	return []*strategyprovider.TaskStrategyReply{rsp}, nil
}

// CompareNumericValue 根据操作符比较数值和阈值
func CompareNumericValue(value float64, operator string, threshold float64) (bool, error) {
	switch operator {
	case ">":
		return value > threshold, nil
	case ">=":
		return value >= threshold, nil
	case "<":
		return value < threshold, nil
	case "<=":
		return value <= threshold, nil
	case "==":
		return value == threshold, nil
	case "!=":
		return value != threshold, nil
	default:
		return false, fmt.Errorf("unsupported operator: %s", operator)
	}
}

// GetTypedConfigValue 从配置中获取指定类型的值
func GetTypedConfigValue[T any](config map[string]interface{}, key string) (T, bool) {
	var zero T
	value, ok := config[key]
	if !ok {
		return zero, false
	}

	typedValue, ok := value.(T)
	return typedValue, ok
}

// BuildNewConfigMapData 根据ConfigMapDataItem列表构建新的ConfigMap data
// 支持update和remove操作
func BuildNewConfigMapData(originalData map[string]string, operations []model.ConfigMapDataItem) (map[string]string, error) {
	if originalData == nil {
		originalData = make(map[string]string)
	}

	// 创建原始数据的副本
	result := make(map[string]string)
	for k, v := range originalData {
		result[k] = v
	}

	// 应用操作
	for _, op := range operations {
		switch op.Type {
		case model.ConfigMapOperationUpdate:
			result[op.Key] = op.Value
		case model.ConfigMapOperationRemove:
			delete(result, op.Key)
		default:
			return nil, fmt.Errorf("unsupported operation type: %s", op.Type)
		}
	}

	return result, nil
}

// ValidateConfigMapData 验证ConfigMapDataItem列表的合法性
func ValidateConfigMapData(data []model.ConfigMapDataItem) error {

	for i, item := range data {
		if err := validateConfigMapDataItem(item); err != nil {
			return fmt.Errorf("invalid ConfigMapDataItem at index %d: %w", i, err)
		}
	}

	return nil
}

// validateConfigMapDataItem 验证单个ConfigMapDataItem的合法性
func validateConfigMapDataItem(item model.ConfigMapDataItem) error {
	// 检查操作类型
	if item.Type != model.ConfigMapOperationUpdate && item.Type != model.ConfigMapOperationRemove {
		return fmt.Errorf("invalid operation type: %s, must be 'update' or 'remove'", item.Type)
	}

	// 检查key是否为空
	if strings.TrimSpace(item.Key) == "" {
		return fmt.Errorf("key cannot be empty")
	}

	// 检查key格式的合法性
	if !IsValidConfigMapKey(item.Key) {
		return fmt.Errorf("invalid key format: %s", item.Key)
	}

	// 对于update操作，value不能为空
	if item.Type == model.ConfigMapOperationUpdate && strings.TrimSpace(item.Value) == "" {
		return fmt.Errorf("value cannot be empty for update operation")
	}

	return nil
}

// IsValidConfigMapKey 检查ConfigMap key的格式合法性
// ConfigMap key必须符合DNS子域名规范：
// - 只能包含小写字母、数字、'-'、'.'
// - 必须以字母或数字开头和结尾
// - 长度不能超过253个字符
func IsValidConfigMapKey(key string) bool {
	if key == "" {
		return false
	}

	// 检查长度限制
	if len(key) > 253 {
		return false
	}

	// 使用正则表达式验证格式
	// ConfigMap key可以包含字母、数字、连字符、点和下划线
	// 但必须以字母数字字符开头和结尾
	validKeyPattern := regexp.MustCompile(`^[a-zA-Z0-9]([a-zA-Z0-9._-]*[a-zA-Z0-9])?$`)
	return validKeyPattern.MatchString(key)
}
