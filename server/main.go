/*
 *
 * Copyright 2015 gRPC authors.
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 */

// Package main implements a server for Greeter service.
package main

import (
	"context"
	"flag"
	"fmt"
	"net"
	"net/http"

	grpcprom "github.com/grpc-ecosystem/go-grpc-prometheus"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"google.golang.org/grpc"
	"k8s.io/klog/v2"

	"git.woa.com/kmetis/starship/infra"
	"git.woa.com/kmetis/starship/lock"
	pb "git.woa.com/kmetis/starship/pb"
	"git.woa.com/kmetis/starship/pkg/clientset"
	"git.woa.com/kmetis/starship/pkg/config"
	"git.woa.com/kmetis/starship/pkg/healthcheck/node"
	"git.woa.com/kmetis/starship/pkg/healthprovider"
	_ "git.woa.com/kmetis/starship/pkg/healthregister"
	"git.woa.com/kmetis/starship/pkg/metrics"
	greport "git.woa.com/kmetis/starship/pkg/strategy/grpc"
	_ "git.woa.com/kmetis/starship/pkg/strategyregister"
	"git.woa.com/kmetis/starship/pkg/task"
	"git.woa.com/kmetis/starship/pkg/task/util"
	pkgutil "git.woa.com/kmetis/starship/pkg/util"
)

var (
	port        = flag.Int("port", 50056, "The server port")
	metricsPort = flag.Int("metrics-port", 50057, "The metrics port")
	configfile  = flag.String("config", "./config.ini", "The path of config file")
)

// server is used to implement helloworld.GreeterServer.
type server struct {
	pb.UnimplementedHealthCheckerServer
}

// IsComponentHealthy checks if component is healthy
func (s *server) IsComponentHealthy(ctx context.Context, in *pb.ComponentHealthyRequest) (*pb.ComponentHealthyReply, error) {
	klog.Infof("isComponentHealthy: %v", in.GetClusterId()+"-"+in.GetComponentName())
	checker, err := healthprovider.GetComponentHealthProvider(in.GetComponentName(), nil)
	if err != nil {
		klog.Errorf("failed to getComponentHealthProvider %s,%s", in.GetComponentName(), in.GetClusterId())
		return nil, err
	}
	return checker.IsHealthy(ctx, in)
}

// IsComponentHealthy checks if node is healthy
func (s *server) IsNodeHealthy(ctx context.Context, in *pb.NodeHealthyRequest) (*pb.NodeHealthyReply, error) {
	klog.Infof("isNodeHealthy: cluster %s nodes %v", in.GetClusterId(), in.GetNodes())
	if checker := node.GetNodeChecker(); checker != nil {
		return checker.IsNodeHealthy(ctx, in)
	}
	metrics.ClusterNodesCheckSkippedTotal.WithLabelValues(in.ClusterId, in.Product).Add(1)
	return &pb.NodeHealthyReply{Skip: true}, nil
}

// CreateDryRunTask Creates dry run task for specified component
func (s *server) CreateTask(ctx context.Context, in *pb.CreateTaskRequest) (*pb.CreateTaskReply, error) {
	klog.Infof("CreateTask, region: %v, cluster_id: %v, app_name: %v, traceId:%s", in.GetRegion(), in.GetClusterId(), in.GetAppName(), in.GetTraceId())
	return task.CreateTask(ctx, in)
}

// ReportTaskResult report the result of task
func (s *server) ReportTaskResult(ctx context.Context, in *pb.ReportTaskResultRequest) (*pb.ReportTaskResultReply, error) {
	klog.Infof("ReportTaskResult, cluster_id: %s, task-id: %d, subtaskId: %d, app_name: %s, status: %s", in.GetClusterId(), in.GetTaskId(), in.GetSubtaskId(), in.GetAppName(), in.GetStatus())
	return task.ReportTaskResult(ctx, in)
}

// DescribeTask get the result of task
func (s *server) DescribeTask(ctx context.Context, in *pb.DescribeTaskRequest) (*pb.DescribeTaskReply, error) {
	klog.Infof("DescribeTask, cluster-id: %s, app-name: %s, type:%s, change-id:%s, taskId:%d", in.GetClusterId(), in.GetAppName(), in.GetType(), in.GetChangeId(), in.GetTaskId())
	return task.DescribeTask(ctx, in)
}

// CancelTask cancel task
func (s *server) CancelTask(ctx context.Context, in *pb.CancelTaskRequest) (*pb.CancelTaskReply, error) {
	klog.Infof("CancelTask, region: %v, task_id: %v", in.GetRegion(), in.GetTaskId())
	return task.CancelTask(ctx, in)
}

// RollbackTask rollback task
func (s *server) RollbackTask(ctx context.Context, in *pb.RollbackTaskRequest) (*pb.RollbackTaskReply, error) {
	klog.Infof("RollbackTask, region: %v, task_id: %v", in.GetRegion(), in.GetTaskId())
	return task.RollbackTask(ctx, in)
}

// TPReportTaskResult report the result of task
func (s *server) TPReportTaskResult(ctx context.Context, in *pb.TPReportTaskResultRequest) (*pb.TPReportTaskResultReply, error) {
	if in == nil {
		return nil, fmt.Errorf("TPReportTaskResultRequest request is nil")
	}

	klog.Infof("TPReportTaskResult, report_id: %s, task_id: %d, status: %s, code: %d, reason: %s", in.GetReportId(), in.GetTaskId(), in.GetStatus(), in.GetCode(), in.GetReason())
	return greport.ReportTaskResult(ctx, in)
}

func main() {
	klog.InitFlags(nil)
	defer klog.Flush()

	flag.Parse()

	err := config.InitConfig(*configfile)
	if err != nil {
		panic(err)
	}

	infra.InitDbSource(config.GetDatabaseConfig())

	util.InitTask(config.GetRainbowConfig())

	podNamespace, err := pkgutil.GetPodNamespace()
	if err != nil {
		panic(err)
	}

	// 初始化clientset，用来访问platform-api
	if err = clientset.InitClientSet(); err != nil {
		panic(err)
	}

	// 分布式锁，只有抢到锁的执行任务，没有抢到锁可以处理rpc调用任务
	go lock.RunLeaderElection(podNamespace)

	// Setup metrics
	customBuckets := []float64{0.001, 0.01, 0.1, 0.3, 0.6, 1, 3, 6, 9, 20, 30, 60, 90, 120}
	grpcprom.EnableHandlingTimeHistogram(
		grpcprom.WithHistogramBuckets(customBuckets),
	)

	// create grpc server
	lis, err := net.Listen("tcp", fmt.Sprintf(":%d", *port))
	if err != nil {
		klog.Fatalf("failed to listen: %v", err)
	}
	s := grpc.NewServer(
		grpc.UnaryInterceptor(grpcprom.UnaryServerInterceptor),
		grpc.StreamInterceptor(grpcprom.StreamServerInterceptor),
	)
	grpcprom.Register(s)
	pb.RegisterHealthCheckerServer(s, &server{})

	// Create HTTP handler for Prometheus metrics
	go func() error {
		m := http.NewServeMux()
		m.Handle("/metrics", promhttp.HandlerFor(prometheus.DefaultGatherer, promhttp.HandlerOpts{}))
		httpSrv := &http.Server{Handler: m, Addr: fmt.Sprintf(":%d", *metricsPort)}
		klog.Infof("metrics listening at %s", httpSrv.Addr)
		return httpSrv.ListenAndServe()
	}()

	klog.Infof("server listening at %v", lis.Addr())
	if err := s.Serve(lis); err != nil {
		klog.Fatalf("failed to serve: %v", err)
	}
}
