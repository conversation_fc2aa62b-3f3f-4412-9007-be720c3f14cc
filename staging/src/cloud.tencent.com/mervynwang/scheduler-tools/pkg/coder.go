package pkg

import (
	config118v1alpha2 "cloud.tencent.com/mervynwang/scheduler-tools/pkg/schedulerConfig/v1.18.4/v1alpha2"
	config122v1beta1 "cloud.tencent.com/mervynwang/scheduler-tools/pkg/schedulerConfig/v1.22.5/v1beta1"
	config128v1 "cloud.tencent.com/mervynwang/scheduler-tools/pkg/schedulerConfig/v1.28.3/v1"
	config128v1beta3 "cloud.tencent.com/mervynwang/scheduler-tools/pkg/schedulerConfig/v1.28.3/v1beta3"

	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/runtime/serializer"
	"k8s.io/klog/v2"
	"sigs.k8s.io/yaml"
)

var (
	// v1alpha2
	DecoderV1alpha2 runtime.Decoder
	EncoderV1alpha2 runtime.Encoder

	// v1Beta1
	DecoderV1Beta1 runtime.Decoder
	EncoderV1Beta1 runtime.Encoder

	// v1Beta3
	DecoderV1Beta3 runtime.Decoder
	EncoderV1Beta3 runtime.Encoder

	// v1
	DecoderV1 runtime.Decoder
	EncoderV1 runtime.Encoder
)

func init() {
	schedulerScheme := runtime.NewScheme()

	// 注册 v1alpha2
	if err := config118v1alpha2.AddToScheme(schedulerScheme); err != nil {
		klog.Errorln(err)
	}
	// 注册 v1beta1
	if err := config122v1beta1.AddToScheme(schedulerScheme); err != nil {
		klog.Errorln(err)
	}
	// 注册 v1beta3
	if err := config128v1beta3.AddToScheme(schedulerScheme); err != nil {
		klog.Errorln(err)
	}
	// 注册 v1
	if err := config128v1.AddToScheme(schedulerScheme); err != nil {
		klog.Errorln(err)
	}

	codecFactory := serializer.NewCodecFactory(schedulerScheme)

	// v1alpha2 编解码器
	DecoderV1alpha2 = codecFactory.UniversalDeserializer()
	EncoderV1alpha2 = codecFactory.EncoderForVersion(codecFactory.LegacyCodec(config118v1alpha2.SchemeGroupVersion), config118v1alpha2.SchemeGroupVersion)

	// v1beta1 编解码器
	DecoderV1Beta1 = codecFactory.UniversalDeserializer()
	EncoderV1Beta1 = codecFactory.EncoderForVersion(codecFactory.LegacyCodec(config122v1beta1.SchemeGroupVersion), config122v1beta1.SchemeGroupVersion)

	// v1beta3 编解码器
	DecoderV1Beta3 = codecFactory.UniversalDeserializer()
	EncoderV1Beta3 = codecFactory.EncoderForVersion(codecFactory.LegacyCodec(config128v1beta3.SchemeGroupVersion), config128v1beta3.SchemeGroupVersion)

	// v1 编解码器
	DecoderV1 = codecFactory.UniversalDeserializer()
	EncoderV1 = codecFactory.EncoderForVersion(codecFactory.LegacyCodec(config128v1.SchemeGroupVersion), config128v1.SchemeGroupVersion)
}

func EncodeSchedulerCfgToStringYaml(oldSchedulerCfg *config128v1.KubeSchedulerConfiguration) (string, error) {

	schedulerConfigBytes, err := runtime.Encode(EncoderV1, oldSchedulerCfg)
	if err != nil {
		return "", err
	}

	oldSchedulerConfigBytes, err := yaml.JSONToYAML(schedulerConfigBytes)
	if err != nil {
		return "", err
	}
	return string(oldSchedulerConfigBytes), nil
}
