package models

import (
	"fmt"
	"github.com/go-xorm/xorm"
	"k8s.io/klog/v2"
	"time"
)

type ClusterModel struct {
	ID                       uint64      `xorm:"id"`
	AppId                    uint64      `xorm:"appId"`
	ClusterInstanceId        string      `xorm:"clusterInstanceId"`
	Name                     string      `xorm:"name"`
	Description              string      `xorm:"description"`
	LifeState                string      `xorm:"lifeState"`
	TaskState                string      `xorm:"taskState"`
	VpcId                    int64       `xorm:"vpcId"` // 非唯一键，单个VPC下可能会有多个集群
	UniqVpcID                string      `xorm:"uniqVpcID"`
	MasterLbUniqSubnetId     string      `xorm:"masterLbUniqSubnetId"`
	Os                       string      `xorm:"os"`
	ImageId                  string      `xorm:"imageId"`
	ClusterCIDR              string      `xorm:"clusterCIDR"` // K8S CIDR
	ServiceCIDR              string      `xorm:"serviceCIDR"`
	NodeCIDRMaskSize         int         `xorm:"nodeCIDRMaskSize"`
	ExternalEndpoint         *string     `xorm:"externalEndpoint"` // 提供给用户直接外网访问k8s api server
	TgwEndpoint              *string     `xorm:"tgwEndpoint"`      // 外网域名绑定的vip
	VpcLbEndpoint            *string     `xorm:"vpcLbEndpoint"`    // 提供给nodekubelet 访问k8s api server
	MasterEndpoint           *string     `xorm:"masterEndpoint"`   // 用于dashboard访问k8s api server
	MasterListenPort         int         `xorm:"masterListenPort"`
	IsSecure                 int         `xorm:"isSecure"`
	ClusterType              ClusterType `xorm:"isClusterDeploy"`        // 是否是集群化部署 1 是集群化部署，默认是0
	EtcdClusterVpcLbEndpoint string      `xorm:"etcdClusterEndpoint"`    // 用于k8s api server访问etcd
	EtcdClusterJNSGWEndpoint string      `xorm:"etcdClusterJNSEndpoint"` // 用于支撑环境api server访问etcd
	CreatedAt                time.Time   `xorm:"createdAt"`              // 时间信息
	UpdatedAt                time.Time   `xorm:"updatedAt"`
	DeleteAt                 time.Time   `xorm:"deletedAt"`
	OwedAt                   time.Time   `xorm:"owedAt"`
	K8sVersion               string      `xorm:"k8sVersion"`
	DockerVersion            string      `xorm:"dockerVersion"`
	ProjectId                int         `xorm:"projectId"`
	RuntimeConifg            string      `xorm:"runtimeConfig"`

	MonitorStorageId string `xorm:"monitorStorageId"`
	BMonitor         *bool  `xorm:"bMonitor"`

	ExternalNodeConfig string `xorm:"externalNodeConfig"`

	Masters                []string `xorm:"-"`
	Region                 string   `xorm:"-"`
	Password               string   `xorm:"-"`
	ImageRegion            string   `xorm:"-"`
	PromeEniLb             string   `xorm:"-"`
	PromeMemory            string   `xorm:"-"`
	KubeStateMetricVersion string   `xorm:"-"`
	ContainerReName        bool     `xorm:"-"`
}

type ClusterType int

func (c *ClusterModel) TableName() string {
	return "cluster"
}

func FindClusterById(engine *xorm.Engine, clusterId string) (*ClusterModel, error) {
	clusters := make([]*ClusterModel, 0)
	err := engine.Where(fmt.Sprintf("clusterInstanceId='%s'", clusterId)).Find(&clusters)
	if err != nil {
		klog.Errorf("query cluster from db failed. clusterId:%s, err:%v", clusterId, err)
		return nil, err
	}
	if len(clusters) == 0 {
		klog.Errorf("not find cluster in tke, clusterId:%s", clusterId)
		return nil, fmt.Errorf("not find cluster in tke")
	}
	if len(clusters) != 1 {
		klog.Errorf("cluster num not equal to 1 and 0, clusterId:%s", clusterId)
		return nil, fmt.Errorf("cluster num not equal to 1 and 0")
	}
	return clusters[0], nil
}
