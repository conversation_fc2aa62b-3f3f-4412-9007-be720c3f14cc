package models

import (
	"fmt"
	"github.com/go-xorm/xorm"
	"k8s.io/klog/v2"
	"time"
)

type ClusterSecurityModel struct {
	ClusterInstanceId string    `xorm:"clusterInstanceId"`
	CACrt             string    `xorm:"caCrt"`
	AdminPasswd       string    `xorm:"adminPasswd"`
	KubeletPasswd     string    `xorm:"kubeletPasswd"`
	KubeProxyPasswd   string    `xorm:"kubeproxyPasswd"`
	EtcdServerCrt     string    `xorm:"etcdServerCrt"`
	EtcdServerKey     string    `xorm:"etcdServerKey"`
	ServiceAccount    string    `xorm:"serviceAccount"`
	CreatedAt         time.Time `xorm:"createdAt"`
	UpdatedAt         time.Time `xorm:"updatedAt"`
	DeleteAt          time.Time `xorm:"deletedAt"`
}

func (model *ClusterSecurityModel) TableName() string {
	return "clusterSecurity"
}

func FindClusterSecurityByClusterId(engine *xorm.Engine, clusterId string) (*ClusterSecurityModel, error) {
	clustersSecurity := make([]*ClusterSecurityModel, 0)
	err := engine.Where(fmt.Sprintf("clusterInstanceId='%s'", clusterId)).Find(&clustersSecurity)
	if err != nil {
		klog.Errorf("find clusterSecurity from db failed. clusterId:%s,err:%v", clusterId, err)
		return nil, err
	}
	if len(clustersSecurity) != 1 {
		klog.Errorf("clusterSecurity num not equal to 1, clusterId:%s", clusterId)
		return nil, fmt.Errorf("clusterSecurity num not equal to 1")
	}

	return clustersSecurity[0], nil
}
