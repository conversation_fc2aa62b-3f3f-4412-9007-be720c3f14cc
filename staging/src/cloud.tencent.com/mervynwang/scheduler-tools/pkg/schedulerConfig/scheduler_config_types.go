package schedulerConfig

import (
	"encoding/json"
	"fmt"
	"sort"

	"k8s.io/apimachinery/pkg/runtime"
	"sigs.k8s.io/yaml"

	"cloud.tencent.com/mervynwang/scheduler-tools/pkg"
	config118v1 "cloud.tencent.com/mervynwang/scheduler-tools/pkg/schedulerConfig/v1.18.4/v1"
	config118v1alpha2 "cloud.tencent.com/mervynwang/scheduler-tools/pkg/schedulerConfig/v1.18.4/v1alpha2"
	config122v1beta1 "cloud.tencent.com/mervynwang/scheduler-tools/pkg/schedulerConfig/v1.22.5/v1beta1"
	config128v1 "cloud.tencent.com/mervynwang/scheduler-tools/pkg/schedulerConfig/v1.28.3/v1"
	config128v1beta3 "cloud.tencent.com/mervynwang/scheduler-tools/pkg/schedulerConfig/v1.28.3/v1beta3"
	"cloud.tencent.com/mervynwang/scheduler-tools/pkg/utils"

	v1 "k8s.io/api/core/v1"
)

// SchedulerConfig 接口定义了所有配置结构体需要实现的方法
type SchedulerConfig interface {
	// 将cm中调度器的配置文件反序列化到结构体中
	Unmarshal(cm *v1.ConfigMap) error
	// 将结构体序列化到cm中调度器的配置文件中
	Marshal(cm *v1.ConfigMap) error
	// 更新 extender
	UpdateExtender(addExtenders, deleteExtenders []string) error
	// 获取调度器的配置文件的字符串
	GetConfigStr() (string, error)
}

var _ SchedulerConfig = &V1alpha2KubeSchedulerConfiguration{}
var _ SchedulerConfig = &V1beta1KubeSchedulerConfiguration{}
var _ SchedulerConfig = &V1beta3KubeSchedulerConfiguration{}
var _ SchedulerConfig = &V1KubeSchedulerConfiguration{}
var _ SchedulerConfig = &Policyv1{}

// v1alpha2.KubeSchedulerConfiguration
type V1alpha2KubeSchedulerConfiguration struct {
	clusterType string
	k8sVersion  string
	config      *config118v1alpha2.KubeSchedulerConfiguration
}

func (c *V1alpha2KubeSchedulerConfiguration) Marshal(cm *v1.ConfigMap) error {
	// 1. 获取配置文件的 key
	confKey := GetSchedulerConfigDataKeyInConfigMap(c.clusterType, c.k8sVersion)
	// 2. 序列化配置文件
	bytes, err := runtime.Encode(pkg.EncoderV1alpha2, c.config)
	schedulerConfigBytes, err := yaml.JSONToYAML(bytes)
	if err != nil {
		return fmt.Errorf("encode scheduler config to yaml failed. err:%v", err)
	}
	// 3. 更新配置文件
	cm.Data[confKey] = string(schedulerConfigBytes)
	return nil
}
func (c *V1alpha2KubeSchedulerConfiguration) Unmarshal(cm *v1.ConfigMap) error {
	// 1. 获取配置文件的 key
	confKey := GetSchedulerConfigDataKeyInConfigMap(c.clusterType, c.k8sVersion)
	// 2. 拿出配置 string
	oldCfgStr := cm.Data[confKey]
	// 3. 反序列化配置文件
	_, _, err := pkg.DecoderV1alpha2.Decode([]byte(oldCfgStr), nil, c.config)
	if err != nil {
		return fmt.Errorf("decode configMap failed. err:%v", err)
	}
	return nil
}
func (c *V1alpha2KubeSchedulerConfiguration) GetConfigStr() (string, error) {
	// 序列化配置文件
	bytes, err := runtime.Encode(pkg.EncoderV1alpha2, c.config)
	if err != nil {
		return "", fmt.Errorf("encode scheduler config to yaml failed. err:%v", err)
	}
	schedulerConfigBytes, err := yaml.JSONToYAML(bytes)
	return string(schedulerConfigBytes), nil
}
func (c *V1alpha2KubeSchedulerConfiguration) UpdateExtender(addExtenders, deleteExtenders []string) error {
	// 前置判断
	if len(addExtenders) == 0 && len(deleteExtenders) == 0 {
		return nil
	}
	delMap := map[string]bool{}
	for _, e := range deleteExtenders {
		delMap[e] = true
	}

	// AddExtenders 和 DeleteExtenders 中的元素是 extender 配置的 json 字符串
	// 添加 extender，如果原来已经有对应 url 的 extender，就覆盖掉
	// 新增的 extender 会放在列表最前面
	newExtenders := []config118v1.Extender{}
l1:
	for _, e := range addExtenders {
		item := config118v1.Extender{}
		if err := json.Unmarshal([]byte(e), &item); err != nil {
			return err
		}
		for i, o := range c.config.Extenders {
			if o.URLPrefix == item.URLPrefix {
				c.config.Extenders[i] = item
				continue l1
			}
		}

		newExtenders = append(newExtenders, item)
	}

	for _, o := range c.config.Extenders {
		if !delMap[o.URLPrefix] {
			newExtenders = append(newExtenders, o)
		}
	}

	// 为确保 eklet-extender 在 ipamd-extender 之前，将 eklet 放到最前面，ipamd 放到最后面。
	sort.SliceStable(newExtenders, func(i, j int) bool {
		// eklet 放到最前面
		if utils.IsEkletExtender(newExtenders[i]) {
			return true
		}
		if utils.IsEkletExtender(newExtenders[j]) {
			return false
		}
		// ipamd 放到最后面
		if utils.IsIpamdExtender(newExtenders[i]) {
			return false
		}
		if utils.IsIpamdExtender(newExtenders[j]) {
			return true
		}

		// 其他情况顺序不变
		return false
	})

	c.config.Extenders = newExtenders

	return nil
}

// v1beta1.KubeSchedulerConfiguration
type V1beta1KubeSchedulerConfiguration struct {
	clusterType string
	k8sVersion  string
	config      *config122v1beta1.KubeSchedulerConfiguration
}

func (c *V1beta1KubeSchedulerConfiguration) Marshal(cm *v1.ConfigMap) error {
	// 1. 获取配置文件的 key
	confKey := GetSchedulerConfigDataKeyInConfigMap(c.clusterType, c.k8sVersion)
	// 2. 序列化配置文件
	bytes, err := runtime.Encode(pkg.EncoderV1Beta1, c.config)
	schedulerConfigBytes, err := yaml.JSONToYAML(bytes)
	if err != nil {
		return fmt.Errorf("encode scheduler config to yaml failed. err:%v", err)
	}
	// 3. 更新配置文件
	cm.Data[confKey] = string(schedulerConfigBytes)
	return nil
}
func (c *V1beta1KubeSchedulerConfiguration) Unmarshal(cm *v1.ConfigMap) error {
	// 1. 获取配置文件的 key
	confKey := GetSchedulerConfigDataKeyInConfigMap(c.clusterType, c.k8sVersion)
	// 2. 拿出配置 string
	oldCfgStr := cm.Data[confKey]
	// 3. 反序列化配置文件
	_, _, err := pkg.DecoderV1Beta1.Decode([]byte(oldCfgStr), nil, c.config)
	if err != nil {
		return fmt.Errorf("decode configMap failed. err:%v", err)
	}
	return nil
}
func (c *V1beta1KubeSchedulerConfiguration) GetConfigStr() (string, error) {
	// 序列化配置文件
	bytes, err := runtime.Encode(pkg.EncoderV1Beta1, c.config)
	if err != nil {
		return "", fmt.Errorf("encode scheduler config to yaml failed. err:%v", err)
	}
	schedulerConfigBytes, err := yaml.JSONToYAML(bytes)
	return string(schedulerConfigBytes), nil
}
func (c *V1beta1KubeSchedulerConfiguration) UpdateExtender(addExtenders, deleteExtenders []string) error {
	// 前置判断
	if len(addExtenders) == 0 && len(deleteExtenders) == 0 {
		return nil
	}
	delMap := map[string]bool{}
	for _, e := range deleteExtenders {
		delMap[e] = true
	}

	// AddExtenders 和 DeleteExtenders 中的元素是 extender 配置的 json 字符串
	// 添加 extender，如果原来已经有对应 url 的 extender，就覆盖掉
	// 新增的 extender 会放在列表最前面
	newExtenders := []config122v1beta1.Extender{}
l1:
	for _, e := range addExtenders {
		item := config122v1beta1.Extender{}
		if err := json.Unmarshal([]byte(e), &item); err != nil {
			return err
		}
		for i, o := range c.config.Extenders {
			if o.URLPrefix == item.URLPrefix {
				c.config.Extenders[i] = item
				continue l1
			}
		}

		newExtenders = append(newExtenders, item)
	}

	for _, o := range c.config.Extenders {
		if !delMap[o.URLPrefix] {
			newExtenders = append(newExtenders, o)
		}
	}

	// 为确保 eklet-extender 在 ipamd-extender 之前，将 eklet 放到最前面，ipamd 放到最后面。
	sort.SliceStable(newExtenders, func(i, j int) bool {
		// eklet 放到最前面
		if utils.IsEkletExtender(newExtenders[i]) {
			return true
		}
		if utils.IsEkletExtender(newExtenders[j]) {
			return false
		}
		// ipamd 放到最后面
		if utils.IsIpamdExtender(newExtenders[i]) {
			return false
		}
		if utils.IsIpamdExtender(newExtenders[j]) {
			return true
		}

		// 其他情况顺序不变
		return false
	})

	c.config.Extenders = newExtenders

	return nil
}

// v1beta3.KubeSchedulerConfiguration
type V1beta3KubeSchedulerConfiguration struct {
	clusterType string
	k8sVersion  string
	config      *config128v1beta3.KubeSchedulerConfiguration
}

func (c *V1beta3KubeSchedulerConfiguration) Marshal(cm *v1.ConfigMap) error {
	// 1. 获取配置文件的 key
	confKey := GetSchedulerConfigDataKeyInConfigMap(c.clusterType, c.k8sVersion)
	// 2. 序列化配置文件
	bytes, err := runtime.Encode(pkg.EncoderV1Beta3, c.config)
	schedulerConfigBytes, err := yaml.JSONToYAML(bytes)
	if err != nil {
		return fmt.Errorf("encode scheduler config to yaml failed. err:%v", err)
	}
	// 3. 更新配置文件
	cm.Data[confKey] = string(schedulerConfigBytes)
	return nil
}
func (c *V1beta3KubeSchedulerConfiguration) Unmarshal(cm *v1.ConfigMap) error {
	// 1. 获取配置文件的 key
	confKey := GetSchedulerConfigDataKeyInConfigMap(c.clusterType, c.k8sVersion)
	// 2. 拿出配置 string
	oldCfgStr := cm.Data[confKey]
	// 3. 反序列化配置文件
	_, _, err := pkg.DecoderV1Beta3.Decode([]byte(oldCfgStr), nil, c.config)
	if err != nil {
		return fmt.Errorf("decode configMap failed. err:%v", err)
	}
	return nil
}
func (c *V1beta3KubeSchedulerConfiguration) GetConfigStr() (string, error) {
	// 序列化配置文件
	bytes, err := runtime.Encode(pkg.EncoderV1Beta3, c.config)
	if err != nil {
		return "", fmt.Errorf("encode scheduler config to yaml failed. err:%v", err)
	}
	schedulerConfigBytes, err := yaml.JSONToYAML(bytes)
	return string(schedulerConfigBytes), nil
}
func (c *V1beta3KubeSchedulerConfiguration) UpdateExtender(addExtenders, deleteExtenders []string) error {
	// 前置判断
	if len(addExtenders) == 0 && len(deleteExtenders) == 0 {
		return nil
	}

	delMap := map[string]bool{}
	for _, e := range deleteExtenders {
		delMap[e] = true
	}

	// AddExtenders 和 DeleteExtenders 中的元素是 extender 配置的 json 字符串
	// 添加 extender，如果原来已经有对应 url 的 extender，就覆盖掉
	// 新增的 extender 会放在列表最前面
	newExtenders := []config128v1beta3.Extender{}
l1:
	for _, e := range addExtenders {
		item := config128v1beta3.Extender{}
		if err := json.Unmarshal([]byte(e), &item); err != nil {
			return err
		}
		for i, o := range c.config.Extenders {
			if o.URLPrefix == item.URLPrefix {
				c.config.Extenders[i] = item
				continue l1
			}
		}

		newExtenders = append(newExtenders, item)
	}

	for _, o := range c.config.Extenders {
		if !delMap[o.URLPrefix] {
			newExtenders = append(newExtenders, o)
		}
	}

	// 为确保 eklet-extender 在 ipamd-extender 之前，将 eklet 放到最前面，ipamd 放到最后面。
	sort.SliceStable(newExtenders, func(i, j int) bool {
		// eklet 放到最前面
		if utils.IsEkletExtender(newExtenders[i]) {
			return true
		}
		if utils.IsEkletExtender(newExtenders[j]) {
			return false
		}
		// ipamd 放到最后面
		if utils.IsIpamdExtender(newExtenders[i]) {
			return false
		}
		if utils.IsIpamdExtender(newExtenders[j]) {
			return true
		}

		// 其他情况顺序不变
		return false
	})

	c.config.Extenders = newExtenders

	return nil
}

// v1.KubeSchedulerConfiguration
type V1KubeSchedulerConfiguration struct {
	clusterType string
	k8sVersion  string
	config      *config128v1.KubeSchedulerConfiguration
}

func (c *V1KubeSchedulerConfiguration) Marshal(cm *v1.ConfigMap) error {
	// 1. 获取配置文件的 key
	confKey := GetSchedulerConfigDataKeyInConfigMap(c.clusterType, c.k8sVersion)
	// 2. 序列化配置文件
	bytes, err := runtime.Encode(pkg.EncoderV1, c.config)
	schedulerConfigBytes, err := yaml.JSONToYAML(bytes)
	if err != nil {
		return fmt.Errorf("encode scheduler config to yaml failed. err:%v", err)
	}
	// 3. 更新配置文件
	cm.Data[confKey] = string(schedulerConfigBytes)
	return nil
}
func (c *V1KubeSchedulerConfiguration) Unmarshal(cm *v1.ConfigMap) error {
	// 1. 获取配置文件的 key
	confKey := GetSchedulerConfigDataKeyInConfigMap(c.clusterType, c.k8sVersion)
	// 2. 拿出配置 string
	oldCfgStr := cm.Data[confKey]
	// 3. 反序列化配置文件
	_, _, err := pkg.DecoderV1.Decode([]byte(oldCfgStr), nil, c.config)
	if err != nil {
		return fmt.Errorf("decode configMap failed. err:%v", err)
	}
	return nil
}
func (c *V1KubeSchedulerConfiguration) GetConfigStr() (string, error) {
	// 序列化配置文件
	bytes, err := runtime.Encode(pkg.EncoderV1, c.config)
	if err != nil {
		return "", fmt.Errorf("encode scheduler config to yaml failed. err:%v", err)
	}
	schedulerConfigBytes, err := yaml.JSONToYAML(bytes)
	return string(schedulerConfigBytes), nil
}
func (c *V1KubeSchedulerConfiguration) UpdateExtender(addExtenders, deleteExtenders []string) error {
	// 前置判断
	if len(addExtenders) == 0 && len(deleteExtenders) == 0 {
		return nil
	}
	delMap := map[string]bool{}
	for _, e := range deleteExtenders {
		delMap[e] = true
	}

	// AddExtenders 和 DeleteExtenders 中的元素是 extender 配置的 json 字符串
	// 添加 extender，如果原来已经有对应 url 的 extender，就覆盖掉
	// 新增的 extender 会放在列表最前面
	newExtenders := []config128v1.Extender{}
l1:
	for _, e := range addExtenders {
		item := config128v1.Extender{}
		if err := json.Unmarshal([]byte(e), &item); err != nil {
			return err
		}
		for i, o := range c.config.Extenders {
			if o.URLPrefix == item.URLPrefix {
				c.config.Extenders[i] = item
				continue l1
			}
		}

		newExtenders = append(newExtenders, item)
	}

	for _, o := range c.config.Extenders {
		if !delMap[o.URLPrefix] {
			newExtenders = append(newExtenders, o)
		}
	}

	// 为确保 eklet-extender 在 ipamd-extender 之前，将 eklet 放到最前面，ipamd 放到最后面。
	sort.SliceStable(newExtenders, func(i, j int) bool {
		// eklet 放到最前面
		if utils.IsEkletExtender(newExtenders[i]) {
			return true
		}
		if utils.IsEkletExtender(newExtenders[j]) {
			return false
		}
		// ipamd 放到最后面
		if utils.IsIpamdExtender(newExtenders[i]) {
			return false
		}
		if utils.IsIpamdExtender(newExtenders[j]) {
			return true
		}

		// 其他情况顺序不变
		return false
	})

	c.config.Extenders = newExtenders
	return nil
}

// v1.Policy
type Policyv1 struct {
	clusterType string
	k8sVersion  string
	config      *config118v1.Policy
}

func (c *Policyv1) Marshal(cm *v1.ConfigMap) error {
	// 1. 获取配置文件的 key
	confKey := GetSchedulerConfigDataKeyInConfigMap(c.clusterType, c.k8sVersion)
	// 2. 序列化配置文件
	bytes, err := json.Marshal(c.config)
	if err != nil {
		return fmt.Errorf("encode scheduler policy to json failed. err:%v", err)
	}
	// 3. 更新配置文件
	cm.Data[confKey] = string(bytes)
	return nil
}
func (c *Policyv1) Unmarshal(cm *v1.ConfigMap) error {
	// 1. 获取配置文件的 key
	confKey := GetSchedulerConfigDataKeyInConfigMap(c.clusterType, c.k8sVersion)
	// 2. 拿出配置 string
	oldCfgStr := cm.Data[confKey]
	// 3. 反序列化配置文件
	err := json.Unmarshal([]byte(oldCfgStr), c.config)
	if err != nil {
		return fmt.Errorf("failed to unmarshal config: %v", err)
	}

	return nil
}
func (c *Policyv1) GetConfigStr() (string, error) {
	// 序列化配置文件
	bytes, err := json.Marshal(c.config)
	if err != nil {
		return "", fmt.Errorf("encode scheduler policy to json failed. err:%v", err)
	}
	return string(bytes), nil
}
func (c *Policyv1) UpdateExtender(addExtenders, deleteExtenders []string) error {
	// 前置判断
	if len(addExtenders) == 0 && len(deleteExtenders) == 0 {
		return nil
	}
	delMap := map[string]bool{}
	for _, e := range deleteExtenders {
		delMap[e] = true
	}

	// AddExtenders 和 DeleteExtenders 中的元素是 extender 配置的 json 字符串
	// 添加 extender，如果原来已经有对应 url 的 extender，就覆盖掉
	// 新增的 extender 会放在列表最前面
	newExtenders := []config118v1.Extender{}
l1:
	for _, e := range addExtenders {
		item := config118v1.Extender{}
		if err := json.Unmarshal([]byte(e), &item); err != nil {
			return err
		}
		for i, o := range c.config.Extenders {
			if o.URLPrefix == item.URLPrefix {
				c.config.Extenders[i] = item
				continue l1
			}
		}

		newExtenders = append(newExtenders, item)
	}

	for _, o := range c.config.Extenders {
		if !delMap[o.URLPrefix] {
			newExtenders = append(newExtenders, o)
		}
	}

	// 为确保 eklet-extender 在 ipamd-extender 之前，将 eklet 放到最前面，ipamd 放到最后面。
	sort.SliceStable(newExtenders, func(i, j int) bool {
		// eklet 放到最前面
		if utils.IsEkletExtender(newExtenders[i]) {
			return true
		}
		if utils.IsEkletExtender(newExtenders[j]) {
			return false
		}
		// ipamd 放到最后面
		if utils.IsIpamdExtender(newExtenders[i]) {
			return false
		}
		if utils.IsIpamdExtender(newExtenders[j]) {
			return true
		}

		// 其他情况顺序不变
		return false
	})

	c.config.Extenders = newExtenders
	return nil
}

func NewSchedulerConfig(clusterType, k8sVersion string) (SchedulerConfig, error) {
	switch clusterType {
	case pkg.EksClusterType:
		if k8sVersion == pkg.VERSION18 {
			return &V1alpha2KubeSchedulerConfiguration{
				clusterType: pkg.EksClusterType,
				k8sVersion:  k8sVersion,
				config:      &config118v1alpha2.KubeSchedulerConfiguration{},
			}, nil
		}
		if k8sVersion == pkg.VERSION20 || k8sVersion == pkg.VERSION22 {
			return &V1beta1KubeSchedulerConfiguration{
				clusterType: pkg.EksClusterType,
				k8sVersion:  k8sVersion,
				config:      &config122v1beta1.KubeSchedulerConfiguration{},
			}, nil
		}
		if k8sVersion == pkg.VERSION24 {
			return &V1beta3KubeSchedulerConfiguration{
				clusterType: pkg.EksClusterType,
				k8sVersion:  k8sVersion,
				config:      &config128v1beta3.KubeSchedulerConfiguration{},
			}, nil
		}
	case pkg.TkeClusterType:
		if k8sVersion == pkg.VERSION18 || k8sVersion == pkg.VERSION20 || k8sVersion == pkg.VERSION22 {
			return &Policyv1{
				clusterType: pkg.TkeClusterType,
				k8sVersion:  k8sVersion,
				config:      &config118v1.Policy{},
			}, nil
		}
		if k8sVersion == pkg.VERSION24 || k8sVersion == pkg.VERSION26 || k8sVersion == pkg.VERSION28 {
			return &V1beta3KubeSchedulerConfiguration{
				clusterType: pkg.TkeClusterType,
				k8sVersion:  k8sVersion,
				config:      &config128v1beta3.KubeSchedulerConfiguration{},
			}, nil
		}
		if k8sVersion == pkg.VERSION30 {
			return &V1KubeSchedulerConfiguration{
				clusterType: pkg.TkeClusterType,
				k8sVersion:  k8sVersion,
				config:      &config128v1.KubeSchedulerConfiguration{},
			}, nil
		}
	default:
		return nil, fmt.Errorf("unsupported cluster type: %s", clusterType)
	}
	return nil, fmt.Errorf("unsupported Kubernetes version: %s for cluster type: %s", k8sVersion, clusterType)
}

// 返回 configmap 中存储配置的 key
func GetSchedulerConfigDataKeyInConfigMap(clusterType, k8sVersion string) string {
	if (clusterType == pkg.TkeClusterType) && (k8sVersion == pkg.VERSION18 || k8sVersion == pkg.VERSION20 || k8sVersion == pkg.VERSION22) {
		return pkg.PolicyConfig
	}
	return pkg.ConfigurationConfig
}
