package utils

import (
	config128v1 "cloud.tencent.com/mervynwang/scheduler-tools/pkg/schedulerConfig/v1.28.3/v1"
	config128v1beta3 "cloud.tencent.com/mervynwang/scheduler-tools/pkg/schedulerConfig/v1.28.3/v1beta3"
	"strings"

	config118v1 "cloud.tencent.com/mervynwang/scheduler-tools/pkg/schedulerConfig/v1.18.4/v1"
	config122v1beta1 "cloud.tencent.com/mervynwang/scheduler-tools/pkg/schedulerConfig/v1.22.5/v1beta1"
)

func IsEkletExtender(extender interface{}) bool {
	if extender == nil {
		return false
	}

	var filterVerb, prioritizeVerb string
	switch e := extender.(type) {
	case *config118v1.Extender:
		filterVerb, prioritizeVerb = e.FilterVerb, e.PrioritizeVerb
	case *config122v1beta1.Extender:
		filterVerb, prioritizeVerb = e.FilterVerb, e.PrioritizeVerb
	case *config128v1beta3.Extender:
		filterVerb, prioritizeVerb = e.FilterVerb, e.PrioritizeVerb
	case *config128v1.Extender:
		filterVerb, prioritizeVerb = e.FilterVerb, e.PrioritizeVerb
	default:
		return false
	}

	return strings.Contains(filterVerb, "eklet") || strings.Contains(prioritizeVerb, "eklet")
}

func IsIpamdExtender(extender interface{}) bool {
	if extender == nil {
		return false
	}

	var managedResources []config118v1.ExtenderManagedResource
	var managedResourcesv1beta3 []config128v1beta3.ExtenderManagedResource
	var managedResourcesv1 []config128v1.ExtenderManagedResource

	switch e := extender.(type) {
	case *config118v1.Extender:
		managedResources = e.ManagedResources
	case *config128v1beta3.Extender:
		managedResourcesv1beta3 = e.ManagedResources
	case *config128v1.Extender:
		managedResourcesv1 = e.ManagedResources
	default:
		return false
	}

	if managedResources != nil {
		for _, resource := range managedResources {
			if strings.Contains(resource.Name, "ipamd") {
				return true
			}
		}
	}

	if managedResourcesv1beta3 != nil {
		for _, resource := range managedResourcesv1beta3 {
			if strings.Contains(resource.Name, "ipamd") {
				return true
			}
		}
	}

	if managedResourcesv1 != nil {
		for _, resource := range managedResourcesv1 {
			if strings.Contains(resource.Name, "ipamd") {
				return true
			}
		}
	}

	return false
}
