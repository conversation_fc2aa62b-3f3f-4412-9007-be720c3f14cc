package utils

import (
	"bufio"
	"cloud.tencent.com/mervynwang/scheduler-tools/pkg/models"
	"errors"
	"fmt"
	"github.com/go-xorm/xorm"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/klog/v2"
	"os"
	"strings"
)

func ParseDBsInfo(dbPath string) (map[string]string, error) { // map[shortRegion]dbConnectInfo, error
	var file, err = os.OpenFile(dbPath, os.O_RDWR, 0666)
	if err != nil {
		klog.Errorf("os.OpenFile failed. err:%v", err)
		return nil, err
	}
	defer file.Close()

	res := make(map[string]string, 0)
	buf := bufio.NewScanner(file)
	for {
		if !buf.Scan() {
			break
		}
		line := buf.Text()
		dbInfo := strings.Split(line, ":")
		if len(dbInfo) != 5 {
			klog.Errorf("dbInfos:%v", dbInfo)
			return nil, fmt.Errorf("line parse res is not ok")
		}
		region := dbInfo[0]
		ip := dbInfo[1]
		port := dbInfo[2]
		user := dbInfo[3]
		pwd := dbInfo[4]
		res[region] = fmt.Sprintf("%s:%s@tcp(%s:%s)/dashboard", user, pwd, ip, port)
	}

	return res, nil
}

func GetClientSetByDBInfo(region, DBInfo, metaclusterId string) (*kubernetes.Clientset, error) {
	//解析数据库信息
	dbInfos, err := ParseDBsInfo(DBInfo)
	if err != nil {
		return &kubernetes.Clientset{}, err
	}

	//指定地域 的 dbInfo
	dbInfo, exists := dbInfos[region]
	if !exists {
		return &kubernetes.Clientset{}, errors.New(fmt.Sprintf("region %s dbinfo not found", region))
	}

	//获取数据库句柄
	engine, err := xorm.NewEngine("mysql", dbInfo)
	if err != nil {
		return &kubernetes.Clientset{}, errors.New(fmt.Sprintf("new db engine failed, err:%v", err))
	}

	metaClusterConfig, err := getRestConfig(engine, metaclusterId)
	if err != nil {
		return &kubernetes.Clientset{}, errors.New(fmt.Sprintf("get metaClusterConfig failed, err:%v", err))
	}
	metaClientSet, err := kubernetes.NewForConfig(metaClusterConfig)
	if err != nil {
		return &kubernetes.Clientset{}, errors.New(fmt.Sprintf("get clientSet failed. err:%v", err))
	}
	return metaClientSet, nil
}

func getRestConfig(engine *xorm.Engine, clusterId string) (*rest.Config, error) {
	// 查询cluster信息
	cluster, err := models.FindClusterById(engine, clusterId)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("find cluster info failed. region:, clusterId:%s, err:%v", clusterId, err))
	}
	// 查询cluster password信息
	clusterSecurity, err := models.FindClusterSecurityByClusterId(engine, clusterId)
	if err != nil {
		return nil, errors.New(fmt.Sprintf("find cluster security failed. region:, clusterId:%s, err:%v", clusterId, err))
	}
	//
	clusterEp := ""
	if cluster.MasterEndpoint == nil {
		return nil, errors.New(fmt.Sprintf("region:  cluster %s masterEndpoint is nil", clusterId))
	}
	clusterEp = *(cluster.MasterEndpoint)
	restConfig := &rest.Config{
		Host:        clusterEp,
		BearerToken: clusterSecurity.AdminPasswd,
		TLSClientConfig: rest.TLSClientConfig{
			Insecure: true,
		},
	}
	return restConfig, err
}
