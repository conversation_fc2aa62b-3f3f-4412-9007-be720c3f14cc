/*
Copyright 2023 TencentCloud.
*/
// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1alpha1 "cloud.tencent.com/tke/master-operator/pkg/client/clientset/versioned/typed/master.cloud.tencent.com/v1alpha1"
	rest "k8s.io/client-go/rest"
	testing "k8s.io/client-go/testing"
)

type FakeMasterV1alpha1 struct {
	*testing.Fake
}

func (c *FakeMasterV1alpha1) Masters(namespace string) v1alpha1.MasterInterface {
	return &FakeMasters{c, namespace}
}

func (c *FakeMasterV1alpha1) MasterSnapshots(namespace string) v1alpha1.MasterSnapshotInterface {
	return &FakeMasterSnapshots{c, namespace}
}

// RESTClient returns a RESTClient that is used to communicate
// with API server by this client implementation.
func (c *FakeMasterV1alpha1) RESTClient() rest.Interface {
	var ret *rest.RESTClient
	return ret
}
