/*
Copyright 2023 TencentCloud.
*/
// Code generated by client-gen. DO NOT EDIT.

package v1alpha1

import (
	v1alpha1 "cloud.tencent.com/tke/master-operator/pkg/apis/master.cloud.tencent.com/v1alpha1"
	"cloud.tencent.com/tke/master-operator/pkg/client/clientset/versioned/scheme"
	rest "k8s.io/client-go/rest"
)

type MasterV1alpha1Interface interface {
	RESTClient() rest.Interface
	MastersGetter
	MasterSnapshotsGetter
}

// MasterV1alpha1Client is used to interact with features provided by the master.cloud.tencent.com group.
type MasterV1alpha1Client struct {
	restClient rest.Interface
}

func (c *MasterV1alpha1Client) Masters(namespace string) MasterInterface {
	return newMasters(c, namespace)
}

func (c *MasterV1alpha1Client) MasterSnapshots(namespace string) MasterSnapshotInterface {
	return newMasterSnapshots(c, namespace)
}

// NewForConfig creates a new MasterV1alpha1Client for the given config.
func NewForConfig(c *rest.Config) (*MasterV1alpha1Client, error) {
	config := *c
	if err := setConfigDefaults(&config); err != nil {
		return nil, err
	}
	client, err := rest.RESTClientFor(&config)
	if err != nil {
		return nil, err
	}
	return &MasterV1alpha1Client{client}, nil
}

// NewForConfigOrDie creates a new MasterV1alpha1Client for the given config and
// panics if there is an error in the config.
func NewForConfigOrDie(c *rest.Config) *MasterV1alpha1Client {
	client, err := NewForConfig(c)
	if err != nil {
		panic(err)
	}
	return client
}

// New creates a new MasterV1alpha1Client for the given RESTClient.
func New(c rest.Interface) *MasterV1alpha1Client {
	return &MasterV1alpha1Client{c}
}

func setConfigDefaults(config *rest.Config) error {
	gv := v1alpha1.SchemeGroupVersion
	config.GroupVersion = &gv
	config.APIPath = "/apis"
	config.NegotiatedSerializer = scheme.Codecs.WithoutConversion()

	if config.UserAgent == "" {
		config.UserAgent = rest.DefaultKubernetesUserAgent()
	}

	return nil
}

// RESTClient returns a RESTClient that is used to communicate
// with API server by this client implementation.
func (c *MasterV1alpha1Client) RESTClient() rest.Interface {
	if c == nil {
		return nil
	}
	return c.restClient
}
