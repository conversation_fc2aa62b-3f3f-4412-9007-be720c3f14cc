/*
Copyright 2023 TencentCloud.
*/
// Code generated by client-gen. DO NOT EDIT.

package v1alpha1

import (
	"context"
	"time"

	v1alpha1 "cloud.tencent.com/tke/master-operator/pkg/apis/master.cloud.tencent.com/v1alpha1"
	scheme "cloud.tencent.com/tke/master-operator/pkg/client/clientset/versioned/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// MasterSnapshotsGetter has a method to return a MasterSnapshotInterface.
// A group's client should implement this interface.
type MasterSnapshotsGetter interface {
	MasterSnapshots(namespace string) MasterSnapshotInterface
}

// MasterSnapshotInterface has methods to work with MasterSnapshot resources.
type MasterSnapshotInterface interface {
	Create(ctx context.Context, masterSnapshot *v1alpha1.MasterSnapshot, opts v1.CreateOptions) (*v1alpha1.MasterSnapshot, error)
	Update(ctx context.Context, masterSnapshot *v1alpha1.MasterSnapshot, opts v1.UpdateOptions) (*v1alpha1.MasterSnapshot, error)
	Delete(ctx context.Context, name string, opts v1.DeleteOptions) error
	DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error
	Get(ctx context.Context, name string, opts v1.GetOptions) (*v1alpha1.MasterSnapshot, error)
	List(ctx context.Context, opts v1.ListOptions) (*v1alpha1.MasterSnapshotList, error)
	Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error)
	Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.MasterSnapshot, err error)
	MasterSnapshotExpansion
}

// masterSnapshots implements MasterSnapshotInterface
type masterSnapshots struct {
	client rest.Interface
	ns     string
}

// newMasterSnapshots returns a MasterSnapshots
func newMasterSnapshots(c *MasterV1alpha1Client, namespace string) *masterSnapshots {
	return &masterSnapshots{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the masterSnapshot, and returns the corresponding masterSnapshot object, and an error if there is any.
func (c *masterSnapshots) Get(ctx context.Context, name string, options v1.GetOptions) (result *v1alpha1.MasterSnapshot, err error) {
	result = &v1alpha1.MasterSnapshot{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("mastersnapshots").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(ctx).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of MasterSnapshots that match those selectors.
func (c *masterSnapshots) List(ctx context.Context, opts v1.ListOptions) (result *v1alpha1.MasterSnapshotList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1alpha1.MasterSnapshotList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("mastersnapshots").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(ctx).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested masterSnapshots.
func (c *masterSnapshots) Watch(ctx context.Context, opts v1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("mastersnapshots").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(ctx)
}

// Create takes the representation of a masterSnapshot and creates it.  Returns the server's representation of the masterSnapshot, and an error, if there is any.
func (c *masterSnapshots) Create(ctx context.Context, masterSnapshot *v1alpha1.MasterSnapshot, opts v1.CreateOptions) (result *v1alpha1.MasterSnapshot, err error) {
	result = &v1alpha1.MasterSnapshot{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("mastersnapshots").
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(masterSnapshot).
		Do(ctx).
		Into(result)
	return
}

// Update takes the representation of a masterSnapshot and updates it. Returns the server's representation of the masterSnapshot, and an error, if there is any.
func (c *masterSnapshots) Update(ctx context.Context, masterSnapshot *v1alpha1.MasterSnapshot, opts v1.UpdateOptions) (result *v1alpha1.MasterSnapshot, err error) {
	result = &v1alpha1.MasterSnapshot{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("mastersnapshots").
		Name(masterSnapshot.Name).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(masterSnapshot).
		Do(ctx).
		Into(result)
	return
}

// Delete takes name of the masterSnapshot and deletes it. Returns an error if one occurs.
func (c *masterSnapshots) Delete(ctx context.Context, name string, opts v1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("mastersnapshots").
		Name(name).
		Body(&opts).
		Do(ctx).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *masterSnapshots) DeleteCollection(ctx context.Context, opts v1.DeleteOptions, listOpts v1.ListOptions) error {
	var timeout time.Duration
	if listOpts.TimeoutSeconds != nil {
		timeout = time.Duration(*listOpts.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Namespace(c.ns).
		Resource("mastersnapshots").
		VersionedParams(&listOpts, scheme.ParameterCodec).
		Timeout(timeout).
		Body(&opts).
		Do(ctx).
		Error()
}

// Patch applies the patch and returns the patched masterSnapshot.
func (c *masterSnapshots) Patch(ctx context.Context, name string, pt types.PatchType, data []byte, opts v1.PatchOptions, subresources ...string) (result *v1alpha1.MasterSnapshot, err error) {
	result = &v1alpha1.MasterSnapshot{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("mastersnapshots").
		Name(name).
		SubResource(subresources...).
		VersionedParams(&opts, scheme.ParameterCodec).
		Body(data).
		Do(ctx).
		Into(result)
	return
}
