/*
Copyright 2023 TencentCloud.
*/
// Code generated by informer-gen. DO NOT EDIT.

package v1alpha1

import (
	internalinterfaces "cloud.tencent.com/tke/master-operator/pkg/client/informers/externalversions/internalinterfaces"
)

// Interface provides access to all the informers in this group version.
type Interface interface {
	// Masters returns a MasterInformer.
	Masters() MasterInformer
	// MasterSnapshots returns a MasterSnapshotInformer.
	MasterSnapshots() MasterSnapshotInformer
}

type version struct {
	factory          internalinterfaces.SharedInformerFactory
	namespace        string
	tweakListOptions internalinterfaces.TweakListOptionsFunc
}

// New returns a new Interface.
func New(f internalinterfaces.SharedInformerFactory, namespace string, tweakListOptions internalinterfaces.TweakListOptionsFunc) Interface {
	return &version{factory: f, namespace: namespace, tweakListOptions: tweakListOptions}
}

// <PERSON> returns a MasterInformer.
func (v *version) Masters() MasterInformer {
	return &masterInformer{factory: v.factory, namespace: v.namespace, tweakListOptions: v.tweakListOptions}
}

// MasterSnapshots returns a MasterSnapshotInformer.
func (v *version) MasterSnapshots() MasterSnapshotInformer {
	return &masterSnapshotInformer{factory: v.factory, namespace: v.namespace, tweakListOptions: v.tweakListOptions}
}
