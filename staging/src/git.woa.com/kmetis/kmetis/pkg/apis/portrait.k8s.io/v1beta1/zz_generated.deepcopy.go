// +build !ignore_autogenerated

/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by deepcopy-gen. DO NOT EDIT.

package v1beta1

import (
	autoscalingv1 "k8s.io/api/autoscaling/v1"
	v1 "k8s.io/api/core/v1"
	runtime "k8s.io/apimachinery/pkg/runtime"
)

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ContainerResource) DeepCopyInto(out *ContainerResource) {
	*out = *in
	if in.Request != nil {
		in, out := &in.Request, &out.Request
		*out = make(v1.ResourceList, len(*in))
		for key, val := range *in {
			(*out)[key] = val.DeepCopy()
		}
	}
	if in.Limit != nil {
		in, out := &in.Limit, &out.Limit
		*out = make(v1.ResourceList, len(*in))
		for key, val := range *in {
			(*out)[key] = val.DeepCopy()
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ContainerResource.
func (in *ContainerResource) DeepCopy() *ContainerResource {
	if in == nil {
		return nil
	}
	out := new(ContainerResource)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *ContainerResourcePolicy) DeepCopyInto(out *ContainerResourcePolicy) {
	*out = *in
	if in.Mode != nil {
		in, out := &in.Mode, &out.Mode
		*out = new(ContainerScalingMode)
		**out = **in
	}
	if in.MinAllowed != nil {
		in, out := &in.MinAllowed, &out.MinAllowed
		*out = make(v1.ResourceList, len(*in))
		for key, val := range *in {
			(*out)[key] = val.DeepCopy()
		}
	}
	if in.MaxAllowed != nil {
		in, out := &in.MaxAllowed, &out.MaxAllowed
		*out = make(v1.ResourceList, len(*in))
		for key, val := range *in {
			(*out)[key] = val.DeepCopy()
		}
	}
	if in.ControlledResources != nil {
		in, out := &in.ControlledResources, &out.ControlledResources
		*out = new([]v1.ResourceName)
		if **in != nil {
			in, out := *in, *out
			*out = make([]v1.ResourceName, len(*in))
			copy(*out, *in)
		}
	}
	if in.ControlledValues != nil {
		in, out := &in.ControlledValues, &out.ControlledValues
		*out = new(ContainerControlledValues)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new ContainerResourcePolicy.
func (in *ContainerResourcePolicy) DeepCopy() *ContainerResourcePolicy {
	if in == nil {
		return nil
	}
	out := new(ContainerResourcePolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *HistogramCheckpoint) DeepCopyInto(out *HistogramCheckpoint) {
	*out = *in
	in.ReferenceTimestamp.DeepCopyInto(&out.ReferenceTimestamp)
	if in.BucketWeights != nil {
		in, out := &in.BucketWeights, &out.BucketWeights
		*out = make(map[int]uint32, len(*in))
		for key, val := range *in {
			(*out)[key] = val
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new HistogramCheckpoint.
func (in *HistogramCheckpoint) DeepCopy() *HistogramCheckpoint {
	if in == nil {
		return nil
	}
	out := new(HistogramCheckpoint)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *PodResourcePolicy) DeepCopyInto(out *PodResourcePolicy) {
	*out = *in
	if in.ContainerPolicies != nil {
		in, out := &in.ContainerPolicies, &out.ContainerPolicies
		*out = make([]ContainerResourcePolicy, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new PodResourcePolicy.
func (in *PodResourcePolicy) DeepCopy() *PodResourcePolicy {
	if in == nil {
		return nil
	}
	out := new(PodResourcePolicy)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RecommendedContainerResources) DeepCopyInto(out *RecommendedContainerResources) {
	*out = *in
	in.Target.DeepCopyInto(&out.Target)
	in.LowerBound.DeepCopyInto(&out.LowerBound)
	in.UpperBound.DeepCopyInto(&out.UpperBound)
	if in.WorkloadTypes != nil {
		in, out := &in.WorkloadTypes, &out.WorkloadTypes
		*out = make([]WorkloadType, len(*in))
		copy(*out, *in)
	}
	in.LastScalingResourceTime.DeepCopyInto(&out.LastScalingResourceTime)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RecommendedContainerResources.
func (in *RecommendedContainerResources) DeepCopy() *RecommendedContainerResources {
	if in == nil {
		return nil
	}
	out := new(RecommendedContainerResources)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *RecommendedPodResources) DeepCopyInto(out *RecommendedPodResources) {
	*out = *in
	if in.ContainerRecommendations != nil {
		in, out := &in.ContainerRecommendations, &out.ContainerRecommendations
		*out = make([]RecommendedContainerResources, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new RecommendedPodResources.
func (in *RecommendedPodResources) DeepCopy() *RecommendedPodResources {
	if in == nil {
		return nil
	}
	out := new(RecommendedPodResources)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *Workload) DeepCopyInto(out *Workload) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	in.Spec.DeepCopyInto(&out.Spec)
	in.Status.DeepCopyInto(&out.Status)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new Workload.
func (in *Workload) DeepCopy() *Workload {
	if in == nil {
		return nil
	}
	out := new(Workload)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *Workload) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WorkloadCheckpoint) DeepCopyInto(out *WorkloadCheckpoint) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ObjectMeta.DeepCopyInto(&out.ObjectMeta)
	out.Spec = in.Spec
	in.Status.DeepCopyInto(&out.Status)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WorkloadCheckpoint.
func (in *WorkloadCheckpoint) DeepCopy() *WorkloadCheckpoint {
	if in == nil {
		return nil
	}
	out := new(WorkloadCheckpoint)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *WorkloadCheckpoint) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WorkloadCheckpointList) DeepCopyInto(out *WorkloadCheckpointList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]WorkloadCheckpoint, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WorkloadCheckpointList.
func (in *WorkloadCheckpointList) DeepCopy() *WorkloadCheckpointList {
	if in == nil {
		return nil
	}
	out := new(WorkloadCheckpointList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *WorkloadCheckpointList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WorkloadCheckpointSpec) DeepCopyInto(out *WorkloadCheckpointSpec) {
	*out = *in
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WorkloadCheckpointSpec.
func (in *WorkloadCheckpointSpec) DeepCopy() *WorkloadCheckpointSpec {
	if in == nil {
		return nil
	}
	out := new(WorkloadCheckpointSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WorkloadCheckpointStatus) DeepCopyInto(out *WorkloadCheckpointStatus) {
	*out = *in
	in.LastUpdateTime.DeepCopyInto(&out.LastUpdateTime)
	in.CPUHistogram.DeepCopyInto(&out.CPUHistogram)
	in.MemoryHistogram.DeepCopyInto(&out.MemoryHistogram)
	in.FirstSampleStart.DeepCopyInto(&out.FirstSampleStart)
	in.LastSampleStart.DeepCopyInto(&out.LastSampleStart)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WorkloadCheckpointStatus.
func (in *WorkloadCheckpointStatus) DeepCopy() *WorkloadCheckpointStatus {
	if in == nil {
		return nil
	}
	out := new(WorkloadCheckpointStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WorkloadCondition) DeepCopyInto(out *WorkloadCondition) {
	*out = *in
	in.LastTransitionTime.DeepCopyInto(&out.LastTransitionTime)
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WorkloadCondition.
func (in *WorkloadCondition) DeepCopy() *WorkloadCondition {
	if in == nil {
		return nil
	}
	out := new(WorkloadCondition)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WorkloadList) DeepCopyInto(out *WorkloadList) {
	*out = *in
	out.TypeMeta = in.TypeMeta
	in.ListMeta.DeepCopyInto(&out.ListMeta)
	if in.Items != nil {
		in, out := &in.Items, &out.Items
		*out = make([]Workload, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WorkloadList.
func (in *WorkloadList) DeepCopy() *WorkloadList {
	if in == nil {
		return nil
	}
	out := new(WorkloadList)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyObject is an autogenerated deepcopy function, copying the receiver, creating a new runtime.Object.
func (in *WorkloadList) DeepCopyObject() runtime.Object {
	if c := in.DeepCopy(); c != nil {
		return c
	}
	return nil
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WorkloadSpec) DeepCopyInto(out *WorkloadSpec) {
	*out = *in
	if in.TargetRef != nil {
		in, out := &in.TargetRef, &out.TargetRef
		*out = new(autoscalingv1.CrossVersionObjectReference)
		**out = **in
	}
	if in.ResourcePolicy != nil {
		in, out := &in.ResourcePolicy, &out.ResourcePolicy
		*out = new(PodResourcePolicy)
		(*in).DeepCopyInto(*out)
	}
	if in.Config != nil {
		in, out := &in.Config, &out.Config
		*out = new(WorkloadTypeThresholdConfig)
		(*in).DeepCopyInto(*out)
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WorkloadSpec.
func (in *WorkloadSpec) DeepCopy() *WorkloadSpec {
	if in == nil {
		return nil
	}
	out := new(WorkloadSpec)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WorkloadStatus) DeepCopyInto(out *WorkloadStatus) {
	*out = *in
	if in.Recommendation != nil {
		in, out := &in.Recommendation, &out.Recommendation
		*out = new(RecommendedPodResources)
		(*in).DeepCopyInto(*out)
	}
	if in.Conditions != nil {
		in, out := &in.Conditions, &out.Conditions
		*out = make([]WorkloadCondition, len(*in))
		for i := range *in {
			(*in)[i].DeepCopyInto(&(*out)[i])
		}
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WorkloadStatus.
func (in *WorkloadStatus) DeepCopy() *WorkloadStatus {
	if in == nil {
		return nil
	}
	out := new(WorkloadStatus)
	in.DeepCopyInto(out)
	return out
}

// DeepCopyInto is an autogenerated deepcopy function, copying the receiver, writing into out. in must be non-nil.
func (in *WorkloadTypeThresholdConfig) DeepCopyInto(out *WorkloadTypeThresholdConfig) {
	*out = *in
	if in.CPUAmountThreshold != nil {
		in, out := &in.CPUAmountThreshold, &out.CPUAmountThreshold
		*out = new(int64)
		**out = **in
	}
	if in.CPURatioThreshold != nil {
		in, out := &in.CPURatioThreshold, &out.CPURatioThreshold
		*out = new(float64)
		**out = **in
	}
	if in.MemoryAmountThreshold != nil {
		in, out := &in.MemoryAmountThreshold, &out.MemoryAmountThreshold
		*out = new(int64)
		**out = **in
	}
	if in.MemoryRatioThreshold != nil {
		in, out := &in.MemoryRatioThreshold, &out.MemoryRatioThreshold
		*out = new(float64)
		**out = **in
	}
	if in.FsReadBytesThreshold != nil {
		in, out := &in.FsReadBytesThreshold, &out.FsReadBytesThreshold
		*out = new(float64)
		**out = **in
	}
	if in.FsWriteBytesThreshold != nil {
		in, out := &in.FsWriteBytesThreshold, &out.FsWriteBytesThreshold
		*out = new(float64)
		**out = **in
	}
	if in.NetworkTransmitBytesThreshold != nil {
		in, out := &in.NetworkTransmitBytesThreshold, &out.NetworkTransmitBytesThreshold
		*out = new(float64)
		**out = **in
	}
	if in.NetworkReceiveBytesThreshold != nil {
		in, out := &in.NetworkReceiveBytesThreshold, &out.NetworkReceiveBytesThreshold
		*out = new(float64)
		**out = **in
	}
	if in.HttpRequestLatencyThreshold != nil {
		in, out := &in.HttpRequestLatencyThreshold, &out.HttpRequestLatencyThreshold
		*out = new(float64)
		**out = **in
	}
	if in.HttpQPSThreshold != nil {
		in, out := &in.HttpQPSThreshold, &out.HttpQPSThreshold
		*out = new(float64)
		**out = **in
	}
	if in.StabilizationWindowSeconds != nil {
		in, out := &in.StabilizationWindowSeconds, &out.StabilizationWindowSeconds
		*out = new(int32)
		**out = **in
	}
	return
}

// DeepCopy is an autogenerated deepcopy function, copying the receiver, creating a new WorkloadTypeThresholdConfig.
func (in *WorkloadTypeThresholdConfig) DeepCopy() *WorkloadTypeThresholdConfig {
	if in == nil {
		return nil
	}
	out := new(WorkloadTypeThresholdConfig)
	in.DeepCopyInto(out)
	return out
}
