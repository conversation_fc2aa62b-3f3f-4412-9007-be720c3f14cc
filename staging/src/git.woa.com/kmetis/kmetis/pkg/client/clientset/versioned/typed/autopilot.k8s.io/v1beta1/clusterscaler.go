/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1beta1

import (
	"context"
	"time"

	v1beta1 "git.woa.com/kmetis/kmetis/pkg/apis/autopilot.k8s.io/v1beta1"
	scheme "git.woa.com/kmetis/kmetis/pkg/client/clientset/versioned/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// ClusterScalersGetter has a method to return a ClusterScalerInterface.
// A group's client should implement this interface.
type ClusterScalersGetter interface {
	ClusterScalers(namespace string) ClusterScalerInterface
}

// ClusterScalerInterface has methods to work with ClusterScaler resources.
type ClusterScalerInterface interface {
	Create(*v1beta1.ClusterScaler) (*v1beta1.ClusterScaler, error)
	Update(*v1beta1.ClusterScaler) (*v1beta1.ClusterScaler, error)
	UpdateStatus(*v1beta1.ClusterScaler) (*v1beta1.ClusterScaler, error)
	Delete(name string, options *v1.DeleteOptions) error
	DeleteCollection(options *v1.DeleteOptions, listOptions v1.ListOptions) error
	Get(name string, options v1.GetOptions) (*v1beta1.ClusterScaler, error)
	List(opts v1.ListOptions) (*v1beta1.ClusterScalerList, error)
	Watch(opts v1.ListOptions) (watch.Interface, error)
	Patch(name string, pt types.PatchType, data []byte, subresources ...string) (result *v1beta1.ClusterScaler, err error)
	ClusterScalerExpansion
}

// clusterScalers implements ClusterScalerInterface
type clusterScalers struct {
	client rest.Interface
	ns     string
}

// newClusterScalers returns a ClusterScalers
func newClusterScalers(c *AutopilotV1beta1Client, namespace string) *clusterScalers {
	return &clusterScalers{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the clusterScaler, and returns the corresponding clusterScaler object, and an error if there is any.
func (c *clusterScalers) Get(name string, options v1.GetOptions) (result *v1beta1.ClusterScaler, err error) {
	result = &v1beta1.ClusterScaler{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("clusterscalers").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(context.TODO()).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of ClusterScalers that match those selectors.
func (c *clusterScalers) List(opts v1.ListOptions) (result *v1beta1.ClusterScalerList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1beta1.ClusterScalerList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("clusterscalers").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(context.TODO()).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested clusterScalers.
func (c *clusterScalers) Watch(opts v1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("clusterscalers").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(context.TODO())
}

// Create takes the representation of a clusterScaler and creates it.  Returns the server's representation of the clusterScaler, and an error, if there is any.
func (c *clusterScalers) Create(clusterScaler *v1beta1.ClusterScaler) (result *v1beta1.ClusterScaler, err error) {
	result = &v1beta1.ClusterScaler{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("clusterscalers").
		Body(clusterScaler).
		Do(context.TODO()).
		Into(result)
	return
}

// Update takes the representation of a clusterScaler and updates it. Returns the server's representation of the clusterScaler, and an error, if there is any.
func (c *clusterScalers) Update(clusterScaler *v1beta1.ClusterScaler) (result *v1beta1.ClusterScaler, err error) {
	result = &v1beta1.ClusterScaler{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("clusterscalers").
		Name(clusterScaler.Name).
		Body(clusterScaler).
		Do(context.TODO()).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().

func (c *clusterScalers) UpdateStatus(clusterScaler *v1beta1.ClusterScaler) (result *v1beta1.ClusterScaler, err error) {
	result = &v1beta1.ClusterScaler{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("clusterscalers").
		Name(clusterScaler.Name).
		SubResource("status").
		Body(clusterScaler).
		Do(context.TODO()).
		Into(result)
	return
}

// Delete takes name of the clusterScaler and deletes it. Returns an error if one occurs.
func (c *clusterScalers) Delete(name string, options *v1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("clusterscalers").
		Name(name).
		Body(options).
		Do(context.TODO()).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *clusterScalers) DeleteCollection(options *v1.DeleteOptions, listOptions v1.ListOptions) error {
	var timeout time.Duration
	if listOptions.TimeoutSeconds != nil {
		timeout = time.Duration(*listOptions.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Namespace(c.ns).
		Resource("clusterscalers").
		VersionedParams(&listOptions, scheme.ParameterCodec).
		Timeout(timeout).
		Body(options).
		Do(context.TODO()).
		Error()
}

// Patch applies the patch and returns the patched clusterScaler.
func (c *clusterScalers) Patch(name string, pt types.PatchType, data []byte, subresources ...string) (result *v1beta1.ClusterScaler, err error) {
	result = &v1beta1.ClusterScaler{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("clusterscalers").
		SubResource(subresources...).
		Name(name).
		Body(data).
		Do(context.TODO()).
		Into(result)
	return
}
