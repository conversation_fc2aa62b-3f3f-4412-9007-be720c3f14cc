/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1beta1 "git.woa.com/kmetis/kmetis/pkg/apis/autopilot.k8s.io/v1beta1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeAutopilotClusters implements AutopilotClusterInterface
type FakeAutopilotClusters struct {
	Fake *FakeAutopilotV1beta1
	ns   string
}

var autopilotclustersResource = schema.GroupVersionResource{Group: "autopilot.k8s.io", Version: "v1beta1", Resource: "autopilotclusters"}

var autopilotclustersKind = schema.GroupVersionKind{Group: "autopilot.k8s.io", Version: "v1beta1", Kind: "ClusterScaler"}

// Get takes name of the autopilotCluster, and returns the corresponding autopilotCluster object, and an error if there is any.
func (c *FakeAutopilotClusters) Get(name string, options v1.GetOptions) (result *v1beta1.ClusterScaler, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewGetAction(autopilotclustersResource, c.ns, name), &v1beta1.ClusterScaler{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.ClusterScaler), err
}

// List takes label and field selectors, and returns the list of AutopilotClusters that match those selectors.
func (c *FakeAutopilotClusters) List(opts v1.ListOptions) (result *v1beta1.ClusterScalerList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewListAction(autopilotclustersResource, autopilotclustersKind, c.ns, opts), &v1beta1.ClusterScalerList{})

	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &v1beta1.ClusterScalerList{ListMeta: obj.(*v1beta1.ClusterScalerList).ListMeta}
	for _, item := range obj.(*v1beta1.ClusterScalerList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested autopilotClusters.
func (c *FakeAutopilotClusters) Watch(opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewWatchAction(autopilotclustersResource, c.ns, opts))

}

// Create takes the representation of a autopilotCluster and creates it.  Returns the server's representation of the autopilotCluster, and an error, if there is any.
func (c *FakeAutopilotClusters) Create(autopilotCluster *v1beta1.ClusterScaler) (result *v1beta1.ClusterScaler, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewCreateAction(autopilotclustersResource, c.ns, autopilotCluster), &v1beta1.ClusterScaler{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.ClusterScaler), err
}

// Update takes the representation of a autopilotCluster and updates it. Returns the server's representation of the autopilotCluster, and an error, if there is any.
func (c *FakeAutopilotClusters) Update(autopilotCluster *v1beta1.ClusterScaler) (result *v1beta1.ClusterScaler, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateAction(autopilotclustersResource, c.ns, autopilotCluster), &v1beta1.ClusterScaler{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.ClusterScaler), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeAutopilotClusters) UpdateStatus(autopilotCluster *v1beta1.ClusterScaler) (*v1beta1.ClusterScaler, error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateSubresourceAction(autopilotclustersResource, "status", c.ns, autopilotCluster), &v1beta1.ClusterScaler{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.ClusterScaler), err
}

// Delete takes name of the autopilotCluster and deletes it. Returns an error if one occurs.
func (c *FakeAutopilotClusters) Delete(name string, options *v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewDeleteAction(autopilotclustersResource, c.ns, name), &v1beta1.ClusterScaler{})

	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeAutopilotClusters) DeleteCollection(options *v1.DeleteOptions, listOptions v1.ListOptions) error {
	action := testing.NewDeleteCollectionAction(autopilotclustersResource, c.ns, listOptions)

	_, err := c.Fake.Invokes(action, &v1beta1.ClusterScalerList{})
	return err
}

// Patch applies the patch and returns the patched autopilotCluster.
func (c *FakeAutopilotClusters) Patch(name string, pt types.PatchType, data []byte, subresources ...string) (result *v1beta1.ClusterScaler, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceAction(autopilotclustersResource, c.ns, name, pt, data, subresources...), &v1beta1.ClusterScaler{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.ClusterScaler), err
}
