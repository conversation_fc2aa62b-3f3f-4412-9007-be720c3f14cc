/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package fake

import (
	v1beta1 "git.woa.com/kmetis/kmetis/pkg/apis/autopilot.k8s.io/v1beta1"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	labels "k8s.io/apimachinery/pkg/labels"
	schema "k8s.io/apimachinery/pkg/runtime/schema"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	testing "k8s.io/client-go/testing"
)

// FakeScalerRules implements ScalerRuleInterface
type FakeScalerRules struct {
	Fake *FakeAutopilotV1beta1
	ns   string
}

var scalerrulesResource = schema.GroupVersionResource{Group: "autopilot.k8s.io", Version: "v1beta1", Resource: "scalerrules"}

var scalerrulesKind = schema.GroupVersionKind{Group: "autopilot.k8s.io", Version: "v1beta1", Kind: "ScalerTask"}

// Get takes name of the scalerRule, and returns the corresponding scalerRule object, and an error if there is any.
func (c *FakeScalerRules) Get(name string, options v1.GetOptions) (result *v1beta1.ScalerTask, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewGetAction(scalerrulesResource, c.ns, name), &v1beta1.ScalerTask{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.ScalerTask), err
}

// List takes label and field selectors, and returns the list of ScalerRules that match those selectors.
func (c *FakeScalerRules) List(opts v1.ListOptions) (result *v1beta1.ScalerTaskList, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewListAction(scalerrulesResource, scalerrulesKind, c.ns, opts), &v1beta1.ScalerTaskList{})

	if obj == nil {
		return nil, err
	}

	label, _, _ := testing.ExtractFromListOptions(opts)
	if label == nil {
		label = labels.Everything()
	}
	list := &v1beta1.ScalerTaskList{ListMeta: obj.(*v1beta1.ScalerTaskList).ListMeta}
	for _, item := range obj.(*v1beta1.ScalerTaskList).Items {
		if label.Matches(labels.Set(item.Labels)) {
			list.Items = append(list.Items, item)
		}
	}
	return list, err
}

// Watch returns a watch.Interface that watches the requested scalerRules.
func (c *FakeScalerRules) Watch(opts v1.ListOptions) (watch.Interface, error) {
	return c.Fake.
		InvokesWatch(testing.NewWatchAction(scalerrulesResource, c.ns, opts))

}

// Create takes the representation of a scalerRule and creates it.  Returns the server's representation of the scalerRule, and an error, if there is any.
func (c *FakeScalerRules) Create(scalerRule *v1beta1.ScalerTask) (result *v1beta1.ScalerTask, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewCreateAction(scalerrulesResource, c.ns, scalerRule), &v1beta1.ScalerTask{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.ScalerTask), err
}

// Update takes the representation of a scalerRule and updates it. Returns the server's representation of the scalerRule, and an error, if there is any.
func (c *FakeScalerRules) Update(scalerRule *v1beta1.ScalerTask) (result *v1beta1.ScalerTask, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateAction(scalerrulesResource, c.ns, scalerRule), &v1beta1.ScalerTask{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.ScalerTask), err
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().
func (c *FakeScalerRules) UpdateStatus(scalerRule *v1beta1.ScalerTask) (*v1beta1.ScalerTask, error) {
	obj, err := c.Fake.
		Invokes(testing.NewUpdateSubresourceAction(scalerrulesResource, "status", c.ns, scalerRule), &v1beta1.ScalerTask{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.ScalerTask), err
}

// Delete takes name of the scalerRule and deletes it. Returns an error if one occurs.
func (c *FakeScalerRules) Delete(name string, options *v1.DeleteOptions) error {
	_, err := c.Fake.
		Invokes(testing.NewDeleteAction(scalerrulesResource, c.ns, name), &v1beta1.ScalerTask{})

	return err
}

// DeleteCollection deletes a collection of objects.
func (c *FakeScalerRules) DeleteCollection(options *v1.DeleteOptions, listOptions v1.ListOptions) error {
	action := testing.NewDeleteCollectionAction(scalerrulesResource, c.ns, listOptions)

	_, err := c.Fake.Invokes(action, &v1beta1.ScalerTaskList{})
	return err
}

// Patch applies the patch and returns the patched scalerRule.
func (c *FakeScalerRules) Patch(name string, pt types.PatchType, data []byte, subresources ...string) (result *v1beta1.ScalerTask, err error) {
	obj, err := c.Fake.
		Invokes(testing.NewPatchSubresourceAction(scalerrulesResource, c.ns, name, pt, data, subresources...), &v1beta1.ScalerTask{})

	if obj == nil {
		return nil, err
	}
	return obj.(*v1beta1.ScalerTask), err
}
