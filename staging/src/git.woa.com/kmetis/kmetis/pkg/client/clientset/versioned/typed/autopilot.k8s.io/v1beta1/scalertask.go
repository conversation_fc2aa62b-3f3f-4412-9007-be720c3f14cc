/*
Copyright The Kubernetes Authors.

Licensed under the Apache License, Version 2.0 (the "License");
you may not use this file except in compliance with the License.
You may obtain a copy of the License at

    http://www.apache.org/licenses/LICENSE-2.0

Unless required by applicable law or agreed to in writing, software
distributed under the License is distributed on an "AS IS" BASIS,
WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
See the License for the specific language governing permissions and
limitations under the License.
*/

// Code generated by client-gen. DO NOT EDIT.

package v1beta1

import (
	"context"
	"time"

	v1beta1 "git.woa.com/kmetis/kmetis/pkg/apis/autopilot.k8s.io/v1beta1"
	scheme "git.woa.com/kmetis/kmetis/pkg/client/clientset/versioned/scheme"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	types "k8s.io/apimachinery/pkg/types"
	watch "k8s.io/apimachinery/pkg/watch"
	rest "k8s.io/client-go/rest"
)

// ScalerTasksGetter has a method to return a ScalerTaskInterface.
// A group's client should implement this interface.
type ScalerTasksGetter interface {
	ScalerTasks(namespace string) ScalerTaskInterface
}

// ScalerTaskInterface has methods to work with ScalerTask resources.
type ScalerTaskInterface interface {
	Create(*v1beta1.ScalerTask) (*v1beta1.ScalerTask, error)
	Update(*v1beta1.ScalerTask) (*v1beta1.ScalerTask, error)
	UpdateStatus(*v1beta1.ScalerTask) (*v1beta1.ScalerTask, error)
	Delete(name string, options *v1.DeleteOptions) error
	DeleteCollection(options *v1.DeleteOptions, listOptions v1.ListOptions) error
	Get(name string, options v1.GetOptions) (*v1beta1.ScalerTask, error)
	List(opts v1.ListOptions) (*v1beta1.ScalerTaskList, error)
	Watch(opts v1.ListOptions) (watch.Interface, error)
	Patch(name string, pt types.PatchType, data []byte, subresources ...string) (result *v1beta1.ScalerTask, err error)
	ScalerTaskExpansion
}

// scalerTasks implements ScalerTaskInterface
type scalerTasks struct {
	client rest.Interface
	ns     string
}

// newScalerTasks returns a ScalerTasks
func newScalerTasks(c *AutopilotV1beta1Client, namespace string) *scalerTasks {
	return &scalerTasks{
		client: c.RESTClient(),
		ns:     namespace,
	}
}

// Get takes name of the scalerTask, and returns the corresponding scalerTask object, and an error if there is any.
func (c *scalerTasks) Get(name string, options v1.GetOptions) (result *v1beta1.ScalerTask, err error) {
	result = &v1beta1.ScalerTask{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("scalertasks").
		Name(name).
		VersionedParams(&options, scheme.ParameterCodec).
		Do(context.TODO()).
		Into(result)
	return
}

// List takes label and field selectors, and returns the list of ScalerTasks that match those selectors.
func (c *scalerTasks) List(opts v1.ListOptions) (result *v1beta1.ScalerTaskList, err error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	result = &v1beta1.ScalerTaskList{}
	err = c.client.Get().
		Namespace(c.ns).
		Resource("scalertasks").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Do(context.TODO()).
		Into(result)
	return
}

// Watch returns a watch.Interface that watches the requested scalerTasks.
func (c *scalerTasks) Watch(opts v1.ListOptions) (watch.Interface, error) {
	var timeout time.Duration
	if opts.TimeoutSeconds != nil {
		timeout = time.Duration(*opts.TimeoutSeconds) * time.Second
	}
	opts.Watch = true
	return c.client.Get().
		Namespace(c.ns).
		Resource("scalertasks").
		VersionedParams(&opts, scheme.ParameterCodec).
		Timeout(timeout).
		Watch(context.TODO())
}

// Create takes the representation of a scalerTask and creates it.  Returns the server's representation of the scalerTask, and an error, if there is any.
func (c *scalerTasks) Create(scalerTask *v1beta1.ScalerTask) (result *v1beta1.ScalerTask, err error) {
	result = &v1beta1.ScalerTask{}
	err = c.client.Post().
		Namespace(c.ns).
		Resource("scalertasks").
		Body(scalerTask).
		Do(context.TODO()).
		Into(result)
	return
}

// Update takes the representation of a scalerTask and updates it. Returns the server's representation of the scalerTask, and an error, if there is any.
func (c *scalerTasks) Update(scalerTask *v1beta1.ScalerTask) (result *v1beta1.ScalerTask, err error) {
	result = &v1beta1.ScalerTask{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("scalertasks").
		Name(scalerTask.Name).
		Body(scalerTask).
		Do(context.TODO()).
		Into(result)
	return
}

// UpdateStatus was generated because the type contains a Status member.
// Add a +genclient:noStatus comment above the type to avoid generating UpdateStatus().

func (c *scalerTasks) UpdateStatus(scalerTask *v1beta1.ScalerTask) (result *v1beta1.ScalerTask, err error) {
	result = &v1beta1.ScalerTask{}
	err = c.client.Put().
		Namespace(c.ns).
		Resource("scalertasks").
		Name(scalerTask.Name).
		SubResource("status").
		Body(scalerTask).
		Do(context.TODO()).
		Into(result)
	return
}

// Delete takes name of the scalerTask and deletes it. Returns an error if one occurs.
func (c *scalerTasks) Delete(name string, options *v1.DeleteOptions) error {
	return c.client.Delete().
		Namespace(c.ns).
		Resource("scalertasks").
		Name(name).
		Body(options).
		Do(context.TODO()).
		Error()
}

// DeleteCollection deletes a collection of objects.
func (c *scalerTasks) DeleteCollection(options *v1.DeleteOptions, listOptions v1.ListOptions) error {
	var timeout time.Duration
	if listOptions.TimeoutSeconds != nil {
		timeout = time.Duration(*listOptions.TimeoutSeconds) * time.Second
	}
	return c.client.Delete().
		Namespace(c.ns).
		Resource("scalertasks").
		VersionedParams(&listOptions, scheme.ParameterCodec).
		Timeout(timeout).
		Body(options).
		Do(context.TODO()).
		Error()
}

// Patch applies the patch and returns the patched scalerTask.
func (c *scalerTasks) Patch(name string, pt types.PatchType, data []byte, subresources ...string) (result *v1beta1.ScalerTask, err error) {
	result = &v1beta1.ScalerTask{}
	err = c.client.Patch(pt).
		Namespace(c.ns).
		Resource("scalertasks").
		SubResource(subresources...).
		Name(name).
		Body(data).
		Do(context.TODO()).
		Into(result)
	return
}
