package main

import (
	"fmt"
	"git.woa.com/kmetis/starship/pkg/healthcheckrule"
	"git.woa.com/kmetis/starship/pkg/model"
	rainutil "git.woa.com/kmetis/starship/pkg/task/util"
	pkgutil "git.woa.com/kmetis/starship/pkg/util"

	"sigs.k8s.io/yaml"

	"git.woa.com/kmetis/starship/util"
)

func main() {
	appId := "4d127961-5bc8-4015-b0b7-55c194936ef2"
	group := "starship-platform"
	env := "Test"
	userId := "4a01b76e41b243e2b334f1fb7f5ef985"
	userKey := "4e0daa93eed146c149534032fe6486209e84"
	region := "qy"

	if err := util.InitRainbow(userId, userKey, region, appId, group, env); err != nil {
		fmt.Printf("InitRainbow failed: %v", err)
	}

	//tags := util.GetRainbowData("components/controller-manager/healthchecks/rules/allowedImageTags")
	//var values map[string]interface{}
	//if err := yaml.Unmarshal([]byte(tags), &values); err != nil {
	//	fmt.Println("Unmarshal failed: %v", err)
	//}
	//
	//hcr := model.HealthCheckRule{
	//	Name:  "allowedImageTags",
	//	Value: values,
	//}
	//fmt.Printf("HealthCheckRule: %v", hcr)

	globalRules := &model.HealthCheckRules{}
	var err error
	config := util.GetRainbowData("global/healthchecks/config")
	var globalHealthCheckBaseConfig healthcheckrule.GlobalHealthCheckBaseConfig
	if err := yaml.Unmarshal([]byte(config), &globalHealthCheckBaseConfig); err != nil {
		panic(err)
	}
	preCheckRules, err := getHealthCheckRules(pkgutil.GlobalHealthCheckComponent, globalHealthCheckBaseConfig.DefaultPreCheckPlugins)
	if err != nil {
		panic(err)
	}
	postCheckRules, err := getHealthCheckRules(pkgutil.GlobalHealthCheckComponent, globalHealthCheckBaseConfig.DefaultPostCheckPlugins)
	if err != nil {
		panic(err)
	}
	globalRules.PreCheckRules = preCheckRules
	globalRules.PostCheckRules = postCheckRules

	healthCheckRules, err := GetComponentHealthCheckRules("eklet")
	fmt.Println(healthCheckRules.PreCheckRules[0])
	rule := healthCheckRules.PreCheckRules[0]
	array, err := rule.GetStringArray("eklet.forbidden")
	fmt.Println(array)
	fmt.Println(globalRules)

	componentsConfigContent := util.GetRainbowData(fmt.Sprintf("components/%s/config", "eklet"))
	componentsConfig := make(map[string]interface{})
	if err := yaml.Unmarshal([]byte(componentsConfigContent), &componentsConfig); err != nil {
		panic(fmt.Sprintf("unmarshal components config from %s failed: %v", componentsConfigContent, err))
	}
	fmt.Println("--->", componentsConfig)
	//fmt.Println(componentsConfig["test"].(string))
	//value := getComponentDataStr(componentsConfig, "test")
	//fmt.Println(value)

	toMap, err := rainutil.ConvertToMap(componentsConfig, "scheduler")
	if err != nil {
		panic(err)
	}
	fmt.Println("---")
	fmt.Println(toMap["tke"])

	postCheckCount := rainutil.GetComponentDataInt(componentsConfig, "postCheckExecutionCount")
	if postCheckCount == 0 {
		postCheckCount = 1
	} else if postCheckCount > 3 {
		postCheckCount = 3
	}

	// 获取后检延迟时间
	postCheckDelay := rainutil.GetComponentDataInt(componentsConfig, "upgradePostCheckDelay")
	if postCheckDelay > 600 {
		postCheckDelay = 600
	}
	fmt.Println(postCheckDelay)
	fmt.Println(postCheckCount)
}

func getComponentDataStr(configMap map[string]interface{}, key string) string {
	if configMapData, ok := configMap[key]; ok {
		return configMapData.(string)
	}
	return ""
}

func getHealthCheckRules(component string, ruleNames []string) ([]model.HealthCheckRule, error) {
	rules := make([]model.HealthCheckRule, 0)
	for i := 0; i < len(ruleNames); i++ {
		ruleName := ruleNames[i]
		rule, err := getHealthCheckRule(component, ruleName)
		if err != nil {
			return nil, err
		}
		rules = append(rules, rule)
	}
	return rules, nil
}

func getHealthCheckRule(component, ruleName string) (model.HealthCheckRule, error) {
	var values map[string]interface{}
	var rulesContent string
	if component != pkgutil.GlobalHealthCheckComponent {
		rulesContent = util.GetRainbowData(fmt.Sprintf("components/%s/healthchecks/rules/%s", component, ruleName))
	} else {
		rulesContent = util.GetRainbowData(fmt.Sprintf("global/healthchecks/rules/%s", ruleName))
	}
	if rulesContent != "" {
		if err := yaml.Unmarshal([]byte(rulesContent), &values); err != nil {
			return model.HealthCheckRule{}, err
		}
	}
	return model.HealthCheckRule{
		Name:  ruleName,
		Value: values,
	}, nil
}

func GetComponentHealthCheckRules(component string) (*model.ComponentHealthCheckRules, error) {
	componentRules := &model.ComponentHealthCheckRules{}
	var err error
	config := util.GetRainbowData(fmt.Sprintf("components/%s/healthchecks/config", component))
	var componentHealthCheckBaseConfig healthcheckrule.ComponentHealthCheckBaseConfig
	if err := yaml.Unmarshal([]byte(config), &componentHealthCheckBaseConfig); err != nil {
		return nil, err
	}
	preCheckRules, err := getHealthCheckRules(component, componentHealthCheckBaseConfig.AdditionalPreCheckPlugins)
	if err != nil {
		return nil, err
	}
	postCheckRules, err := getHealthCheckRules(component, componentHealthCheckBaseConfig.AdditionalPostCheckPlugins)
	if err != nil {
		return nil, err
	}
	componentRules.PreCheckRules = preCheckRules
	componentRules.PostCheckRules = postCheckRules
	componentRules.ExcludePreCheckPlugins = componentHealthCheckBaseConfig.ExcludeDefaultPreCheckPlugins
	componentRules.ExcludePostCheckPlugins = componentHealthCheckBaseConfig.ExcludeDefaultPostCheckPlugins

	return componentRules, nil
}
