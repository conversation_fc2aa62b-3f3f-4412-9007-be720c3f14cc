package main

import (
	"context"
	"fmt"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/tools/clientcmd/api"
	"k8s.io/klog/v2"
	"os"
	"tkestack.io/tke/api/client/clientset/versioned"
)

const (
	contextName = "tke-starship"

	url = "https://%s.tke.caas.tencentyun.com:9443"
	ca  = "/ca.crt"
	key = "/key.crt"
)

func main() {
	if len(os.Args) < 3 {
		fmt.Println("Usage: ./program <region> <clusterid>")
		os.Exit(1)
	}
	region := os.Args[1]
	clusterId := os.Args[2]

	url := fmt.Sprintf(url, region)

	// 创建客户端并传入clusterid上下文
	client, err := NewTkeClient(url)
	if err != nil {
		klog.Fatalf("Failed to create TKE client: %v", err)
	}

	cluster, err := client.PlatformV1().Clusters().Get(context.TODO(), clusterId, metav1.GetOptions{})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(fmt.Sprintf("cluster cr: %+v", cluster))
}

func NewTkeClient(url string) (*versioned.Clientset, error) {
	config := api.NewConfig()
	config.CurrentContext = contextName
	config.Clusters[contextName] = &api.Cluster{
		Server:                url,
		InsecureSkipTLSVerify: true,
	}
	config.AuthInfos[contextName] = &api.AuthInfo{
		ClientCertificate: ca,
		ClientKey:         key,
	}
	config.Contexts[contextName] = &api.Context{
		Cluster:  contextName,
		AuthInfo: contextName,
	}

	clientConfig := clientcmd.NewNonInteractiveClientConfig(*config, contextName, &clientcmd.ConfigOverrides{Timeout: "5s"}, nil)
	restConfig, err := clientConfig.ClientConfig()
	if err != nil {
		return nil, fmt.Errorf("build versioned TKE client config error:%v", err)
	}
	restConfig.QPS = 50
	restConfig.Burst = 100
	klog.Infof("NewTKE platform restConfig qps = %d, burst = %d ", restConfig.QPS, restConfig.Burst)
	client, err := versioned.NewForConfig(restConfig)
	if err != nil {
		return nil, fmt.Errorf("build versioned TKE client error:%v", err)
	}
	return client, nil
}
