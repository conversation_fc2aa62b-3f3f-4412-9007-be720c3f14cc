package util

import (
	"encoding/json"
	"fmt"
	"sync"

	"git.code.oa.com/rainbow/golang-sdk/types"
	"git.code.oa.com/rainbow/golang-sdk/v3/confapi"
	"git.code.oa.com/rainbow/golang-sdk/v3/watch"
	v3 "git.code.oa.com/rainbow/proto/api/configv3"
)

type rainbowClient struct {
	data map[string]string
	lock sync.RWMutex
}

func GetRainbowData(key string) string {
	rbData.lock.RLock()
	defer rbData.lock.RUnlock()

	return rbData.data[key]
}

func SetRainbowData(key, value string) {
	rbData.lock.Lock()
	defer rbData.lock.Unlock()

	rbData.data[key] = value
}

var rbData = rainbowClient{
	data: make(map[string]string),
}

func callback(oldVal watch.Result, newVal []*v3.Item) error {
	oldData, _ := json.Marshal(oldVal)
	newData, _ := json.Marshal(newVal)
	fmt.Printf("rainbow watch old value: %s \n", oldData)
	fmt.Printf("rainbow watch new value: %s \n", newData)

	files, err := rainbow.GetFileData(getOpts...)
	if err != nil {
		fmt.Printf("GetFileData failed: %v \n", err)
		return err
	}

	for _, file := range files {
		fmt.Printf("SetRainbowData: %v %v \n", file.Name, file.Content)
		SetRainbowData(file.Name, file.Content)
	}
	return nil
}

var rainbow *confapi.ConfAPIV2
var getOpts []types.AssignGetOption
var once sync.Once

func InitRainbow(userId, userKey, region, appId, group, env string) error {
	var err error
	once.Do(
		func() {
			rainbow, err = confapi.NewAgainV2(types.CarryClientInfoV2("region", region))
			if err != nil {
				return
			}

			getOpts = append(getOpts, types.WithAppID(appId))
			getOpts = append(getOpts, types.WithGroup(group))
			getOpts = append(getOpts, types.WithEnvName(env))
			getOpts = append(getOpts, types.WithHmacWay("sha1"))
			getOpts = append(getOpts, types.WithUserID(userId))
			getOpts = append(getOpts, types.WithUserKey(userKey))

			err = rainbow.PreloadGroups([][]types.AssignGetOption{getOpts}...)
			if err != nil {
				return
			}

			files, err1 := rainbow.GetFileData(getOpts...)
			if err != nil {
				err = err1
				return
			}

			// init rainbow data
			for _, file := range files {
				fmt.Printf("SetRainbowData: %v %v \n", file.Name, file.Content)
				SetRainbowData(file.Name, file.Content)
			}

			err = rainbow.AddWatcher(watch.Watcher{CB: callback}, getOpts...)
		})

	return err
}
